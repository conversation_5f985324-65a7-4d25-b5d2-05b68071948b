#!/usr/bin/env python3
"""
SYSTEM INTEGRATION - MAIN INTEGRATION FILE

ROLE: Integration between all components
FUNCTIONS:
- Connect realtime_data_fetcher.py with DB1 and DB2
- Provide APIs for flask_app.py
- Handle database schema setup
- Coordinate complete system flow

INTEGRATION POINTS:
- realtime_data_fetcher.py calls trigger_complete_system_cycle()
- flask_app.py calls get_system_status_for_frontend()
"""

import os
import sqlite3
import logging
from datetime import datetime
from typing import Dict, List
from db1_engine import db1_engine
from db2_engine import db2_engine

class SystemIntegration:
    """Main system integration coordinator"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.setup_database_schema()
        self.logger.info("🚀 SYSTEM INTEGRATION INITIALIZED")
    
    def setup_database_schema(self):
        """Setup database schema for both DB1 and DB2"""
        try:
            # Ensure Data directory exists
            os.makedirs('Data', exist_ok=True)
            
            # Setup DB1 schema
            conn1 = sqlite3.connect('Data/trading_data.db', timeout=30.0)
            cursor1 = conn1.cursor()
            
            cursor1.execute('''
            CREATE TABLE IF NOT EXISTS trading_signals (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                signal_type TEXT NOT NULL DEFAULT 'BUY',
                price REAL NOT NULL,
                pattern_sequence TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            cursor1.execute('CREATE INDEX IF NOT EXISTS idx_trading_signals_symbol ON trading_signals(symbol)')
            conn1.commit()
            conn1.close()
            
            # Setup DB2 schema
            conn2 = sqlite3.connect('Data/trading_operations.db', timeout=30.0)
            cursor2 = conn2.cursor()
            
            cursor2.execute('''
            CREATE TABLE IF NOT EXISTS db2_signals_received (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                signal_type TEXT NOT NULL DEFAULT 'BUY',
                signal_price REAL NOT NULL,
                status TEXT DEFAULT 'PENDING',
                received_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                db1_signal_id INTEGER NOT NULL
            )
            ''')
            
            cursor2.execute('''
            CREATE TABLE IF NOT EXISTS trading_positions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                buy_price REAL NOT NULL,
                sell_price REAL,
                shares_quantity INTEGER NOT NULL,
                investment REAL NOT NULL,
                sell_value REAL,
                current_profit REAL DEFAULT 0.0,
                final_profit REAL,
                status TEXT DEFAULT 'ACTIVE',
                profit_800_base_point REAL,
                profit_800_timestamp DATETIME,
                buy_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                sell_timestamp DATETIME,
                db1_signal_id INTEGER NOT NULL
            )
            ''')
            
            cursor2.execute('CREATE INDEX IF NOT EXISTS idx_db2_signals_symbol ON db2_signals_received(symbol)')
            cursor2.execute('CREATE INDEX IF NOT EXISTS idx_trading_positions_symbol ON trading_positions(symbol)')
            conn2.commit()
            conn2.close()
            
            self.logger.info("✅ Database schema setup complete")
            
        except Exception as e:
            self.logger.error(f"❌ Error setting up database schema: {e}")
    
    def trigger_complete_system_cycle(self) -> Dict:
        """
        MAIN INTEGRATION POINT: Called by realtime_data_fetcher.py every 15 minutes
        Triggers complete DB1 → DB2 cycle
        """
        try:
            self.logger.info("🔄 TRIGGERING COMPLETE SYSTEM CYCLE")
            
            # Step 1: Run DB1 cycle
            db1_result = db1_engine.run_complete_db1_cycle()
            
            # Step 2: Run DB2 cycle
            db2_result = db2_engine.run_complete_db2_cycle()
            
            result = {
                'db1_result': db1_result,
                'db2_result': db2_result,
                'system_cycle_time': datetime.now().isoformat()
            }
            
            self.logger.info("✅ COMPLETE SYSTEM CYCLE FINISHED")
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Error in complete system cycle: {e}")
            return {'error': str(e)}
    
    def get_system_status_for_frontend(self) -> Dict:
        """
        INTEGRATION POINT: Called by flask_app.py for frontend display
        Returns complete system status
        """
        try:
            # Get DB1 statistics
            db1_stats = self.get_db1_statistics()
            
            # Get DB2 statistics
            db2_stats = self.get_db2_statistics()
            
            # Get priority queue
            priority_queue = db1_engine.get_priority_queue_status()
            
            return {
                'db1_statistics': db1_stats,
                'db2_statistics': db2_stats,
                'priority_queue': priority_queue,
                'system_time': datetime.now().isoformat(),
                'integration_status': 'ACTIVE'
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error getting system status: {e}")
            return {'error': str(e)}
    
    def get_db1_statistics(self) -> Dict:
        """Get DB1 statistics for frontend"""
        try:
            conn = sqlite3.connect('Data/trading_data.db', timeout=30.0)
            cursor = conn.cursor()
            
            # Total signals
            cursor.execute('SELECT COUNT(*) FROM trading_signals')
            total_signals = cursor.fetchone()[0]
            
            # Recent signals
            cursor.execute('''
            SELECT COUNT(*) FROM trading_signals 
            WHERE datetime(created_at) > datetime('now', '-1 day')
            ''')
            recent_signals = cursor.fetchone()[0]
            
            # Signals by symbol
            cursor.execute('''
            SELECT symbol, COUNT(*) as count FROM trading_signals 
            GROUP BY symbol ORDER BY count DESC LIMIT 10
            ''')
            signals_by_symbol = dict(cursor.fetchall())
            
            conn.close()
            
            return {
                'total_signals': total_signals,
                'recent_signals': recent_signals,
                'signals_by_symbol': signals_by_symbol
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error getting DB1 statistics: {e}")
            return {}
    
    def get_db2_statistics(self) -> Dict:
        """Get DB2 statistics for frontend"""
        try:
            conn = sqlite3.connect('Data/trading_operations.db', timeout=30.0)
            cursor = conn.cursor()
            
            # Signals received
            cursor.execute('SELECT COUNT(*) FROM db2_signals_received')
            signals_received = cursor.fetchone()[0]
            
            # Pending signals
            cursor.execute('SELECT COUNT(*) FROM db2_signals_received WHERE status = "PENDING"')
            pending_signals = cursor.fetchone()[0]
            
            # Active positions
            cursor.execute('SELECT COUNT(*) FROM trading_positions WHERE status = "ACTIVE"')
            active_positions = cursor.fetchone()[0]
            
            # Completed positions
            cursor.execute('SELECT COUNT(*) FROM trading_positions WHERE status = "COMPLETED"')
            completed_positions = cursor.fetchone()[0]
            
            # Total profit
            cursor.execute('SELECT SUM(final_profit) FROM trading_positions WHERE status = "COMPLETED"')
            total_profit = cursor.fetchone()[0] or 0.0
            
            conn.close()
            
            return {
                'signals_received': signals_received,
                'pending_signals': pending_signals,
                'active_positions': active_positions,
                'completed_positions': completed_positions,
                'total_profit': total_profit
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error getting DB2 statistics: {e}")
            return {}
    
    def get_active_signals_for_display(self) -> List[Dict]:
        """Get active signals for 'Active Patterns' display"""
        try:
            conn = sqlite3.connect('Data/trading_data.db', timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT symbol, signal_type, price, pattern_sequence, created_at
            FROM trading_signals 
            ORDER BY created_at DESC LIMIT 20
            ''')
            
            signals = []
            for row in cursor.fetchall():
                signals.append({
                    'symbol': row[0],
                    'signal_type': row[1],
                    'price': row[2],
                    'pattern_sequence': row[3],
                    'created_at': row[4]
                })
            
            conn.close()
            return signals
            
        except Exception as e:
            self.logger.error(f"❌ Error getting active signals: {e}")
            return []
    
    def get_paper_trading_brain_for_display(self) -> Dict:
        """Get DB2 confirmations for 'Paper Trading Brain' display"""
        try:
            conn = sqlite3.connect('Data/trading_operations.db', timeout=30.0)
            cursor = conn.cursor()
            
            # Pending confirmations
            cursor.execute('''
            SELECT symbol, signal_price, status, received_time
            FROM db2_signals_received WHERE status = 'PENDING'
            ORDER BY received_time DESC
            ''')
            
            pending_confirmations = []
            for row in cursor.fetchall():
                pending_confirmations.append({
                    'symbol': row[0],
                    'signal_price': row[1],
                    'status': row[2],
                    'received_time': row[3]
                })
            
            # Active monitors from rolling window manager
            from rolling_window_manager import get_rolling_window_manager
            rolling_manager = get_rolling_window_manager()
            active_monitors = rolling_manager.get_active_monitors()
            
            conn.close()
            
            return {
                'pending_confirmations': pending_confirmations,
                'active_monitors': active_monitors
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error getting paper trading brain data: {e}")
            return {}
    
    def get_trading_positions_for_display(self) -> List[Dict]:
        """Get trading positions for 'Paper Trading Records' display"""
        try:
            conn = sqlite3.connect('Data/trading_operations.db', timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT symbol, buy_price, sell_price, shares_quantity, investment, 
                   current_profit, final_profit, status, buy_timestamp, sell_timestamp
            FROM trading_positions 
            ORDER BY buy_timestamp DESC
            ''')
            
            positions = []
            for row in cursor.fetchall():
                positions.append({
                    'symbol': row[0],
                    'buy_price': row[1],
                    'sell_price': row[2],
                    'shares_quantity': row[3],
                    'investment': row[4],
                    'current_profit': row[5],
                    'final_profit': row[6],
                    'status': row[7],
                    'buy_timestamp': row[8],
                    'sell_timestamp': row[9]
                })
            
            conn.close()
            return positions
            
        except Exception as e:
            self.logger.error(f"❌ Error getting trading positions: {e}")
            return []

# Global instance
system_integration = SystemIntegration()
