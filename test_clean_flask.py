#!/usr/bin/env python3
"""
Test the clean Flask app directly
"""

import sys
sys.path.append('.')
from flask_app import app

def test_flask_app():
    # Test the app directly
    with app.test_client() as client:
        print('🔄 Testing Clean Flask App Endpoints...')
        
        # Test main route
        response = client.get('/')
        print(f'✅ Main route: {response.status_code}')
        
        # Test API endpoints (including the missing ones)
        endpoints = [
            '/api/db1/signals',
            '/api/active-positions',
            '/api/symbols',
            '/api/database-schema',
            '/api/data-integrity',
            '/api/priority-queue/status',
            '/api/db2/statistics',
            '/api/db2/trading-positions',
            '/api/layer2-confirmations',
            '/api/paper-trading/records',  # MISSING ENDPOINT
            '/api/db2-signals'  # MISSING ENDPOINT
        ]
        
        success_count = 0
        for endpoint in endpoints:
            try:
                response = client.get(endpoint)
                if response.status_code == 200:
                    data = response.get_json()
                    count = data.get('count', 'N/A')
                    print(f'✅ {endpoint}: SUCCESS (count: {count})')
                    success_count += 1
                else:
                    print(f'❌ {endpoint}: HTTP {response.status_code}')
            except Exception as e:
                print(f'❌ {endpoint}: ERROR - {e}')
        
        # Test POST endpoints
        print('\n🔄 Testing POST endpoints...')
        post_endpoints = [
            '/api/scan-duplicates',
            '/api/start-trading',
            '/api/stop-trading',
            '/api/start-data-fetch',
            '/api/comprehensive-check'  # MISSING ENDPOINT
        ]
        
        for endpoint in post_endpoints:
            try:
                response = client.post(endpoint)
                if response.status_code == 200:
                    data = response.get_json()
                    success = data.get('success', False)
                    print(f'✅ {endpoint}: SUCCESS (success: {success})')
                    success_count += 1
                else:
                    print(f'❌ {endpoint}: HTTP {response.status_code}')
            except Exception as e:
                print(f'❌ {endpoint}: ERROR - {e}')
        
        total_endpoints = len(endpoints) + len(post_endpoints)
        print(f'\n🎉 Results: {success_count}/{total_endpoints} endpoints working!')
        print('✅ Clean Flask App is ready!')

if __name__ == '__main__':
    test_flask_app()
