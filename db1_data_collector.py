#!/usr/bin/env python3
"""
DB1 Data Collector - PURE SQL OPERATIONS

This module handles DB1 data operations with perfect F/R calculation.
NO API CALLS - Only SQL operations on existing data.
API calls are handled by realtime_data_fetcher.py only.
"""

import sqlite3
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional

class DB1DataCollector:
    """Pure SQL DB1 data operations with F/R calculation"""

    def __init__(self):
        self.db_path = 'Data/trading_data.db'
        self.logger = logging.getLogger(__name__)
        
    def calculate_fr_movements_for_all_symbols(self) -> int:
        """Calculate F/R movements for all symbols in DB1 - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()

            # Get all symbols that have data
            cursor.execute('SELECT DISTINCT symbol FROM trading_data ORDER BY symbol')
            symbols = [row[0] for row in cursor.fetchall()]

            updated_count = 0

            for symbol in symbols:
                # Calculate F/R for this symbol
                success = self._calculate_and_update_fr_movement(cursor, symbol)
                if success:
                    updated_count += 1

            conn.commit()
            conn.close()

            if updated_count > 0:
                self.logger.info(f"📊 Updated F/R movements for {updated_count} symbols")

            return updated_count

        except Exception as e:
            self.logger.error(f"❌ Error calculating F/R movements: {e}")
            return 0
    
    def _calculate_and_update_fr_movement(self, cursor, symbol: str) -> bool:
        """Calculate and update F/R movement for a symbol - PURE SQL"""
        try:
            # Get last 2 records for this symbol to calculate F/R
            cursor.execute('''
            SELECT id, close_price, timestamp
            FROM trading_data
            WHERE symbol = ?
            ORDER BY timestamp DESC
            LIMIT 2
            ''', (symbol,))

            records = cursor.fetchall()

            if len(records) < 2:
                # Not enough data for F/R calculation
                return False

            # Current record (most recent)
            current_id, current_close, current_timestamp = records[0]
            # Previous record
            previous_id, previous_close, previous_timestamp = records[1]

            # Calculate F/R movement
            if current_close > previous_close:
                fr_movement = 'R'  # RISE
            elif current_close < previous_close:
                fr_movement = 'F'  # FALL
            else:
                fr_movement = 'N'  # NO CHANGE

            # Update the current record with F/R movement and previous close
            cursor.execute('''
            UPDATE trading_data
            SET fr_movement = ?, previous_close = ?
            WHERE id = ?
            ''', (fr_movement, previous_close, current_id))

            self.logger.debug(f"📊 {symbol}: ₹{previous_close:.2f} → ₹{current_close:.2f} = {fr_movement}")
            return True

        except Exception as e:
            self.logger.error(f"❌ Error calculating F/R for {symbol}: {e}")
            return False
    
    def _calculate_fr_movement(self, symbol: str, current_close: float) -> tuple:
        """Calculate F/R movement using previous close price"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Get the most recent record for this symbol
            cursor.execute('''
            SELECT close_price FROM trading_data 
            WHERE symbol = ? 
            ORDER BY timestamp DESC 
            LIMIT 1
            ''', (symbol,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result is None:
                # First record for this symbol
                return 'START', current_close
            
            previous_close = result[0]
            
            # Calculate F/R movement
            if current_close > previous_close:
                fr_movement = 'R'  # RISE
            elif current_close < previous_close:
                fr_movement = 'F'  # FALL
            else:
                fr_movement = 'N'  # NO CHANGE
            
            return fr_movement, previous_close
            
        except Exception as e:
            self.logger.error(f"❌ Error calculating F/R for {symbol}: {e}")
            return 'START', current_close
    
    def _store_trading_data(self, symbol: str, token: str, timestamp: datetime, 
                           open_price: float, high_price: float, low_price: float, 
                           close_price: float, volume: int, fr_movement: str, 
                           previous_close: float) -> bool:
        """Store trading data in DB1 with perfect structure"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('''
            INSERT OR REPLACE INTO trading_data 
            (symbol, token, exchange, timestamp, open_price, high_price, low_price, 
             close_price, volume, fr_movement, previous_close)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                symbol, token, 'NSE', timestamp, open_price, high_price, 
                low_price, close_price, volume, fr_movement, previous_close
            ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error storing data for {symbol}: {e}")
            return False
    
    def get_recent_data(self, symbol: str, limit: int = 6) -> List[Dict]:
        """Get recent trading data for pattern analysis"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT timestamp, close_price, fr_movement, previous_close
            FROM trading_data 
            WHERE symbol = ? 
            ORDER BY timestamp DESC 
            LIMIT ?
            ''', (symbol, limit))
            
            results = cursor.fetchall()
            conn.close()
            
            # Convert to list of dictionaries
            data = []
            for row in results:
                data.append({
                    'timestamp': row[0],
                    'close_price': row[1],
                    'fr_movement': row[2],
                    'previous_close': row[3]
                })
            
            # Reverse to get chronological order
            return list(reversed(data))
            
        except Exception as e:
            self.logger.error(f"❌ Error getting recent data for {symbol}: {e}")
            return []
    
    def get_all_symbols_with_data(self) -> List[str]:
        """Get all symbols that have data in DB1"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('SELECT DISTINCT symbol FROM trading_data')
            results = cursor.fetchall()
            conn.close()
            
            return [row[0] for row in results]
            
        except Exception as e:
            self.logger.error(f"❌ Error getting symbols: {e}")
            return []

# Global instance
db1_data_collector = DB1DataCollector()
