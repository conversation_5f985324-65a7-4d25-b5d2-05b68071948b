#!/usr/bin/env python3
"""
DB1 Data Collector - INTEGRATED WITH EXISTING SYSTEM

This module handles 15-minute data collection for DB1 with perfect F/R calculation.
INTEGRATES with realtime_data_fetcher.py and uses REAL API calls.
Follows exact documentation specifications.
"""

import sqlite3
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from realtime_data_fetcher import RealtimeDataFetcher
from symbol_manager import SymbolManager

class DB1DataCollector:
    """Perfect DB1 data collector with consistent F/R calculation"""
    
    def __init__(self):
        self.db_path = 'Data/trading_data.db'
        self.logger = logging.getLogger(__name__)

        # INTEGRATION: Use existing components
        self.realtime_fetcher = RealtimeDataFetcher()
        self.symbol_manager = SymbolManager()

        # Connect to API
        self.realtime_fetcher.connect_to_api()
        
    def collect_15min_data(self, symbol: str, token: str) -> bool:
        """Collect 15-minute data for a symbol with perfect F/R calculation"""
        try:
            # Get current data from API
            current_data = self._fetch_current_data(symbol, token)
            if not current_data:
                return False
            
            # Calculate F/R movement
            fr_movement, previous_close = self._calculate_fr_movement(symbol, current_data['close_price'])
            
            # Store data with F/R calculation
            success = self._store_trading_data(
                symbol=symbol,
                token=token,
                timestamp=current_data['timestamp'],
                open_price=current_data['open_price'],
                high_price=current_data['high_price'],
                low_price=current_data['low_price'],
                close_price=current_data['close_price'],
                volume=current_data['volume'],
                fr_movement=fr_movement,
                previous_close=previous_close
            )
            
            if success:
                self.logger.info(f"📊 {symbol}: ₹{previous_close:.2f} → ₹{current_data['close_price']:.2f} = {fr_movement}")
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error collecting data for {symbol}: {e}")
            return False
    
    def _fetch_current_data(self, symbol: str, token: str) -> Optional[Dict]:
        """Fetch current 15-minute data from REAL API"""
        try:
            # Import real API components
            from realtime_data_fetcher import RealtimeDataFetcher

            # Create fetcher instance
            fetcher = RealtimeDataFetcher()

            # Connect to API
            if not fetcher.smart_api:
                if not fetcher.connect_to_api():
                    self.logger.error(f"❌ Failed to connect to API for {symbol}")
                    return None

            # Get current time and round to 15-minute interval
            current_time = datetime.now()

            # Use the real API fetch method
            candle_data = fetcher.fetch_latest_candle_for_symbol(symbol, token, current_time)

            if candle_data:
                return {
                    'timestamp': candle_data['timestamp'],
                    'open_price': candle_data['open_price'],
                    'high_price': candle_data['high_price'],
                    'low_price': candle_data['low_price'],
                    'close_price': candle_data['close_price'],
                    'volume': candle_data['volume']
                }
            else:
                self.logger.error(f"❌ No data received from API for {symbol}")
                return None

        except Exception as e:
            self.logger.error(f"❌ Error fetching REAL data for {symbol}: {e}")
            return None
    
    def _calculate_fr_movement(self, symbol: str, current_close: float) -> tuple:
        """Calculate F/R movement using previous close price"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Get the most recent record for this symbol
            cursor.execute('''
            SELECT close_price FROM trading_data 
            WHERE symbol = ? 
            ORDER BY timestamp DESC 
            LIMIT 1
            ''', (symbol,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result is None:
                # First record for this symbol
                return 'START', current_close
            
            previous_close = result[0]
            
            # Calculate F/R movement
            if current_close > previous_close:
                fr_movement = 'R'  # RISE
            elif current_close < previous_close:
                fr_movement = 'F'  # FALL
            else:
                fr_movement = 'N'  # NO CHANGE
            
            return fr_movement, previous_close
            
        except Exception as e:
            self.logger.error(f"❌ Error calculating F/R for {symbol}: {e}")
            return 'START', current_close
    
    def _store_trading_data(self, symbol: str, token: str, timestamp: datetime, 
                           open_price: float, high_price: float, low_price: float, 
                           close_price: float, volume: int, fr_movement: str, 
                           previous_close: float) -> bool:
        """Store trading data in DB1 with perfect structure"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('''
            INSERT OR REPLACE INTO trading_data 
            (symbol, token, exchange, timestamp, open_price, high_price, low_price, 
             close_price, volume, fr_movement, previous_close)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                symbol, token, 'NSE', timestamp, open_price, high_price, 
                low_price, close_price, volume, fr_movement, previous_close
            ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error storing data for {symbol}: {e}")
            return False
    
    def get_recent_data(self, symbol: str, limit: int = 6) -> List[Dict]:
        """Get recent trading data for pattern analysis"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT timestamp, close_price, fr_movement, previous_close
            FROM trading_data 
            WHERE symbol = ? 
            ORDER BY timestamp DESC 
            LIMIT ?
            ''', (symbol, limit))
            
            results = cursor.fetchall()
            conn.close()
            
            # Convert to list of dictionaries
            data = []
            for row in results:
                data.append({
                    'timestamp': row[0],
                    'close_price': row[1],
                    'fr_movement': row[2],
                    'previous_close': row[3]
                })
            
            # Reverse to get chronological order
            return list(reversed(data))
            
        except Exception as e:
            self.logger.error(f"❌ Error getting recent data for {symbol}: {e}")
            return []
    
    def get_all_symbols_with_data(self) -> List[str]:
        """Get all symbols that have data in DB1"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('SELECT DISTINCT symbol FROM trading_data')
            results = cursor.fetchall()
            conn.close()
            
            return [row[0] for row in results]
            
        except Exception as e:
            self.logger.error(f"❌ Error getting symbols: {e}")
            return []

# Global instance
db1_data_collector = DB1DataCollector()
