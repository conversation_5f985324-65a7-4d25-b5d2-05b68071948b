#!/usr/bin/env python3
"""
DB2 Trade Executor - Main DB2 Engine for Trade Execution
Consolidates: Signal confirmation + Trade execution + Database operations

Responsibilities:
1. Receive signals from DB1 via millisecond communication
2. Start rolling window confirmations (R+R for BUY, F+F for SELL)
3. Execute trades immediately upon confirmation
4. Manage active positions and send updates to DB1
5. Handle paper trading with complete audit trail
6. Database operations for DB2 (trading_operations.db)

WORKS INDEPENDENTLY - Gets data every 2 minutes and executes BUY/SELL
"""

import sqlite3
import logging
import time
import threading
from datetime import datetime
from typing import Dict, List, Optional
from db1_db2_communicator import TradingSignal, ActivePosition, get_communicator
from rolling_window_manager import get_rolling_window_manager

class DB2_TradeExecutor:
    """
    DB2 Trade Executor - Complete Trade Management Engine

    🎯 SIMPLIFIED ONE-WAY ARCHITECTURE: DB1 → DB2 (NO BACK COMMUNICATION)

    Responsibilities:
    1. Receive BUY signals from DB1 (ONE-WAY: DB1→DB2)
    2. Fetch 2-minute data independently
    3. Confirm BUY signals with RR pattern (2-minute data)
    4. Execute BUY orders (₹10,000 worth shares)
    5. Track positions in DB2 SQL tables
    6. Monitor ₹800 profit target using DB2 data
    7. Apply FF condition when ₹800+ profit reached
    8. Execute SELL orders with FF confirmation
    9. Maintain complete paper trading records
    10. Handle all BUY/SELL execution logic

    🔄 WORKS INDEPENDENTLY - 2-minute data + Complete trade lifecycle
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.db_path = 'Data/trading_operations.db'
        self.communicator = get_communicator()
        self.rolling_window_manager = get_rolling_window_manager()

        # Active positions managed by DB2
        self.active_positions = {}

        # Trading parameters
        self.investment_per_symbol = 100000  # ₹100,000 per symbol as specified
        self.profit_target = 800  # ₹800 profit target (₹100,800 total when reached)

        # Initialize database
        self._init_db2_tables()
        self._load_active_positions_from_db2()

        self.logger.info("✅ DB2 Trade Executor initialized - Independent trade execution engine")
        self.logger.info(f"💰 Investment per symbol: ₹{self.investment_per_symbol:,} (₹1 Lakh as specified)")
        self.logger.info(f"🎯 Profit target: ₹{self.profit_target} per position")
        self.logger.info(f"📊 Loaded {len(self.active_positions)} active positions from DB2")
        self.logger.info("🔄 DB2 RESPONSIBILITIES: Execute BUY/SELL with 2-min data confirmation")
        self.logger.info("📤 DB1 RESPONSIBILITIES: Generate 4F+1R signals only")
    
    def run_periodic_check(self):
        """
        🔄 DB2 PERIODIC CHECK - Complete Trade Management Cycle

        1. Check for BUY signals from DB1 (ONE-WAY: DB1→DB2)
        2. Monitor active positions for ₹800 profit target (DB2 RESPONSIBILITY)
        3. Execute SELL when ₹800+ profit + FF pattern confirmed
        4. Cleanup completed rolling windows

        🎯 NO BACK COMMUNICATION TO DB1 - DB2 handles complete trade lifecycle
        """
        try:
            self.logger.info("🔄 DB2 PERIODIC CHECK - Complete Trade Management")

            # Step 1: Check for BUY signals from DB1 (ONE-WAY)
            signals_processed = 0
            while True:
                signal = self.communicator.receive_signal_from_db1(timeout_ms=10)
                if not signal:
                    break

                self.logger.info(f"📥 BUY SIGNAL FROM DB1: {signal.symbol} @ ₹{signal.price:.2f}")
                self._process_buy_signal_from_db1(signal)
                signals_processed += 1

            if signals_processed > 0:
                self.logger.info(f"✅ Processed {signals_processed} BUY signals from DB1")

            # Step 2: Monitor ₹800 profit targets (DB2 RESPONSIBILITY)
            self._monitor_profit_targets_and_execute_sells()

            # Step 3: Cleanup completed rolling windows
            self.rolling_window_manager.cleanup_completed_monitors()

            self.logger.info(f"✅ DB2 cycle completed - {len(self.active_positions)} active positions")

        except Exception as e:
            self.logger.error(f"❌ Error in DB2 periodic check: {e}")

    def run_trade_execution_loop(self):
        """
        DEPRECATED: Use run_periodic_check() instead
        This method is kept for backward compatibility but should not be used
        """
        self.logger.warning("⚠️ run_trade_execution_loop() is deprecated - use run_periodic_check() instead")
        self.logger.info("🔄 DB2 running in EVENT-DRIVEN mode - waiting for periodic calls")

        # Just run one periodic check and exit
        self.run_periodic_check()
    
    def _process_buy_signal_from_db1(self, signal: TradingSignal):
        """
        🔄 Process BUY signal from DB1 and track in DB2 tables

        1. Store signal in db2_signals_received table
        2. Check if symbol already has active position
        3. Start RR confirmation with 2-minute data
        4. Track signal status throughout process
        """
        try:
            if signal.signal_type != 'BUY':
                self.logger.warning(f"⚠️ Unexpected signal type from DB1: {signal.signal_type} (expected BUY only)")
                return

            # Step 1: Store signal in DB2 tracking table
            signal_id = self._store_signal_in_db2(signal)

            # Step 2: Check if symbol already has active position
            if signal.symbol in self.active_positions:
                self.logger.warning(f"⚠️ BUY signal ignored - {signal.symbol} already has active position")
                self._update_signal_status(signal_id, 'IGNORED', 'Symbol already has active position')
                return

            # Step 3: Log signal details
            self.logger.info(f"📥 DB1→DB2 BUY SIGNAL: {signal.symbol}")
            self.logger.info(f"   💰 Signal Price: ₹{signal.price:.2f}")
            self.logger.info(f"   📊 Investment: ₹{self.investment_per_symbol:,}")

            # Calculate potential shares
            potential_shares = int(self.investment_per_symbol / signal.price)
            actual_investment = potential_shares * signal.price
            self.logger.info(f"   📈 Potential Shares: {potential_shares:,} shares")
            self.logger.info(f"   💵 Actual Investment: ₹{actual_investment:,.2f}")

            # Step 4: Start RR confirmation with 2-minute data
            self.logger.info(f"🔄 Starting RR confirmation for {signal.symbol} with 2-minute data")

            success = self.rolling_window_manager.start_monitor(
                symbol=signal.symbol,
                signal_type='BUY',
                base_price=signal.price,
                callback=lambda symbol, confirmation_price, confirmation_time, data_points:
                    self._on_buy_confirmation(symbol, signal, confirmation_price, confirmation_time, data_points, signal_id)
            )

            if success:
                self.logger.info(f"✅ RR rolling window started for {signal.symbol}")
                self._update_signal_status(signal_id, 'CONFIRMING', 'RR pattern confirmation started')
            else:
                self.logger.error(f"❌ Failed to start RR rolling window for {signal.symbol}")
                self._update_signal_status(signal_id, 'FAILED', 'Failed to start RR confirmation')

        except Exception as e:
            self.logger.error(f"❌ Error processing BUY signal from DB1: {e}")

    def _store_signal_in_db2(self, signal: TradingSignal) -> int:
        """Store received signal in DB2 tracking table"""
        try:
            import json

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
            INSERT INTO db2_signals_received
            (symbol, signal_type, signal_price, db1_timestamp, pattern_info, status)
            VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                signal.symbol,
                signal.signal_type,
                signal.price,
                str(signal.timestamp_ns),
                json.dumps(signal.pattern_info) if signal.pattern_info else None,
                'RECEIVED'
            ))

            signal_id = cursor.lastrowid
            conn.commit()
            conn.close()

            return signal_id

        except Exception as e:
            self.logger.error(f"❌ Error storing signal in DB2: {e}")
            return 0

    def _update_signal_status(self, signal_id: int, status: str, notes: str = None):
        """Update signal status in DB2 tracking table"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
            UPDATE db2_signals_received
            SET status = ?, notes = ?
            WHERE id = ?
            ''', (status, notes, signal_id))

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"❌ Error updating signal status: {e}")
    
    def _monitor_profit_targets_and_execute_sells(self):
        """
        🎯 DB2 ₹800 PROFIT MONITORING SYSTEM (Every 15 minutes)

        1. Fetch latest 2-minute data for all active positions
        2. Store data with F/R calculation in DB2 tables
        3. Calculate current profit for each position
        4. When ₹800+ profit reached, check for FF pattern
        5. Execute SELL when FF pattern confirmed

        🔄 SYSTEMATIC APPROACH: SQL-based tracking, not API calls
        """
        try:
            if not self.active_positions:
                self.logger.info("📊 No active positions to monitor")
                return

            self.logger.info(f"💰 MONITORING ₹800 PROFIT TARGET for {len(self.active_positions)} positions")

            for symbol, position in list(self.active_positions.items()):
                try:
                    # Step 1: Fetch and store latest 2-minute data with F/R calculation
                    current_price = self._fetch_and_store_2min_data_for_symbol(symbol)

                    if current_price:
                        # Step 2: Calculate current profit
                        current_value = position.shares_quantity * current_price
                        current_profit = current_value - position.investment

                        # Step 3: Update position profit in DB2
                        position.current_profit = current_profit
                        self._update_position_profit_in_db2(symbol, current_profit, current_price)

                        # Step 4: Log profit status
                        profit_percentage = (current_profit / position.investment) * 100
                        self.logger.info(f"📊 {symbol}: ₹{position.investment:.0f} → ₹{current_value:.0f}")
                        self.logger.info(f"   💰 Profit: ₹{current_profit:.0f} ({profit_percentage:.1f}%) | Target: ₹{self.profit_target}")

                        # Step 5: Check if ₹800 profit target reached
                        if current_profit >= self.profit_target:
                            self.logger.info(f"🎉 PROFIT TARGET REACHED! {symbol}")
                            self.logger.info(f"   💰 Profit: ₹{current_profit:.0f} ≥ ₹{self.profit_target} (Target reached!)")

                            # Step 6: Check for FF pattern in recent 2-minute data
                            if self._check_ff_pattern_in_db2_data(symbol):
                                self.logger.info(f"✅ FF PATTERN CONFIRMED for {symbol} - Executing SELL")
                                self._execute_sell_order(symbol, current_price, position)
                            else:
                                self.logger.info(f"⏳ Waiting for FF pattern confirmation for {symbol}")
                        else:
                            remaining_profit = self.profit_target - current_profit
                            self.logger.info(f"   ⏳ Need ₹{remaining_profit:.0f} more to reach target")

                except Exception as e:
                    self.logger.error(f"❌ Error monitoring profit for {symbol}: {e}")

        except Exception as e:
            self.logger.error(f"❌ Error in profit monitoring: {e}")

    def _fetch_and_store_2min_data_for_symbol(self, symbol: str) -> Optional[float]:
        """
        Fetch latest price and store in DB2 with F/R calculation
        Returns current price for profit calculation
        """
        try:
            # Get current price (simulate 2-minute data fetch)
            current_price = self._get_current_price_2min(symbol)

            if current_price:
                # Store with F/R calculation
                self._store_2min_data_with_fr_calculation(symbol, current_price)
                return current_price
            else:
                self.logger.warning(f"⚠️ Could not fetch 2-minute data for {symbol}")
                return None

        except Exception as e:
            self.logger.error(f"❌ Error fetching 2-min data for {symbol}: {e}")
            return None

    def _check_ff_pattern_in_db2_data(self, symbol: str) -> bool:
        """
        🔍 Check for FF pattern in DB2 2-minute data

        Uses SQL-based pattern detection on stored F/R data
        Returns True if FF pattern found in recent data
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Get last 5 F/R movements for pattern analysis
            cursor.execute('''
            SELECT fr_movement, close_price, timestamp
            FROM db2_trading_data
            WHERE symbol = ? AND fr_movement IS NOT NULL
            ORDER BY timestamp DESC
            LIMIT 5
            ''', (symbol,))

            results = cursor.fetchall()
            conn.close()

            if len(results) < 2:
                self.logger.info(f"📊 {symbol}: Not enough data for FF pattern (need 2+ points)")
                return False

            # Check for FF pattern (last 2 movements should be F, F)
            recent_movements = [row[0] for row in results[:2]]  # Most recent 2
            recent_movements.reverse()  # Chronological order

            pattern_str = ''.join(recent_movements)
            self.logger.info(f"📊 {symbol}: Recent pattern: {pattern_str}")

            if pattern_str == 'FF':
                self.logger.info(f"✅ FF PATTERN DETECTED for {symbol}!")
                return True
            else:
                self.logger.info(f"⏳ {symbol}: Waiting for FF pattern (current: {pattern_str})")
                return False

        except Exception as e:
            self.logger.error(f"❌ Error checking FF pattern for {symbol}: {e}")
            return False

    def _update_position_profit_in_db2(self, symbol: str, current_profit: float, current_price: float):
        """Update position profit in DB2 database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
            UPDATE trading_positions
            SET current_profit = ?, current_price = ?, updated_at = ?
            WHERE symbol = ? AND status = 'ACTIVE'
            ''', (current_profit, current_price, datetime.now().isoformat(), symbol))

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"❌ Error updating position profit for {symbol}: {e}")

    def _execute_sell_order(self, symbol: str, sell_price: float, position):
        """
        Execute SELL order when ₹800+ profit + FF pattern confirmed
        """
        try:
            self.logger.info(f"🎯 EXECUTING SELL ORDER: {symbol} @ ₹{sell_price:.2f}")

            # Calculate final profit
            total_value = position.shares_quantity * sell_price
            final_profit = total_value - position.investment

            # Update position in DB2 as COMPLETED
            if self._close_position_in_db2(symbol, sell_price, final_profit, datetime.now()):
                # Log paper trade
                self._log_paper_trade(symbol, sell_price, 'SELL', position.shares_quantity, total_value)

                # Remove from active positions
                del self.active_positions[symbol]

                self.logger.info(f"✅ SELL EXECUTED: {symbol}")
                self.logger.info(f"   📊 Shares: {position.shares_quantity} @ ₹{sell_price:.2f}")
                self.logger.info(f"   💰 Investment: ₹{position.investment:.0f}")
                self.logger.info(f"   💰 Final Value: ₹{total_value:.0f}")
                self.logger.info(f"   🎉 Final Profit: ₹{final_profit:.0f} ({(final_profit/position.investment)*100:.1f}%)")

            else:
                self.logger.error(f"❌ Failed to close position for {symbol}")

        except Exception as e:
            self.logger.error(f"❌ Error executing SELL order: {e}")
    
    def _on_buy_confirmation(self, symbol: str, signal: TradingSignal, confirmation_price: float,
                           confirmation_time: datetime, data_points: List, signal_id: int):
        """
        🎯 RR PATTERN CONFIRMED - Execute BUY trade with ₹1 Lakh investment

        🔄 DB2 RESPONSIBILITY: Execute BUY with ₹100,000 worth shares
        📊 Calculate shares, profit targets, and portfolio impact
        """
        try:
            self.logger.info(f"🎯 RR CONFIRMED: Executing BUY trade for {symbol}")
            self.logger.info(f"📊 DB1 Signal Price: ₹{signal.price:.2f}")
            self.logger.info(f"💰 DB2 Execution Price: ₹{confirmation_price:.2f}")

            # Calculate trade parameters using ₹1 Lakh investment
            shares_quantity = int(self.investment_per_symbol / confirmation_price)
            actual_investment = shares_quantity * confirmation_price
            target_value = actual_investment + self.profit_target  # ₹100,800
            target_price = target_value / shares_quantity

            self.logger.info(f"📊 TRADE CALCULATION:")
            self.logger.info(f"   💰 Investment Budget: ₹{self.investment_per_symbol:,}")
            self.logger.info(f"   📈 Shares to Buy: {shares_quantity:,} shares")
            self.logger.info(f"   💵 Actual Investment: ₹{actual_investment:,.2f}")
            self.logger.info(f"   🎯 Target Value: ₹{target_value:,.2f} (₹{self.profit_target} profit)")
            self.logger.info(f"   📊 Target Price: ₹{target_price:.2f}")

            # Create active position
            position = ActivePosition(
                symbol=symbol,
                buy_price=confirmation_price,
                shares_quantity=shares_quantity,
                investment=actual_investment,
                target_price=target_price,
                buy_time=confirmation_time,
                current_profit=0.0,
                status='ACTIVE',
                timestamp_ns=time.time_ns()
            )

            # Store position in DB2
            if self._store_position_in_db2(position):
                # Add to active positions
                self.active_positions[symbol] = position

                # Update signal status
                self._update_signal_status(signal_id, 'EXECUTED', f'BUY executed: {shares_quantity:,} shares @ ₹{confirmation_price:.2f}')

                # Log paper trade
                self._log_paper_trade(symbol, confirmation_price, 'BUY', shares_quantity, actual_investment)

                # Update portfolio summary
                self._update_portfolio_summary()

                self.logger.info(f"✅ BUY TRADE EXECUTED IN DB2: {symbol}")
                self.logger.info(f"   📈 Shares: {shares_quantity:,} @ ₹{confirmation_price:.2f}")
                self.logger.info(f"   💰 Investment: ₹{actual_investment:,.2f}")
                self.logger.info(f"   🎯 Target: ₹{target_price:.2f} (₹{self.profit_target} profit)")
                self.logger.info(f"   📊 Portfolio: {len(self.active_positions)} active positions")
                self.logger.info(f"🔄 DB2 will monitor ₹800 profit target every 15 minutes")

            else:
                self.logger.error(f"❌ Failed to store position for {symbol}")
                self._update_signal_status(signal_id, 'FAILED', 'Failed to store position in DB2')

        except Exception as e:
            self.logger.error(f"❌ Error executing BUY trade: {e}")
            self._update_signal_status(signal_id, 'ERROR', f'Error executing BUY: {str(e)}')
    
    def _on_sell_confirmation(self, symbol: str, position, confirmation_price: float,
                            confirmation_time: datetime, data_points: List):
        """
        🎯 FF PATTERN CONFIRMED - Execute SELL trade immediately

        🔄 DB2 RESPONSIBILITY: Execute SELL when ₹800+ profit + FF confirmed
        📊 NO COMMUNICATION BACK TO DB1 - Complete trade lifecycle in DB2
        """
        try:
            self.logger.info(f"🎯 FF CONFIRMED: Executing SELL trade for {symbol} @ ₹{confirmation_price:.2f}")

            # Calculate profit
            total_value = position.shares_quantity * confirmation_price
            actual_profit = total_value - position.investment

            # Update position in DB2
            if self._close_position_in_db2(symbol, confirmation_price, actual_profit, confirmation_time):
                # Log paper trade
                self._log_paper_trade(symbol, confirmation_price, 'SELL', position.shares_quantity, total_value)

                # Remove from active positions
                del self.active_positions[symbol]

                self.logger.info(f"✅ SELL EXECUTED: {symbol} - {position.shares_quantity} shares @ ₹{confirmation_price:.2f}")
                self.logger.info(f"💰 Final Profit: ₹{actual_profit:.0f} ({(actual_profit/position.investment)*100:.1f}%)")
                self.logger.info(f"🎉 Trade completed: ₹{position.investment:.0f} → ₹{total_value:.0f}")

            else:
                self.logger.error(f"❌ Failed to close position for {symbol}")

        except Exception as e:
            self.logger.error(f"❌ Error executing SELL trade: {e}")
    
    def _get_current_price_2min(self, symbol: str) -> Optional[float]:
        """
        Get current price using 2-minute data for DB2 operations

        🔄 DB2 RESPONSIBILITY: Fetch 2-minute data independently
        📊 Used for both profit monitoring and FF pattern confirmation
        """
        try:
            # TODO: Replace with actual 2-minute real-time price API
            # For now, get latest price from DB2's 2-minute data table
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Try to get from DB2's 2-minute data table first
            cursor.execute('''
            SELECT close_price FROM db2_trading_data
            WHERE symbol = ?
            ORDER BY timestamp DESC
            LIMIT 1
            ''', (symbol,))

            result = cursor.fetchone()

            # If no 2-minute data available, fallback to 15-minute data
            if not result:
                conn.close()
                conn = sqlite3.connect('Data/trading_data.db')
                cursor = conn.cursor()

                cursor.execute('''
                SELECT close_price FROM trading_data
                WHERE symbol = ?
                ORDER BY timestamp DESC
                LIMIT 1
                ''', (symbol,))

                result = cursor.fetchone()

            conn.close()

            return result[0] if result else None

        except Exception as e:
            self.logger.error(f"❌ Error getting 2-minute price for {symbol}: {e}")
            return None
    
    def _store_position_in_db2(self, position: ActivePosition) -> bool:
        """Store active position in DB2 database with proper calculations"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Calculate target_value (investment + profit target)
            target_value = position.investment + self.profit_target

            cursor.execute('''
            INSERT OR REPLACE INTO trading_positions
            (symbol, buy_price, shares_quantity, investment, target_value, target_price, buy_time, status, actual_profit, profit_amount)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                position.symbol,
                position.buy_price,
                position.shares_quantity,
                position.investment,
                target_value,
                position.target_price,
                position.buy_time.isoformat() if hasattr(position.buy_time, 'isoformat') else str(position.buy_time),
                position.status,
                0.0,  # initial actual_profit
                0.0   # initial profit_amount
            ))

            conn.commit()
            conn.close()

            self.logger.info(f"💾 Position stored: {position.symbol} - {position.shares_quantity} shares @ ₹{position.buy_price:.2f}")
            self.logger.info(f"   Investment: ₹{position.investment:.0f}, Target: ₹{target_value:.0f}")

            return True

        except Exception as e:
            self.logger.error(f"❌ Error storing position in DB2: {e}")
            return False
    
    def _close_position_in_db2(self, symbol: str, sell_price: float, profit: float, sell_time: datetime) -> bool:
        """Close position in DB2 database with proper profit calculations"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
            UPDATE trading_positions
            SET sell_price = ?, actual_profit = ?, profit_amount = ?, sell_time = ?, status = 'SOLD'
            WHERE symbol = ? AND status = 'ACTIVE'
            ''', (
                sell_price,
                profit,
                profit,  # Both actual_profit and profit_amount get the same value
                sell_time.isoformat() if hasattr(sell_time, 'isoformat') else str(sell_time),
                symbol
            ))

            rows_affected = cursor.rowcount
            conn.commit()
            conn.close()

            if rows_affected > 0:
                self.logger.info(f"✅ Position closed: {symbol} @ ₹{sell_price:.2f} - Profit: ₹{profit:.0f}")
                return True
            else:
                self.logger.error(f"❌ No active position found to close for {symbol}")
                return False

        except Exception as e:
            self.logger.error(f"❌ Error closing position in DB2: {e}")
            return False
    
    def _log_paper_trade(self, symbol: str, price: float, action: str, quantity: int, amount: float):
        """Log paper trade to CSV file"""
        try:
            import csv
            import os
            
            csv_file = 'Data/csv_files/paper_trade.csv'
            
            # Ensure directory exists
            os.makedirs(os.path.dirname(csv_file), exist_ok=True)
            
            # Check if file exists to write header
            file_exists = os.path.exists(csv_file)
            
            with open(csv_file, 'a', newline='') as file:
                writer = csv.writer(file)
                
                if not file_exists:
                    writer.writerow(['Timestamp', 'Symbol', 'Action', 'Price', 'Quantity', 'Amount', 'API_Call'])
                
                api_call = f"smart_api.placeOrder(symbol='{symbol}', transactiontype='{action}', quantity={quantity}, price={price}, ordertype='MARKET', producttype='INTRADAY')"
                
                writer.writerow([
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    symbol,
                    action,
                    f"{price:.2f}",
                    quantity,
                    f"{amount:.2f}",
                    api_call
                ])
            
            self.logger.info(f"📝 Paper trade logged: {action} {symbol} @ ₹{price:.2f}")
        
        except Exception as e:
            self.logger.error(f"❌ Error logging paper trade: {e}")
    
    def _init_db2_tables(self):
        """Initialize DB2 database tables with proper schema"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Trading positions table with standardized schema
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS trading_positions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                buy_price REAL NOT NULL,
                sell_price REAL,
                shares_quantity INTEGER NOT NULL,
                investment REAL NOT NULL,
                target_value REAL NOT NULL,
                target_price REAL NOT NULL,
                buy_time DATETIME NOT NULL,
                sell_time DATETIME,
                actual_profit REAL DEFAULT 0,
                profit_amount REAL DEFAULT 0,
                status TEXT DEFAULT 'ACTIVE',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, buy_time)
            )
            ''')

            # Add missing columns for profit tracking (for existing databases)
            try:
                cursor.execute('ALTER TABLE trading_positions ADD COLUMN target_value REAL DEFAULT 0')
            except sqlite3.OperationalError:
                pass  # Column already exists

            try:
                cursor.execute('ALTER TABLE trading_positions ADD COLUMN profit_amount REAL DEFAULT 0')
            except sqlite3.OperationalError:
                pass  # Column already exists

            try:
                cursor.execute('ALTER TABLE trading_positions ADD COLUMN current_profit REAL DEFAULT 0')
            except sqlite3.OperationalError:
                pass  # Column already exists

            try:
                cursor.execute('ALTER TABLE trading_positions ADD COLUMN current_price REAL')
            except sqlite3.OperationalError:
                pass  # Column already exists

            try:
                cursor.execute('ALTER TABLE trading_positions ADD COLUMN profit_target REAL DEFAULT 800.0')
            except sqlite3.OperationalError:
                pass  # Column already exists

            # Create DB2 2-minute trading data table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS db2_trading_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                close_price REAL NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                fr_movement TEXT,
                previous_close REAL,
                fr_calculated BOOLEAN DEFAULT FALSE
            )
            ''')

            # Create DB2 signal tracking table (BUY signals from DB1)
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS db2_signals_received (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                signal_type TEXT NOT NULL,
                signal_price REAL NOT NULL,
                received_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                db1_timestamp TEXT,
                pattern_info TEXT,
                status TEXT DEFAULT 'PENDING',
                confirmation_price REAL,
                confirmation_time DATETIME,
                execution_time DATETIME,
                notes TEXT
            )
            ''')

            # Create DB2 portfolio summary table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS db2_portfolio_summary (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                total_investment REAL DEFAULT 0,
                total_current_value REAL DEFAULT 0,
                total_profit REAL DEFAULT 0,
                active_positions INTEGER DEFAULT 0,
                completed_trades INTEGER DEFAULT 0,
                success_rate REAL DEFAULT 0,
                last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            conn.commit()
            conn.close()

            self.logger.info("✅ DB2 tables initialized with standardized schema")
            self.logger.info("✅ DB2 2-minute data table created for F/R tracking")

        except Exception as e:
            self.logger.error(f"❌ Error initializing DB2 tables: {e}")

    def _load_active_positions_from_db2(self):
        """Load active positions from DB2 database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
            SELECT symbol, buy_price, shares_quantity, investment, target_price, buy_time
            FROM trading_positions
            WHERE status = 'ACTIVE'
            ''')

            rows = cursor.fetchall()
            conn.close()

            for row in rows:
                symbol, buy_price, shares_quantity, investment, target_price, buy_time = row

                # Create ActivePosition object
                position = ActivePosition(
                    symbol=symbol,
                    buy_price=buy_price,
                    shares_quantity=shares_quantity,
                    investment=investment,
                    target_price=target_price,
                    buy_time=datetime.fromisoformat(buy_time) if isinstance(buy_time, str) else buy_time,
                    current_profit=0.0,
                    status='ACTIVE',
                    timestamp_ns=time.time_ns()
                )

                self.active_positions[symbol] = position

            self.logger.info(f"✅ Loaded {len(self.active_positions)} active positions from DB2")

        except Exception as e:
            self.logger.error(f"❌ Error loading active positions from DB2: {e}")

    def _fetch_data_independently_2min(self):
        """
        🔄 DB2 INDEPENDENT 2-MINUTE DATA FETCH

        Fetch 2-minute data independently for:
        1. Profit monitoring (₹800 target)
        2. FF pattern confirmation for SELL
        3. Complete trade lifecycle management

        📊 NO DEPENDENCY ON DB1 - DB2 works independently
        """
        try:
            from datetime import datetime

            # Check if it's trading hours (9:15 AM - 3:30 PM)
            now = datetime.now()
            market_start = now.replace(hour=9, minute=15, second=0, microsecond=0)
            market_end = now.replace(hour=15, minute=30, second=0, microsecond=0)

            if not (market_start <= now <= market_end):
                self.logger.info("🕐 Outside trading hours - skipping 2-minute data fetch")
                return

            self.logger.info("📊 DB2 INDEPENDENT 2-MINUTE DATA FETCH")

            # Get 2-minute prices for active positions
            for symbol in self.active_positions.keys():
                try:
                    current_price = self._get_current_price_2min(symbol)
                    if current_price:
                        self.logger.debug(f"📈 {symbol}: ₹{current_price:.2f} (2-min data)")

                        # Store 2-minute data with F/R calculation in DB2 database
                        self._store_2min_data_with_fr_calculation(symbol, current_price)

                except Exception as e:
                    self.logger.error(f"❌ Error fetching 2-min price for {symbol}: {e}")

            self.logger.info(f"✅ DB2 2-minute data fetch completed for {len(self.active_positions)} positions")

        except Exception as e:
            self.logger.error(f"❌ Error in 2-minute data fetch: {e}")

    def _store_2min_data_with_fr_calculation(self, symbol: str, price: float):
        """
        🔄 Store 2-minute data in DB2 with proper F/R calculation

        Uses the SAME FIXED LOGIC as DB1 for F/R calculation:
        - Compare current price with immediately previous interval
        - Calculate F (Fall), R (Rise), or N (No change)
        - Store with proper previous_close reference
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Create 2-minute data table if not exists
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS db2_trading_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                close_price REAL NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                fr_movement TEXT,
                previous_close REAL,
                fr_calculated BOOLEAN DEFAULT FALSE
            )
            ''')

            # Get previous close price for F/R calculation
            previous_close = self._get_previous_close_price_db2(symbol)

            # Calculate F/R movement
            if previous_close is None:
                fr_movement = 'START'
                previous_close_value = None
            else:
                if price > previous_close:
                    fr_movement = 'R'
                elif price < previous_close:
                    fr_movement = 'F'
                else:
                    fr_movement = 'N'
                previous_close_value = previous_close

            # Insert 2-minute data point with F/R calculation
            cursor.execute('''
            INSERT INTO db2_trading_data (symbol, close_price, timestamp, fr_movement, previous_close, fr_calculated)
            VALUES (?, ?, ?, ?, ?, ?)
            ''', (symbol, price, datetime.now().isoformat(), fr_movement, previous_close_value, True))

            conn.commit()
            conn.close()

            self.logger.debug(f"📊 DB2 F/R: {symbol} ₹{previous_close_value or 'START':.2f} → ₹{price:.2f} = {fr_movement}")

        except Exception as e:
            self.logger.error(f"❌ Error storing 2-min data with F/R for {symbol}: {e}")

    def _get_previous_close_price_db2(self, symbol: str) -> Optional[float]:
        """
        Get previous close price for F/R calculation in DB2
        Uses SAME LOGIC as fixed DB1 F/R calculation
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Get the most recent price for this symbol
            cursor.execute('''
            SELECT close_price FROM db2_trading_data
            WHERE symbol = ?
            ORDER BY timestamp DESC
            LIMIT 1
            ''', (symbol,))

            result = cursor.fetchone()
            conn.close()

            return result[0] if result else None

        except Exception as e:
            self.logger.error(f"❌ Error getting previous close for {symbol}: {e}")
            return None

    def _update_portfolio_summary(self):
        """Update portfolio summary in DB2 database"""
        try:
            # Calculate portfolio totals
            total_investment = sum(pos.investment for pos in self.active_positions.values())
            total_current_profit = sum(pos.current_profit for pos in self.active_positions.values())
            total_current_value = total_investment + total_current_profit
            active_positions = len(self.active_positions)

            # Get completed trades count
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT COUNT(*) FROM trading_positions WHERE status = 'COMPLETED'")
            completed_trades = cursor.fetchone()[0]

            # Calculate success rate (simplified)
            total_trades = active_positions + completed_trades
            success_rate = (completed_trades / total_trades * 100) if total_trades > 0 else 0

            # Update or insert portfolio summary
            cursor.execute('''
            INSERT OR REPLACE INTO db2_portfolio_summary
            (id, total_investment, total_current_value, total_profit, active_positions,
             completed_trades, success_rate, last_updated)
            VALUES (1, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                total_investment,
                total_current_value,
                total_current_profit,
                active_positions,
                completed_trades,
                success_rate,
                datetime.now().isoformat()
            ))

            conn.commit()
            conn.close()

            self.logger.info(f"📊 PORTFOLIO UPDATED:")
            self.logger.info(f"   💰 Total Investment: ₹{total_investment:,.2f}")
            self.logger.info(f"   📈 Current Value: ₹{total_current_value:,.2f}")
            self.logger.info(f"   💵 Current Profit: ₹{total_current_profit:,.2f}")
            self.logger.info(f"   📊 Active Positions: {active_positions}")
            self.logger.info(f"   ✅ Completed Trades: {completed_trades}")

        except Exception as e:
            self.logger.error(f"❌ Error updating portfolio summary: {e}")

    def get_portfolio_summary(self) -> dict:
        """Get current portfolio summary for display"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM db2_portfolio_summary WHERE id = 1')
            result = cursor.fetchone()

            if result:
                return {
                    'total_investment': result[1],
                    'total_current_value': result[2],
                    'total_profit': result[3],
                    'active_positions': result[4],
                    'completed_trades': result[5],
                    'success_rate': result[6],
                    'last_updated': result[7]
                }
            else:
                return {
                    'total_investment': 0,
                    'total_current_value': 0,
                    'total_profit': 0,
                    'active_positions': 0,
                    'completed_trades': 0,
                    'success_rate': 0,
                    'last_updated': datetime.now().isoformat()
                }

        except Exception as e:
            self.logger.error(f"❌ Error getting portfolio summary: {e}")
            return {}

    def get_status(self) -> Dict[str, any]:
        """Get current status of DB2 trade executor"""
        total_profit = sum(pos.current_profit for pos in self.active_positions.values())
        profitable_positions = sum(1 for pos in self.active_positions.values() if pos.current_profit > 0)

        return {
            'active_positions': len(self.active_positions),
            'profitable_positions': profitable_positions,
            'total_current_profit': total_profit,
            'profit_target': self.profit_target,
            'active_monitors': len(self.rolling_window_manager.get_active_monitors()),
            'rolling_window_status': self.rolling_window_manager.get_active_monitors(),
            'communicator_status': self.communicator.get_queue_status(),
            'performance_stats': self.communicator.get_performance_stats(),
            'last_update': datetime.now().isoformat()
        }

# Global instance
_db2_trade_executor = None

def get_db2_trade_executor() -> DB2_TradeExecutor:
    """Get global DB2 trade executor instance"""
    global _db2_trade_executor
    if _db2_trade_executor is None:
        _db2_trade_executor = DB2_TradeExecutor()
    return _db2_trade_executor
