#!/usr/bin/env python3
"""
DB2 Trade Executor - EXACT DOCUMENTATION SPECIFICATIONS

Main DB2 engine for trade execution and profit monitoring
FUNCTIONS:
* Receives BUY signals from DB1
* Executes trades with ₹100,000 investment per symbol
* Monitors ₹800 profit target every 15 minutes
* Executes SELL when ₹800 + FF pattern confirmed

BASE POINTS:
* BUY confirmation: DB1 signal price for first 2-min comparison
* SELL confirmation: ₹800 confirmation price for FF pattern

DATABASE: trading_operations.db (multiple tables)
AUTO-TRIGGER: Yes, called by realtime_data_fetcher.py every 15 minutes
"""

import sqlite3
import logging
from datetime import datetime
from typing import List, Dict, Optional
from rolling_window_manager import get_rolling_window_manager

class DB2TradeExecutor:
    """Main DB2 engine for trade execution and profit monitoring"""

    def __init__(self):
        self.db_path = 'Data/trading_operations.db'
        self.logger = logging.getLogger(__name__)
        self.rolling_window_manager = get_rolling_window_manager()

        # Trading parameters from documentation
        self.investment_per_symbol = 100000.0  # ₹100,000 per symbol
        self.profit_target = 800.0  # ₹800 profit target
        
    def receive_buy_signals_from_db1(self) -> List[Dict]:
        """Receives BUY signals from DB1 - EXACT DOCUMENTATION"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Get new BUY signals from DB1
            cursor.execute('''
            SELECT id, symbol, signal_type, signal_price, status, received_time, db1_signal_id
            FROM db2_signals_received 
            WHERE status = 'PENDING' AND signal_type = 'BUY'
            ORDER BY received_time ASC
            ''')
            
            signals = []
            for row in cursor.fetchall():
                signals.append({
                    'id': row[0],
                    'symbol': row[1],
                    'signal_type': row[2],
                    'signal_price': row[3],
                    'status': row[4],
                    'received_time': row[5],
                    'db1_signal_id': row[6]
                })
            
            conn.close()
            
            if signals:
                self.logger.info(f"📨 RECEIVED {len(signals)} BUY SIGNALS FROM DB1")
                
                # Start RR confirmation for each signal
                for signal in signals:
                    self._start_rr_confirmation_thread(signal)
            
            return signals
            
        except Exception as e:
            self.logger.error(f"❌ Error receiving BUY signals from DB1: {e}")
            return []
    
    def _start_rr_confirmation_thread(self, signal: Dict) -> bool:
        """Start RR pattern confirmation thread - EXACT DOCUMENTATION"""
        try:
            # Start separate 2-minute thread for RR confirmation
            success = self.rolling_window_manager.start_monitor(
                symbol=signal['symbol'],
                signal_type='BUY',  # RR confirmation for BUY
                base_price=signal['signal_price'],  # BASE POINT for first 2-min comparison
                callback=self._handle_rr_confirmation,
                timeout_minutes=10
            )

            if success:
                self.logger.info(f"🔄 STARTED RR CONFIRMATION THREAD: {signal['symbol']} @ ₹{signal['signal_price']:.2f}")
                return True
            else:
                self.logger.error(f"❌ Failed to start RR thread for {signal['symbol']}")
                return False

        except Exception as e:
            self.logger.error(f"❌ Error starting RR confirmation for {signal['symbol']}: {e}")
            return False

    def _handle_rr_confirmation(self, symbol: str, confirmation_price: float,
                               confirmation_time: datetime, data_points: List[Dict]) -> None:
        """Handle RR confirmation callback - Execute BUY trade"""
        try:
            self.logger.info(f"🎯 RR CONFIRMATION RECEIVED: {symbol} @ ₹{confirmation_price:.2f}")

            # Get the original signal details
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()

            cursor.execute('''
            SELECT id, db1_signal_id, signal_price FROM db2_signals_received
            WHERE symbol = ? AND status = 'PENDING'
            ORDER BY received_time DESC LIMIT 1
            ''', (symbol,))

            result = cursor.fetchone()
            conn.close()

            if result:
                signal_id, db1_signal_id, original_signal_price = result

                # Execute BUY trade at confirmation price
                success = self.execute_buy_trade(symbol, confirmation_price, db1_signal_id)

                if success:
                    self.logger.info(f"✅ BUY EXECUTED after RR confirmation: {symbol}")
                else:
                    self.logger.error(f"❌ Failed to execute BUY for {symbol}")
            else:
                self.logger.error(f"❌ No pending signal found for {symbol}")

        except Exception as e:
            self.logger.error(f"❌ Error handling RR confirmation for {symbol}: {e}")

    def _handle_ff_confirmation(self, symbol: str, confirmation_price: float,
                               confirmation_time: datetime, data_points: List[Dict]) -> None:
        """Handle FF confirmation callback - Execute SELL trade"""
        try:
            self.logger.info(f"🎯 FF CONFIRMATION RECEIVED: {symbol} @ ₹{confirmation_price:.2f}")

            # Get the active position
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()

            cursor.execute('''
            SELECT id FROM trading_positions
            WHERE symbol = ? AND status = 'ACTIVE' AND profit_800_base_point IS NOT NULL
            ORDER BY buy_timestamp DESC LIMIT 1
            ''', (symbol,))

            result = cursor.fetchone()
            conn.close()

            if result:
                position_id = result[0]

                # Execute SELL trade at confirmation price
                success = self.execute_sell_trade(symbol, confirmation_price, position_id)

                if success:
                    self.logger.info(f"✅ SELL EXECUTED after FF confirmation: {symbol}")
                else:
                    self.logger.error(f"❌ Failed to execute SELL for {symbol}")
            else:
                self.logger.error(f"❌ No active position found for {symbol}")

        except Exception as e:
            self.logger.error(f"❌ Error handling FF confirmation for {symbol}: {e}")
    
    def execute_buy_trade(self, symbol: str, signal_price: float, db1_signal_id: int) -> bool:
        """Execute BUY trade with ₹100,000 investment - EXACT DOCUMENTATION"""
        try:
            # Calculate shares quantity
            shares_quantity = int(self.investment_per_symbol / signal_price)
            actual_investment = shares_quantity * signal_price
            
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Insert into trading_positions table
            cursor.execute('''
            INSERT INTO trading_positions 
            (symbol, buy_price, shares_quantity, investment, current_profit, status, 
             profit_800_base_point, profit_800_timestamp, db1_signal_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                symbol, signal_price, shares_quantity, actual_investment, 0.0, 'ACTIVE',
                None, None, db1_signal_id
            ))
            
            position_id = cursor.lastrowid
            
            # Update signal status to EXECUTED
            cursor.execute('''
            UPDATE db2_signals_received 
            SET status = 'EXECUTED'
            WHERE db1_signal_id = ? AND symbol = ?
            ''', (db1_signal_id, symbol))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"💰 BUY EXECUTED: {symbol}")
            self.logger.info(f"   Price: ₹{signal_price:.2f}")
            self.logger.info(f"   Shares: {shares_quantity}")
            self.logger.info(f"   Investment: ₹{actual_investment:.2f}")
            self.logger.info(f"   Position ID: {position_id}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error executing BUY trade for {symbol}: {e}")
            return False
    
    def monitor_800_profit_target(self) -> List[Dict]:
        """Monitor ₹800 profit target every 15 minutes - EXACT DOCUMENTATION"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Get active positions
            cursor.execute('''
            SELECT id, symbol, buy_price, shares_quantity, investment, current_profit, 
                   profit_800_base_point, profit_800_timestamp
            FROM trading_positions 
            WHERE status = 'ACTIVE'
            ''')
            
            positions = []
            profit_800_reached = []
            
            for row in cursor.fetchall():
                position = {
                    'id': row[0],
                    'symbol': row[1],
                    'buy_price': row[2],
                    'shares_quantity': row[3],
                    'investment': row[4],
                    'current_profit': row[5],
                    'profit_800_base_point': row[6],
                    'profit_800_timestamp': row[7]
                }
                positions.append(position)
                
                # Check if profit reached ₹800 and not already marked
                if position['current_profit'] >= self.profit_target and not position['profit_800_base_point']:
                    # Mark ₹800 base point
                    current_price = position['buy_price'] + (self.profit_target / position['shares_quantity'])
                    
                    cursor.execute('''
                    UPDATE trading_positions 
                    SET profit_800_base_point = ?, profit_800_timestamp = ?
                    WHERE id = ?
                    ''', (current_price, datetime.now(), position['id']))
                    
                    profit_800_reached.append(position)
                    
                    # Start FF confirmation thread for SELL
                    self._start_ff_confirmation_thread(position, current_price)
            
            conn.commit()
            conn.close()
            
            if profit_800_reached:
                self.logger.info(f"📈 ₹800 PROFIT REACHED: {len(profit_800_reached)} positions")
                for pos in profit_800_reached:
                    self.logger.info(f"   {pos['symbol']}: ₹{pos['current_profit']:.2f} profit")
            
            return positions
            
        except Exception as e:
            self.logger.error(f"❌ Error monitoring ₹800 profit: {e}")
            return []
    
    def _start_ff_confirmation_thread(self, position: Dict, base_price: float) -> bool:
        """Start FF pattern confirmation thread for SELL - EXACT DOCUMENTATION"""
        try:
            # Start separate 2-minute thread for FF confirmation
            success = self.rolling_window_manager.start_monitor(
                symbol=position['symbol'],
                signal_type='SELL',  # FF confirmation for SELL
                base_price=base_price,  # BASE POINT for FF pattern (₹800 confirmation price)
                callback=self._handle_ff_confirmation,
                timeout_minutes=10
            )

            if success:
                self.logger.info(f"🔄 STARTED FF CONFIRMATION THREAD: {position['symbol']} @ ₹{base_price:.2f}")
                return True
            else:
                self.logger.error(f"❌ Failed to start FF thread for {position['symbol']}")
                return False

        except Exception as e:
            self.logger.error(f"❌ Error starting FF confirmation for {position['symbol']}: {e}")
            return False
    
    def execute_sell_trade(self, symbol: str, sell_price: float, position_id: int) -> bool:
        """Execute SELL when ₹800 + FF pattern confirmed - EXACT DOCUMENTATION"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Get position details
            cursor.execute('''
            SELECT buy_price, shares_quantity, investment 
            FROM trading_positions 
            WHERE id = ?
            ''', (position_id,))
            
            result = cursor.fetchone()
            if not result:
                return False
            
            buy_price, shares_quantity, investment = result
            
            # Calculate final profit
            sell_value = shares_quantity * sell_price
            final_profit = sell_value - investment
            
            # Update position to COMPLETED
            cursor.execute('''
            UPDATE trading_positions 
            SET status = 'COMPLETED', sell_price = ?, sell_value = ?, 
                final_profit = ?, sell_timestamp = ?
            WHERE id = ?
            ''', (sell_price, sell_value, final_profit, datetime.now(), position_id))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"💸 SELL EXECUTED: {symbol}")
            self.logger.info(f"   Buy Price: ₹{buy_price:.2f}")
            self.logger.info(f"   Sell Price: ₹{sell_price:.2f}")
            self.logger.info(f"   Shares: {shares_quantity}")
            self.logger.info(f"   Final Profit: ₹{final_profit:.2f}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error executing SELL trade for {symbol}: {e}")
            return False
    
    def run_db2_cycle(self) -> Dict:
        """Run complete DB2 cycle - AUTO-TRIGGER every 15 minutes"""
        try:
            self.logger.info("🔄 RUNNING DB2 TRADE EXECUTOR CYCLE")
            
            # Step 1: Receive BUY signals from DB1
            new_signals = self.receive_buy_signals_from_db1()
            
            # Step 2: Monitor ₹800 profit target
            positions = self.monitor_800_profit_target()
            
            result = {
                'new_signals_received': len(new_signals),
                'active_positions': len(positions),
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"✅ DB2 CYCLE COMPLETE: {len(new_signals)} new signals, {len(positions)} active positions")
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Error in DB2 cycle: {e}")
            return {'error': str(e)}

# Global instance
db2_trade_executor = DB2TradeExecutor()
