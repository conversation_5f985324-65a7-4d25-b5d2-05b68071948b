#!/usr/bin/env python3
"""
Update Database Schema for Perfect System

This script ensures all required tables exist with correct schema
for the perfect DB1-DB2 integration system.
"""

import os
import sqlite3
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def update_database_schema():
    """Update database schema to match perfect system requirements"""
    logger.info("🔧 UPDATING DATABASE SCHEMA FOR PERFECT SYSTEM")
    
    # Ensure Data directory exists
    os.makedirs('Data', exist_ok=True)
    
    # Update DB1 schema
    update_db1_schema()
    
    # Update DB2 schema
    update_db2_schema()
    
    logger.info("✅ DATABASE SCHEMA UPDATE COMPLETE")

def update_db1_schema():
    """Update DB1 (trading_data.db) schema"""
    logger.info("📊 Updating DB1 schema (trading_data.db)")
    
    conn = sqlite3.connect('Data/trading_data.db', timeout=30.0)
    cursor = conn.cursor()
    
    # Ensure trading_signals table exists with correct schema
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS trading_signals (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        symbol TEXT NOT NULL,
        signal_type TEXT NOT NULL DEFAULT 'BUY',
        price REAL NOT NULL,
        pattern_sequence TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    
    # Ensure trading_data table has required columns
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS trading_data (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        symbol TEXT NOT NULL,
        token TEXT NOT NULL,
        exchange TEXT NOT NULL DEFAULT 'NSE',
        timestamp DATETIME NOT NULL,
        open_price REAL NOT NULL,
        high_price REAL NOT NULL,
        low_price REAL NOT NULL,
        close_price REAL NOT NULL,
        volume INTEGER NOT NULL,
        fr_movement TEXT DEFAULT 'START',
        previous_close REAL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(symbol, timestamp)
    )
    ''')
    
    # Create indexes for performance
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_trading_data_symbol_timestamp ON trading_data(symbol, timestamp)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_trading_data_symbol ON trading_data(symbol)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_trading_data_fr_movement ON trading_data(fr_movement)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_trading_signals_symbol ON trading_signals(symbol)')
    
    conn.commit()
    conn.close()
    
    logger.info("✅ DB1 schema updated")

def update_db2_schema():
    """Update DB2 (trading_operations.db) schema"""
    logger.info("📊 Updating DB2 schema (trading_operations.db)")
    
    conn = sqlite3.connect('Data/trading_operations.db', timeout=30.0)
    cursor = conn.cursor()
    
    # Table 1: db2_signals_received (signals from DB1)
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS db2_signals_received (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        symbol TEXT NOT NULL,
        signal_type TEXT NOT NULL DEFAULT 'BUY',
        signal_price REAL NOT NULL,
        status TEXT DEFAULT 'PENDING',
        received_time DATETIME DEFAULT CURRENT_TIMESTAMP,
        db1_signal_id INTEGER NOT NULL
    )
    ''')
    
    # Table 2: db2_trading_data (2-minute intervals)
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS db2_trading_data (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        symbol TEXT NOT NULL,
        close_price REAL NOT NULL,
        timestamp DATETIME NOT NULL,
        fr_movement TEXT DEFAULT 'START',
        previous_close REAL,
        is_800_base_point BOOLEAN DEFAULT FALSE,
        fr_calculated BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(symbol, timestamp)
    )
    ''')
    
    # Table 3: trading_positions (executed trades)
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS trading_positions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        symbol TEXT NOT NULL,
        buy_price REAL NOT NULL,
        sell_price REAL,
        shares_quantity INTEGER NOT NULL,
        investment REAL NOT NULL,
        sell_value REAL,
        current_profit REAL DEFAULT 0.0,
        final_profit REAL,
        status TEXT DEFAULT 'ACTIVE',
        profit_800_base_point REAL,
        profit_800_timestamp DATETIME,
        buy_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        sell_timestamp DATETIME,
        db1_signal_id INTEGER NOT NULL
    )
    ''')
    
    # Add missing columns if they don't exist
    try:
        cursor.execute('ALTER TABLE db2_trading_data ADD COLUMN is_800_base_point BOOLEAN DEFAULT FALSE')
    except sqlite3.OperationalError:
        pass  # Column already exists

    try:
        cursor.execute('ALTER TABLE db2_trading_data ADD COLUMN fr_calculated BOOLEAN DEFAULT FALSE')
    except sqlite3.OperationalError:
        pass  # Column already exists

    # Create indexes for performance
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_db2_signals_symbol ON db2_signals_received(symbol)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_db2_signals_status ON db2_signals_received(status)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_db2_trading_data_symbol_timestamp ON db2_trading_data(symbol, timestamp)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_db2_trading_data_symbol ON db2_trading_data(symbol)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_trading_positions_symbol ON trading_positions(symbol)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_trading_positions_status ON trading_positions(status)')
    
    conn.commit()
    conn.close()
    
    logger.info("✅ DB2 schema updated")

def verify_schema():
    """Verify the schema is correct"""
    logger.info("🔍 VERIFYING SCHEMA")
    
    # Verify DB1
    conn1 = sqlite3.connect('Data/trading_data.db')
    cursor1 = conn1.cursor()
    
    cursor1.execute("SELECT name FROM sqlite_master WHERE type='table'")
    db1_tables = [row[0] for row in cursor1.fetchall()]
    logger.info(f"   DB1 tables: {db1_tables}")
    
    conn1.close()
    
    # Verify DB2
    conn2 = sqlite3.connect('Data/trading_operations.db')
    cursor2 = conn2.cursor()
    
    cursor2.execute("SELECT name FROM sqlite_master WHERE type='table'")
    db2_tables = [row[0] for row in cursor2.fetchall()]
    logger.info(f"   DB2 tables: {db2_tables}")
    
    conn2.close()
    
    # Check required tables
    required_db1_tables = ['trading_data', 'trading_signals']
    required_db2_tables = ['db2_signals_received', 'db2_trading_data', 'trading_positions']
    
    db1_ok = all(table in db1_tables for table in required_db1_tables)
    db2_ok = all(table in db2_tables for table in required_db2_tables)
    
    if db1_ok and db2_ok:
        logger.info("✅ SCHEMA VERIFICATION PASSED")
        return True
    else:
        logger.error("❌ SCHEMA VERIFICATION FAILED")
        if not db1_ok:
            missing = [t for t in required_db1_tables if t not in db1_tables]
            logger.error(f"   Missing DB1 tables: {missing}")
        if not db2_ok:
            missing = [t for t in required_db2_tables if t not in db2_tables]
            logger.error(f"   Missing DB2 tables: {missing}")
        return False

def main():
    """Main function"""
    logger.info("🚀 DATABASE SCHEMA UPDATE FOR PERFECT SYSTEM")
    logger.info("=" * 80)
    
    update_database_schema()
    success = verify_schema()
    
    logger.info("\n" + "=" * 80)
    if success:
        logger.info("🎉 DATABASE SCHEMA UPDATE SUCCESSFUL!")
        logger.info("✅ DB1: trading_data, trading_signals")
        logger.info("✅ DB2: db2_signals_received, db2_trading_data, trading_positions")
        logger.info("✅ All indexes created")
        logger.info("✅ Ready for perfect DB1-DB2 system")
    else:
        logger.error("❌ DATABASE SCHEMA UPDATE FAILED")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
