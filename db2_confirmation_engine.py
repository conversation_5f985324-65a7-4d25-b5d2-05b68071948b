#!/usr/bin/env python3
"""
DB2 Confirmation Engine - PURE SQL OPERATIONS

This module handles Layer 2 confirmations (FF/RR detection) using 2-minute data.
NO API CALLS - Only SQL operations on DB2 data.
Uses SAME LOGIC as DB1 pattern detection but for FF/RR patterns.
FOLLOWS EXACT DOCUMENTATION SPECIFICATIONS.
"""

import sqlite3
import logging
from typing import List, Dict, Optional
from datetime import datetime
from db2_signal_receiver import db2_signal_receiver
from db2_data_collector import db2_data_collector

class DB2ConfirmationEngine:
    """Pure SQL Layer 2 confirmation engine for FF/RR detection"""
    
    def __init__(self):
        self.db_path = 'Data/trading_operations.db'
        self.logger = logging.getLogger(__name__)
        
    def check_ff_rr_confirmation(self, symbol: str) -> Optional[str]:
        """Check for FF or RR confirmation using 2-minute data - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Get last 3 data points (3 points = 2 movements = FF or RR)
            cursor.execute('''
            SELECT timestamp, close_price, fr_movement, previous_close
            FROM db2_trading_data 
            WHERE symbol = ? 
            ORDER BY timestamp DESC 
            LIMIT 3
            ''', (symbol,))
            
            results = cursor.fetchall()
            conn.close()
            
            if len(results) < 3:
                return None
            
            # Reverse to get chronological order
            data_points = list(reversed(results))
            
            # Extract F/R movements (skip first START record)
            movements = []
            
            for i, (timestamp, close_price, fr_movement, previous_close) in enumerate(data_points):
                if i > 0:  # Skip first record (START)
                    movements.append(fr_movement)
            
            # Check for FF or RR pattern
            if len(movements) != 2:
                return None
            
            pattern_string = ''.join(movements)
            
            if pattern_string == 'FF':
                self.logger.info(f"🔥 FF CONFIRMATION DETECTED: {symbol}")
                return 'FF'
            elif pattern_string == 'RR':
                self.logger.info(f"🚀 RR CONFIRMATION DETECTED: {symbol}")
                return 'RR'
            else:
                self.logger.debug(f"   {symbol}: Pattern {pattern_string} (not FF or RR)")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Error checking confirmation for {symbol}: {e}")
            return None
    
    def process_pending_confirmations(self) -> int:
        """Process all pending signals for Layer 2 confirmation - PURE SQL"""
        try:
            # Get all pending signals
            pending_signals = db2_signal_receiver.get_pending_signals()
            
            if not pending_signals:
                return 0
            
            confirmed_count = 0
            
            for signal in pending_signals:
                symbol = signal['symbol']
                signal_id = signal['id']
                
                # Check for FF/RR confirmation
                confirmation = self.check_ff_rr_confirmation(symbol)
                
                if confirmation:
                    # Mark signal as confirmed
                    success = db2_signal_receiver.mark_signal_confirmed(signal_id, confirmation)
                    
                    if success:
                        confirmed_count += 1
                        self.logger.info(f"✅ SIGNAL CONFIRMED: {symbol} with {confirmation}")
                        self.logger.info(f"   Signal ID: {signal_id}")
                        self.logger.info(f"   Original Price: ₹{signal['signal_price']:.2f}")
            
            if confirmed_count > 0:
                self.logger.info(f"🎯 CONFIRMED {confirmed_count} SIGNALS")
            
            return confirmed_count
            
        except Exception as e:
            self.logger.error(f"❌ Error processing pending confirmations: {e}")
            return 0
    
    def check_800_profit_ff_confirmation(self, symbol: str) -> Optional[str]:
        """Check for FF confirmation after ₹800 profit reached - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Get data points after ₹800 base point
            cursor.execute('''
            SELECT timestamp, close_price, fr_movement, previous_close
            FROM db2_trading_data 
            WHERE symbol = ? AND timestamp > (
                SELECT timestamp FROM db2_trading_data 
                WHERE symbol = ? AND is_800_base_point = TRUE 
                ORDER BY timestamp DESC LIMIT 1
            )
            ORDER BY timestamp DESC 
            LIMIT 3
            ''', (symbol, symbol))
            
            results = cursor.fetchall()
            conn.close()
            
            if len(results) < 3:
                return None
            
            # Reverse to get chronological order
            data_points = list(reversed(results))
            
            # Extract F/R movements (skip first point)
            movements = []
            
            for i, (timestamp, close_price, fr_movement, previous_close) in enumerate(data_points):
                if i > 0:  # Skip first record
                    movements.append(fr_movement)
            
            # Check for FF pattern for SELL confirmation
            if len(movements) >= 2:
                pattern_string = ''.join(movements[-2:])  # Last 2 movements
                
                if pattern_string == 'FF':
                    self.logger.info(f"🔥 ₹800 FF SELL CONFIRMATION: {symbol}")
                    return 'FF'
            
            return None
                
        except Exception as e:
            self.logger.error(f"❌ Error checking ₹800 FF confirmation for {symbol}: {e}")
            return None
    
    def get_confirmation_statistics(self) -> Dict:
        """Get Layer 2 confirmation statistics - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Total pending signals
            cursor.execute('SELECT COUNT(*) FROM db2_signals_received WHERE status = "PENDING"')
            pending_count = cursor.fetchone()[0]
            
            # FF confirmations
            cursor.execute('SELECT COUNT(*) FROM db2_signals_received WHERE status = "FF"')
            ff_count = cursor.fetchone()[0]
            
            # RR confirmations
            cursor.execute('SELECT COUNT(*) FROM db2_signals_received WHERE status = "RR"')
            rr_count = cursor.fetchone()[0]
            
            # Ready for execution
            cursor.execute('SELECT COUNT(*) FROM db2_signals_received WHERE status IN ("FF", "RR", "CONFIRMED")')
            ready_for_execution = cursor.fetchone()[0]
            
            # Confirmation rate
            cursor.execute('SELECT COUNT(*) FROM db2_signals_received')
            total_signals = cursor.fetchone()[0]
            
            confirmed_signals = ff_count + rr_count
            confirmation_rate = (confirmed_signals / max(1, total_signals)) * 100
            
            conn.close()
            
            return {
                'pending_confirmation': pending_count,
                'ff_confirmations': ff_count,
                'rr_confirmations': rr_count,
                'ready_for_execution': ready_for_execution,
                'total_confirmed': confirmed_signals,
                'confirmation_rate': confirmation_rate
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error getting confirmation statistics: {e}")
            return {}
    
    def run_confirmation_cycle(self) -> Dict:
        """Run complete confirmation cycle - PURE SQL"""
        try:
            self.logger.info("🔄 RUNNING LAYER 2 CONFIRMATION CYCLE")
            
            # Step 1: Calculate F/R movements for pending signals
            calculated_count = db2_data_collector.calculate_fr_movements_for_pending_symbols()
            
            # Step 2: Process confirmations
            confirmed_count = self.process_pending_confirmations()
            
            # Step 3: Get statistics
            stats = self.get_confirmation_statistics()
            
            result = {
                'fr_calculated': calculated_count,
                'signals_confirmed': confirmed_count,
                'statistics': stats
            }
            
            if calculated_count > 0 or confirmed_count > 0:
                self.logger.info(f"📊 CONFIRMATION CYCLE: {calculated_count} F/R calculated, {confirmed_count} signals confirmed")
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Error running confirmation cycle: {e}")
            return {}

# Global instance
db2_confirmation_engine = DB2ConfirmationEngine()
