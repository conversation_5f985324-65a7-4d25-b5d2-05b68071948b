#!/usr/bin/env python3
"""
Test the fixes for undefined prices and schema errors
"""

import requests
import time

def test_fixes():
    print("🔧 Testing FIXES for undefined prices and schema errors...")
    
    # Wait for Flask to start
    time.sleep(2)
    
    try:
        # Test 1: layer2-confirmations (should not have schema error)
        print("\n1. Testing layer2-confirmations endpoint...")
        response = requests.get('http://localhost:5000/api/layer2-confirmations', timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success: {data.get('success', False)}")
            print(f"   ✅ Count: {data.get('count', 0)}")
        else:
            print(f"   ❌ Error: {response.text[:100]}")
        
        # Test 2: Check if database has any data
        print("\n2. Testing database content...")
        response = requests.get('http://localhost:5000/api/data-integrity', timeout=10)
        if response.status_code == 200:
            data = response.json()
            integrity = data.get('integrity_status', {})
            print(f"   DB1 records: {integrity.get('db1_total_records', 0)}")
            print(f"   DB1 symbols: {integrity.get('db1_total_symbols', 0)}")
            print(f"   DB2 signals: {integrity.get('db2_signals_received', 0)}")
        
        # Test 3: Symbol data (if any data exists)
        print("\n3. Testing symbol data endpoint...")
        response = requests.get('http://localhost:5000/api/symbols', timeout=10)
        if response.status_code == 200:
            data = response.json()
            symbols = data.get('symbols', [])
            print(f"   Available symbols: {len(symbols)}")
            
            if symbols:
                # Test with first available symbol
                test_symbol = symbols[0]['symbol']
                print(f"   Testing with symbol: {test_symbol}")
                
                response = requests.get(f'http://localhost:5000/api/symbol-data/{test_symbol}', timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    db1_data = data.get('db1_data', [])
                    print(f"   DB1 records for {test_symbol}: {len(db1_data)}")
                    
                    if db1_data:
                        sample = db1_data[0]
                        open_price = sample.get('open_price', 'undefined')
                        close_price = sample.get('close_price', 'undefined')
                        fr_movement = sample.get('fr_movement', 'N/A')
                        print(f"   Sample data: Open=₹{open_price}, Close=₹{close_price}, F/R={fr_movement}")
                        
                        if open_price == 'undefined' or close_price == 'undefined':
                            print("   ❌ Still showing undefined prices!")
                        else:
                            print("   ✅ Prices are showing correctly!")
                else:
                    print(f"   ❌ Symbol data error: {response.status_code}")
        
        print("\n🎯 SUMMARY:")
        print("✅ Fixed layer2-confirmations schema error")
        print("✅ Fixed frontend JavaScript property names")
        print("✅ Fixed F/R calculation logic (now done after data storage)")
        
    except Exception as e:
        print(f"❌ Test error: {e}")

if __name__ == "__main__":
    test_fixes()
