#!/usr/bin/env python3
"""
Test Fixed Priority System

This script tests the fixed priority system that uses 6 data points like the simple database manager.
"""

import logging
from datetime import datetime, timedelta
from simple_database_manager import get_simple_database_manager

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_priority_test_data():
    """Create test data for different priority levels"""
    logger.info("📊 CREATING PRIORITY TEST DATA")
    
    db_manager = get_simple_database_manager()
    base_time = datetime.now().replace(hour=10, minute=0, second=0, microsecond=0)
    
    # GOLD: Symbol with FFFFR pattern (should be highest priority)
    gold_data = []
    for i in range(6):
        gold_data.append({
            'symbol': 'GOLDTEST',
            'token': '11111',
            'exchange': 'NSE',
            'timestamp': base_time + timedelta(minutes=15*i),
            'open_price': 1000.0,
            'high_price': 1002.0,
            'low_price': 998.0,
            'close_price': 1000.0 if i == 0 else (997.0 - (i-1)*2 if i < 5 else 996.0),  # FFFFR pattern
            'volume': 100000 + i*10000
        })
    
    # SILVER: Symbol with RFFFF pattern (should be medium priority)
    silver_data = []
    for i in range(6):
        silver_data.append({
            'symbol': 'SILVERTEST',
            'token': '22222',
            'exchange': 'NSE',
            'timestamp': base_time + timedelta(minutes=15*i),
            'open_price': 1000.0,
            'high_price': 1002.0,
            'low_price': 998.0,
            'close_price': 1000.0 if i == 0 else (1001.0 if i == 1 else 999.0 - (i-2)*1),  # RFFFF pattern
            'volume': 100000 + i*10000
        })
    
    # Insert test data
    all_data = gold_data + silver_data
    success = db_manager.insert_trading_data(all_data)
    
    if success:
        logger.info("✅ PRIORITY TEST DATA CREATED")
        logger.info(f"   GOLDTEST: FFFFR pattern")
        logger.info(f"   SILVERTEST: RFFFF pattern")
        return True
    else:
        logger.error("❌ FAILED TO CREATE PRIORITY TEST DATA")
        return False

def test_fixed_priority_system():
    """Test the fixed priority system"""
    logger.info("\n🧪 TESTING FIXED PRIORITY SYSTEM")
    logger.info("-" * 60)
    
    try:
        from realtime_data_fetcher import RealtimeDataFetcher
        fetcher = RealtimeDataFetcher()
        
        # Run priority analysis
        priority_batches = fetcher.analyze_symbol_patterns_for_priority()
        
        logger.info("📊 FIXED PRIORITY ANALYSIS RESULTS:")
        
        # Check each tier
        for tier in ['GOLD', 'SILVER', 'BRONZE', 'REMAINING']:
            batch = priority_batches.get(tier, [])
            logger.info(f"   {tier}: {len(batch)} symbols")
            
            # Show test symbols in each tier
            test_symbols = ['GOLDTEST', 'SILVERTEST']
            for symbol_priority in batch:
                if symbol_priority.symbol in test_symbols:
                    logger.info(f"      ✅ {symbol_priority.symbol}: {symbol_priority.pattern_status}")
        
        # Verify expected classifications
        gold_symbols = [sp.symbol for sp in priority_batches.get('GOLD', [])]
        silver_symbols = [sp.symbol for sp in priority_batches.get('SILVER', [])]
        
        # Check if classifications are correct
        gold_correct = 'GOLDTEST' in gold_symbols
        silver_correct = 'SILVERTEST' in silver_symbols
        
        logger.info(f"\n📋 PRIORITY CLASSIFICATION VERIFICATION:")
        logger.info(f"   GOLDTEST in GOLD: {'✅' if gold_correct else '❌'}")
        logger.info(f"   SILVERTEST in SILVER: {'✅' if silver_correct else '❌'}")
        
        if gold_correct and silver_correct:
            logger.info("✅ FIXED PRIORITY SYSTEM WORKING CORRECTLY")
            return True
        else:
            logger.error("❌ FIXED PRIORITY SYSTEM CLASSIFICATION INCORRECT")
            return False
            
    except Exception as e:
        logger.error(f"❌ FIXED PRIORITY SYSTEM TEST FAILED: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return False

def test_signaling_verification():
    """Verify signaling is still working"""
    logger.info("\n🧪 VERIFYING SIGNALING STILL WORKS")
    logger.info("-" * 60)
    
    import sqlite3
    
    # Check DB1 signals for GOLDTEST
    conn1 = sqlite3.connect('Data/trading_data.db')
    cursor1 = conn1.cursor()
    
    cursor1.execute('''
    SELECT symbol, signal_type, signal_price, pattern_sequence, drop_percentage
    FROM trading_signals 
    WHERE symbol = 'GOLDTEST'
    ''')
    
    db1_signals = cursor1.fetchall()
    conn1.close()
    
    logger.info(f"📊 DB1 SIGNALS FOR GOLDTEST: {len(db1_signals)}")
    for signal in db1_signals:
        symbol, signal_type, price, pattern, drop = signal
        logger.info(f"   {signal_type} {symbol} @ ₹{price:.2f} | Pattern: {pattern} | Drop: {drop:.2f}%")
    
    # Check DB2 signals for GOLDTEST
    conn2 = sqlite3.connect('Data/trading_operations.db')
    cursor2 = conn2.cursor()
    
    cursor2.execute('''
    SELECT symbol, signal_type, signal_price, status
    FROM db2_signals_received 
    WHERE symbol = 'GOLDTEST'
    ''')
    
    db2_signals = cursor2.fetchall()
    conn2.close()
    
    logger.info(f"📊 DB2 SIGNALS FOR GOLDTEST: {len(db2_signals)}")
    for signal in db2_signals:
        symbol, signal_type, price, status = signal
        logger.info(f"   {signal_type} {symbol} @ ₹{price:.2f} | Status: {status}")
    
    # Verify signaling worked
    if len(db1_signals) > 0 and len(db2_signals) > 0:
        logger.info("✅ SIGNALING STILL WORKING CORRECTLY")
        return True
    else:
        logger.error(f"❌ SIGNALING VERIFICATION FAILED: DB1={len(db1_signals)}, DB2={len(db2_signals)}")
        return False

def cleanup_test_data():
    """Clean up test data"""
    logger.info("\n🧹 CLEANING TEST DATA")
    
    import sqlite3
    
    test_symbols = ['GOLDTEST', 'SILVERTEST']
    
    # Clean DB1
    conn1 = sqlite3.connect('Data/trading_data.db')
    cursor1 = conn1.cursor()
    for symbol in test_symbols:
        cursor1.execute('DELETE FROM trading_data WHERE symbol = ?', (symbol,))
        cursor1.execute('DELETE FROM trading_signals WHERE symbol = ?', (symbol,))
    conn1.commit()
    conn1.close()
    
    # Clean DB2
    conn2 = sqlite3.connect('Data/trading_operations.db')
    cursor2 = conn2.cursor()
    for symbol in test_symbols:
        cursor2.execute('DELETE FROM db2_signals_received WHERE symbol = ?', (symbol,))
        cursor2.execute('DELETE FROM trading_positions WHERE symbol = ?', (symbol,))
    conn2.commit()
    conn2.close()
    
    logger.info("✅ TEST DATA CLEANED")

def main():
    """Main test function"""
    logger.info("🧪 FIXED PRIORITY SYSTEM TEST")
    logger.info("=" * 80)
    
    # Step 1: Create test data
    data_created = create_priority_test_data()
    
    if not data_created:
        logger.error("❌ FAILED TO CREATE TEST DATA")
        return False
    
    # Step 2: Test fixed priority system
    priority_test_passed = test_fixed_priority_system()
    
    # Step 3: Verify signaling still works
    signaling_test_passed = test_signaling_verification()
    
    # Clean up
    cleanup_test_data()
    
    logger.info("\n" + "=" * 80)
    if priority_test_passed and signaling_test_passed:
        logger.info("🎉 FIXED PRIORITY SYSTEM TEST PASSED!")
        logger.info("✅ Priority system now uses 6 data points (synchronized)")
        logger.info("✅ GOLDTEST correctly classified as GOLD")
        logger.info("✅ SILVERTEST correctly classified as SILVER")
        logger.info("✅ Signaling still working correctly")
        logger.info("✅ DB1→DB2 flow intact")
        logger.info("✅ Pattern-based priority working")
        logger.info("✅ Data fetching will follow GOLD→SILVER→BRONZE→REMAINING order")
    else:
        logger.error("❌ FIXED PRIORITY SYSTEM TEST FAILED!")
        if not priority_test_passed:
            logger.error("   Priority system issues")
        if not signaling_test_passed:
            logger.error("   Signaling issues")
    
    return priority_test_passed and signaling_test_passed

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
