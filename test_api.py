#!/usr/bin/env python3
"""
Test API Endpoints - Quick test to verify Flask APIs work
"""
import requests
import json
import time
import subprocess
import sys
from threading import Thread

def start_flask_app():
    """Start Flask app in background"""
    try:
        subprocess.run([sys.executable, "flask_app.py"], check=True)
    except Exception as e:
        print(f"Error starting Flask app: {e}")

def test_api_endpoints():
    """Test API endpoints"""
    base_url = "http://localhost:5000"
    
    endpoints = [
        "/api/sql-dashboard/summary",
        "/api/db1/signals",
        "/api/db2/pending-confirmations",
        "/api/db2/trading-positions",
        "/api/db2/statistics",
        "/api/portfolio-status",
        "/api/active-positions",
        "/api/data-integrity"
    ]
    
    print("🔄 Testing API endpoints...")
    print("=" * 50)
    
    for endpoint in endpoints:
        try:
            url = f"{base_url}{endpoint}"
            print(f"🔄 Testing: {endpoint}")
            
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"✅ {endpoint}: SUCCESS")
                    # Print some key data
                    if 'summary' in data:
                        summary = data['summary']
                        print(f"   📊 Signals: {summary.get('db1_signals', 0)}")
                        print(f"   📊 Positions: {summary.get('db2_active_positions', 0)}")
                    elif 'count' in data:
                        print(f"   📊 Count: {data['count']}")
                else:
                    print(f"⚠️ {endpoint}: API returned success=False")
                    print(f"   Error: {data.get('error', 'Unknown error')}")
            else:
                print(f"❌ {endpoint}: HTTP {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"❌ {endpoint}: Connection refused (Flask app not running?)")
        except requests.exceptions.Timeout:
            print(f"❌ {endpoint}: Timeout")
        except Exception as e:
            print(f"❌ {endpoint}: {e}")
        
        time.sleep(0.5)  # Small delay between requests
    
    print("\n" + "=" * 50)
    print("🎉 API testing complete!")

def main():
    """Main function"""
    print("🚀 TESTING FLASK API ENDPOINTS")
    print("📝 Note: Make sure Flask app is running on localhost:5000")
    print("💡 Start with: python flask_app.py")
    print()
    
    # Wait a moment for user to start Flask app
    input("Press Enter when Flask app is running...")
    
    test_api_endpoints()

if __name__ == "__main__":
    main()
