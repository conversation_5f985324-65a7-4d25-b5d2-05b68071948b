#!/usr/bin/env python3
"""
SIMPLE INTEGRATION - JUST COORDINATION

ROLE: Simple coordination between DB1 and DB2 (NO APIs for flask)
FUNCTIONS:
- Connect realtime_data_fetcher.py with DB1 and DB2
- Database schema setup only
- Simple system coordination
- Handle market timings (9:15-15:15, except weekends)

NO APIs FOR FLASK - Flask reads DB1/DB2 directly!
"""

import os
import sqlite3
import logging
from datetime import datetime, time
from typing import Dict
from db1_engine import db1_engine
from db2_engine import db2_engine

class SimpleIntegration:
    """Simple system coordination - NO APIs"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.setup_database_schema()
        self.logger.info("🚀 SIMPLE INTEGRATION INITIALIZED")
    
    def setup_database_schema(self):
        """Setup database schema for both DB1 and DB2"""
        try:
            # Ensure Data directory exists
            os.makedirs('Data', exist_ok=True)
            
            # Setup DB1 schema
            conn1 = sqlite3.connect('Data/trading_data.db', timeout=30.0)
            cursor1 = conn1.cursor()
            
            cursor1.execute('''
            CREATE TABLE IF NOT EXISTS trading_signals (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                signal_type TEXT NOT NULL DEFAULT 'BUY',
                price REAL NOT NULL,
                pattern_sequence TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            cursor1.execute('CREATE INDEX IF NOT EXISTS idx_trading_signals_symbol ON trading_signals(symbol)')
            conn1.commit()
            conn1.close()
            
            # Setup DB2 schema
            conn2 = sqlite3.connect('Data/trading_operations.db', timeout=30.0)
            cursor2 = conn2.cursor()
            
            cursor2.execute('''
            CREATE TABLE IF NOT EXISTS db2_signals_received (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                signal_type TEXT NOT NULL DEFAULT 'BUY',
                signal_price REAL NOT NULL,
                status TEXT DEFAULT 'PENDING',
                received_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                db1_signal_id INTEGER NOT NULL
            )
            ''')
            
            cursor2.execute('''
            CREATE TABLE IF NOT EXISTS db2_2min_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                close_price REAL NOT NULL,
                timestamp DATETIME NOT NULL,
                fr_movement TEXT DEFAULT 'START',
                previous_close REAL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, timestamp)
            )
            ''')
            
            cursor2.execute('''
            CREATE TABLE IF NOT EXISTS db2_15min_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                close_price REAL NOT NULL,
                timestamp DATETIME NOT NULL,
                profit_amount REAL DEFAULT 0.0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, timestamp)
            )
            ''')
            
            cursor2.execute('''
            CREATE TABLE IF NOT EXISTS trading_positions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                buy_price REAL NOT NULL,
                sell_price REAL,
                shares_quantity INTEGER NOT NULL,
                investment REAL NOT NULL,
                sell_value REAL,
                current_profit REAL DEFAULT 0.0,
                final_profit REAL,
                status TEXT DEFAULT 'ACTIVE',
                profit_800_reached BOOLEAN DEFAULT FALSE,
                profit_800_timestamp DATETIME,
                buy_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                sell_timestamp DATETIME,
                db1_signal_id INTEGER NOT NULL
            )
            ''')
            
            cursor2.execute('CREATE INDEX IF NOT EXISTS idx_db2_signals_symbol ON db2_signals_received(symbol)')
            cursor2.execute('CREATE INDEX IF NOT EXISTS idx_db2_2min_data_symbol ON db2_2min_data(symbol)')
            cursor2.execute('CREATE INDEX IF NOT EXISTS idx_db2_15min_data_symbol ON db2_15min_data(symbol)')
            cursor2.execute('CREATE INDEX IF NOT EXISTS idx_trading_positions_symbol ON trading_positions(symbol)')
            conn2.commit()
            conn2.close()
            
            self.logger.info("✅ Database schema setup complete")
            
        except Exception as e:
            self.logger.error(f"❌ Error setting up database schema: {e}")
    
    def is_market_hours(self) -> bool:
        """Check if current time is within market hours (9:15-15:15, except weekends)"""
        try:
            now = datetime.now()
            
            # Check if weekend
            if now.weekday() >= 5:  # Saturday=5, Sunday=6
                return False
            
            # Check market hours (9:15 AM to 3:15 PM)
            current_time = now.time()
            market_start = time(9, 15)  # 9:15 AM
            market_end = time(15, 15)   # 3:15 PM
            
            return market_start <= current_time <= market_end
            
        except Exception as e:
            self.logger.error(f"❌ Error checking market hours: {e}")
            return False
    
    def trigger_complete_system_cycle(self) -> Dict:
        """
        MAIN INTEGRATION POINT: Called by realtime_data_fetcher.py every 15 minutes
        Triggers complete DB1 → DB2 cycle ONLY during market hours
        """
        try:
            # Check market hours
            if not self.is_market_hours():
                self.logger.info("⏰ Outside market hours - skipping system cycle")
                return {'status': 'outside_market_hours', 'timestamp': datetime.now().isoformat()}
            
            self.logger.info("🔄 TRIGGERING COMPLETE SYSTEM CYCLE (MARKET HOURS)")
            
            # Step 1: Run DB1 cycle (15-minute data processing)
            db1_result = db1_engine.run_complete_db1_cycle()
            
            # Step 2: Run DB2 cycle (2-minute confirmations + trade execution)
            db2_result = db2_engine.run_complete_db2_cycle()
            
            result = {
                'status': 'completed',
                'market_hours': True,
                'db1_result': db1_result,
                'db2_result': db2_result,
                'system_cycle_time': datetime.now().isoformat()
            }
            
            self.logger.info("✅ COMPLETE SYSTEM CYCLE FINISHED")
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Error in complete system cycle: {e}")
            return {'error': str(e)}
    
    def check_data_integrity(self) -> Dict:
        """Check data integrity for missing data"""
        try:
            self.logger.info("🔍 CHECKING DATA INTEGRITY")
            
            # Check DB1 data integrity
            conn1 = sqlite3.connect('Data/trading_data.db', timeout=30.0)
            cursor1 = conn1.cursor()
            
            # Count total records
            cursor1.execute('SELECT COUNT(*) FROM trading_data')
            total_records = cursor1.fetchone()[0]
            
            # Count records with F/R movements
            cursor1.execute('SELECT COUNT(*) FROM trading_data WHERE fr_movement IS NOT NULL')
            fr_calculated = cursor1.fetchone()[0]
            
            conn1.close()
            
            # Check DB2 data integrity
            conn2 = sqlite3.connect('Data/trading_operations.db', timeout=30.0)
            cursor2 = conn2.cursor()
            
            cursor2.execute('SELECT COUNT(*) FROM db2_signals_received')
            db2_signals = cursor2.fetchone()[0]
            
            cursor2.execute('SELECT COUNT(*) FROM trading_positions')
            trading_positions = cursor2.fetchone()[0]
            
            conn2.close()
            
            integrity_status = {
                'db1_total_records': total_records,
                'db1_fr_calculated': fr_calculated,
                'db1_fr_percentage': (fr_calculated / max(1, total_records)) * 100,
                'db2_signals_received': db2_signals,
                'db2_trading_positions': trading_positions,
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"📊 DATA INTEGRITY: DB1={total_records} records, DB2={db2_signals} signals, {trading_positions} positions")
            
            return integrity_status
            
        except Exception as e:
            self.logger.error(f"❌ Error checking data integrity: {e}")
            return {'error': str(e)}

# Global instance
simple_integration = SimpleIntegration()
