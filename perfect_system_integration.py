#!/usr/bin/env python3
"""
Perfect System Integration - INTEGRATES WITH EXISTING SYSTEM

This module integrates the new perfect DB1/DB2 components with the existing system.
WORKS WITH: realtime_data_fetcher.py, flask_app.py, rolling_window_manager.py
NO API CALLS - Only SQL operations.
FOLLOWS EXACT DOCUMENTATION SPECIFICATIONS.
"""

import logging
import time
import threading
from datetime import datetime
from typing import Dict, List

# Import existing system components
from realtime_data_fetcher import RealtimeDataFetcher
from rolling_window_manager import RollingWindowManager

# Import new perfect components
from db1_data_collector import db1_data_collector
from db1_pattern_detector import db1_pattern_detector
from perfect_db1_signal_generator import perfect_db1_signal_generator
from db1_signal_transmitter import db1_signal_transmitter
from db2_signal_receiver import db2_signal_receiver
from db2_data_collector import db2_data_collector
from db2_confirmation_engine import db2_confirmation_engine

class PerfectSystemIntegration:
    """Perfect integration with existing system components"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Existing system components
        self.realtime_fetcher = RealtimeDataFetcher()
        self.rolling_window_manager = RollingWindowManager()
        
        self.logger.info("🚀 PERFECT SYSTEM INTEGRATION INITIALIZED")
        
    def trigger_db1_analysis_after_data_fetch(self) -> Dict:
        """Trigger DB1 analysis after realtime_data_fetcher completes - INTEGRATION POINT"""
        try:
            self.logger.info("🔄 TRIGGERING DB1 ANALYSIS AFTER DATA FETCH")
            
            # Step 1: Calculate F/R movements for all symbols (PURE SQL)
            fr_updated = db1_data_collector.calculate_fr_movements_for_all_symbols()
            
            # Step 2: Detect 4F+1R patterns (PURE SQL)
            detected_patterns = db1_pattern_detector.scan_all_symbols_for_patterns()
            
            # Step 3: Generate BUY signals (PURE SQL)
            generated_signals = perfect_db1_signal_generator.scan_and_generate_signals()
            
            # Step 4: Transmit signals to DB2 (PURE SQL)
            transmitted_count = db1_signal_transmitter.transmit_signals_to_db2()
            
            # Step 5: Trigger DB2 processing
            db2_result = self.trigger_db2_processing()
            
            result = {
                'fr_updated': fr_updated,
                'patterns_detected': len(detected_patterns),
                'signals_generated': len(generated_signals),
                'signals_transmitted': transmitted_count,
                'db2_processing': db2_result,
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"✅ DB1 ANALYSIS COMPLETE: {len(generated_signals)} signals generated, {transmitted_count} transmitted")
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Error in DB1 analysis: {e}")
            return {'error': str(e)}
    
    def trigger_db2_processing(self) -> Dict:
        """Trigger DB2 processing for Layer 2 confirmations - INTEGRATION POINT"""
        try:
            self.logger.info("🔄 TRIGGERING DB2 PROCESSING")
            
            # Step 1: Run Layer 2 confirmation cycle (PURE SQL)
            confirmation_result = db2_confirmation_engine.run_confirmation_cycle()
            
            # Step 2: Get confirmed signals ready for execution
            confirmed_signals = db2_signal_receiver.get_confirmed_signals()
            
            # Step 3: Start rolling window threads for confirmed signals (INTEGRATION)
            rolling_threads_started = 0
            for signal in confirmed_signals:
                if signal['status'] in ['RR', 'CONFIRMED']:
                    # Start 2-minute rolling window for this symbol
                    success = self.rolling_window_manager.start_2min_thread_for_symbol(
                        symbol=signal['symbol'],
                        signal_price=signal['signal_price'],
                        thread_type='BUY_CONFIRMATION'
                    )
                    if success:
                        rolling_threads_started += 1
            
            result = {
                'confirmation_cycle': confirmation_result,
                'confirmed_signals': len(confirmed_signals),
                'rolling_threads_started': rolling_threads_started,
                'timestamp': datetime.now().isoformat()
            }
            
            if confirmed_signals:
                self.logger.info(f"✅ DB2 PROCESSING: {len(confirmed_signals)} signals confirmed, {rolling_threads_started} threads started")
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Error in DB2 processing: {e}")
            return {'error': str(e)}
    
    def get_priority_queue_for_data_fetch(self) -> Dict:
        """Get priority queue for realtime_data_fetcher - INTEGRATION POINT"""
        try:
            # Get priority status from DB1 pattern detector
            priority_status = db1_pattern_detector.get_priority_queue_status()
            
            # Format for realtime_data_fetcher compatibility
            priority_queue = {
                'GOLD': priority_status.get('symbols', {}).get('GOLD', []),
                'SILVER': priority_status.get('symbols', {}).get('SILVER', []),
                'BRONZE': priority_status.get('symbols', {}).get('BRONZE', []),
                'REMAINING': priority_status.get('symbols', {}).get('REMAINING', [])
            }
            
            return {
                'priority_queue': priority_queue,
                'counts': priority_status.get('counts', {}),
                'total_symbols': priority_status.get('total_symbols', 0)
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error getting priority queue: {e}")
            return {}
    
    def get_system_status_for_frontend(self) -> Dict:
        """Get complete system status for flask_app.py - INTEGRATION POINT"""
        try:
            # DB1 statistics
            db1_stats = {
                'signal_stats': perfect_db1_signal_generator.get_signal_statistics(),
                'transmission_stats': db1_signal_transmitter.get_transmission_statistics(),
                'priority_queue': db1_pattern_detector.get_priority_queue_status()
            }
            
            # DB2 statistics
            db2_stats = {
                'signal_stats': db2_signal_receiver.get_signal_statistics(),
                'confirmation_stats': db2_confirmation_engine.get_confirmation_statistics(),
                'data_stats': db2_data_collector.get_data_statistics()
            }
            
            # Recent signals for display
            recent_signals = perfect_db1_signal_generator.get_recent_signals(10)
            pending_confirmations = db2_signal_receiver.get_pending_signals()
            confirmed_signals = db2_signal_receiver.get_confirmed_signals()
            
            return {
                'db1_statistics': db1_stats,
                'db2_statistics': db2_stats,
                'recent_signals': recent_signals,
                'pending_confirmations': pending_confirmations,
                'confirmed_signals': confirmed_signals,
                'system_time': datetime.now().isoformat(),
                'integration_status': 'ACTIVE'
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error getting system status: {e}")
            return {'error': str(e)}
    
    def handle_15min_profit_monitoring(self) -> Dict:
        """Handle 15-minute profit monitoring for existing positions - INTEGRATION POINT"""
        try:
            self.logger.info("📈 HANDLING 15-MINUTE PROFIT MONITORING")
            
            # This integrates with existing trading_positions table
            # Get positions that need ₹800 profit monitoring
            import sqlite3
            
            conn = sqlite3.connect('Data/trading_operations.db', timeout=30.0)
            cursor = conn.cursor()
            
            # Get active positions that need profit monitoring
            cursor.execute('''
            SELECT symbol, buy_price, shares_quantity, current_profit
            FROM trading_positions 
            WHERE status = 'ACTIVE' AND current_profit < 800
            ''')
            
            positions = cursor.fetchall()
            conn.close()
            
            monitored_count = 0
            profit_800_reached = 0
            
            for symbol, buy_price, shares, current_profit in positions:
                # Calculate current profit (this would use latest price from DB1)
                # Mark ₹800 base points when profit reaches ₹800
                if current_profit >= 800:
                    # Mark as ₹800 base point in DB2
                    success = db2_data_collector.mark_800_base_point(
                        symbol=symbol,
                        price=buy_price + (800 / shares),  # Price at ₹800 profit
                        timestamp=datetime.now()
                    )
                    if success:
                        profit_800_reached += 1
                
                monitored_count += 1
            
            result = {
                'positions_monitored': monitored_count,
                'profit_800_reached': profit_800_reached,
                'timestamp': datetime.now().isoformat()
            }
            
            if profit_800_reached > 0:
                self.logger.info(f"📈 PROFIT MONITORING: {profit_800_reached} positions reached ₹800")
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Error in profit monitoring: {e}")
            return {'error': str(e)}

# Global instance for integration
perfect_system_integration = PerfectSystemIntegration()
