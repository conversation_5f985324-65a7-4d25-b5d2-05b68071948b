#!/usr/bin/env python3
"""
Test the previously missing endpoints
"""

import requests
import json

def test_endpoints():
    # Test the missing endpoints
    endpoints = [
        'http://localhost:5000/api/paper-trading/records',
        'http://localhost:5000/api/confirmation-status', 
        'http://localhost:5000/api/paper-trades',
        'http://localhost:5000/api/data-integrity/status'
    ]

    print('🔄 Testing previously missing endpoints...')
    success_count = 0
    
    for endpoint in endpoints:
        try:
            response = requests.get(endpoint, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                print(f'✅ {endpoint}: SUCCESS')
                if 'count' in data:
                    print(f'   📊 Count: {data["count"]}')
                elif 'success' in data:
                    print(f'   📊 Success: {data["success"]}')
                success_count += 1
            else:
                print(f'❌ {endpoint}: HTTP {response.status_code}')
        except Exception as e:
            print(f'❌ {endpoint}: ERROR - {str(e)}')

    print(f'\n🎉 Results: {success_count}/{len(endpoints)} endpoints working!')
    
    # Test POST endpoint
    print('\n🔄 Testing POST endpoint...')
    try:
        response = requests.post('http://localhost:5000/api/data-integrity/comprehensive-check', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f'✅ comprehensive-check: SUCCESS')
            print(f'   📊 Success: {data["success"]}')
        else:
            print(f'❌ comprehensive-check: HTTP {response.status_code}')
    except Exception as e:
        print(f'❌ comprehensive-check: ERROR - {str(e)}')

if __name__ == '__main__':
    test_endpoints()
