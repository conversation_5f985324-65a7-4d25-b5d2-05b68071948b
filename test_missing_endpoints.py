#!/usr/bin/env python3
"""
Test the previously missing endpoints
"""

import requests
import json

def test_endpoints():
    # Test the NEW CLEAN endpoints
    endpoints = [
        'http://localhost:5000/api/db1/signals',
        'http://localhost:5000/api/active-positions',
        'http://localhost:5000/api/symbols',
        'http://localhost:5000/api/database-schema',
        'http://localhost:5000/api/data-integrity',
        'http://localhost:5000/api/priority-queue/status',
        'http://localhost:5000/api/db2/statistics',
        'http://localhost:5000/api/db2/trading-positions',
        'http://localhost:5000/api/layer2-confirmations'
    ]

    print('🔄 Testing previously missing endpoints...')
    success_count = 0
    
    for endpoint in endpoints:
        try:
            response = requests.get(endpoint, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                print(f'✅ {endpoint}: SUCCESS')
                if 'count' in data:
                    print(f'   📊 Count: {data["count"]}')
                elif 'success' in data:
                    print(f'   📊 Success: {data["success"]}')
                success_count += 1
            else:
                print(f'❌ {endpoint}: HTTP {response.status_code}')
        except Exception as e:
            print(f'❌ {endpoint}: ERROR - {str(e)}')

    print(f'\n🎉 Results: {success_count}/{len(endpoints)} endpoints working!')
    
    # Test POST endpoints
    print('\n🔄 Testing POST endpoints...')
    post_endpoints = [
        'http://localhost:5000/api/scan-duplicates',
        'http://localhost:5000/api/start-trading',
        'http://localhost:5000/api/stop-trading',
        'http://localhost:5000/api/start-data-fetch'
    ]

    for endpoint in post_endpoints:
        try:
            response = requests.post(endpoint, timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f'✅ {endpoint}: SUCCESS')
                if 'success' in data:
                    print(f'   📊 Success: {data["success"]}')
            else:
                print(f'❌ {endpoint}: HTTP {response.status_code}')
        except Exception as e:
            print(f'❌ {endpoint}: ERROR - {str(e)}')

if __name__ == '__main__':
    test_endpoints()
