import sqlite3

# Check if table exists detection is working
conn = sqlite3.connect('Data/trading_operations.db')
cursor = conn.cursor()

# Check table existence
cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='db2_signals_received'")
table_exists = cursor.fetchone()

print(f"Table exists check result: {table_exists}")
print(f"Table exists boolean: {bool(table_exists)}")

if table_exists:
    print("✅ Table exists - using new logic")
    
    # Test the exact queries from the API
    cursor.execute('SELECT COUNT(*) FROM db2_signals_received WHERE status IN ("RECEIVED", "CONFIRMING")')
    total_signals = cursor.fetchone()[0]
    print(f"Total signals (pending): {total_signals}")
    
    cursor.execute("SELECT COUNT(*) FROM db2_signals_received WHERE status = 'CONFIRMING'")
    pending_buy = cursor.fetchone()[0]
    print(f"Pending BUY: {pending_buy}")
    
    cursor.execute("SELECT COUNT(*) FROM trading_positions WHERE status = 'ACTIVE'")
    active_positions = cursor.fetchone()[0]
    print(f"Active positions: {active_positions}")
    
    # Check profit calculation
    cursor.execute('''
    SELECT symbol, buy_price, shares_quantity, investment 
    FROM trading_positions 
    WHERE status = 'ACTIVE'
    ''')
    
    positions = cursor.fetchall()
    total_profit = 0
    
    for symbol, buy_price, shares, investment in positions:
        cursor.execute('''
        SELECT close_price FROM db2_trading_data 
        WHERE symbol = ? 
        ORDER BY timestamp DESC 
        LIMIT 1
        ''', (symbol,))
        
        current_price_result = cursor.fetchone()
        if current_price_result:
            current_price = current_price_result[0]
            current_profit = (current_price - buy_price) * shares
            total_profit += current_profit
            print(f"Profit calculation: {symbol} - ₹{current_profit:.2f}")
    
    print(f"Total profit: ₹{total_profit:.2f}")
    
else:
    print("❌ Table doesn't exist - using fallback logic")

conn.close()
