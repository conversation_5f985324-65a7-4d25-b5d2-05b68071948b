/**
 * NEW TRADING DASHBOARD - Complete Frontend for DB1/DB2 System
 * Handles all tabs: Active Positions, Symbol Explorer, SQL Query, Data Integrity, Duplicates, Paper Trading
 */

class NewTradingDashboard {
    constructor() {
        this.socket = null;
        this.autoRefreshIntervals = {};
        this.init();
    }

    init() {
        this.initializeSocket();
        this.initializeEventListeners();
        this.loadInitialData();
        this.setupAutoRefresh();
    }

    initializeSocket() {
        this.socket = io();
        
        this.socket.on('connect', () => {
            this.updateConnectionStatus(true);
            this.addLog('SYSTEM', 'Connected to trading server', 'success');
        });

        this.socket.on('disconnect', () => {
            this.updateConnectionStatus(false);
            this.addLog('SYSTEM', 'Disconnected from trading server', 'danger');
        });
    }

    initializeEventListeners() {
        // Trading control buttons
        document.getElementById('startTradingBtn')?.addEventListener('click', () => this.startTrading());
        document.getElementById('stopTradingBtn')?.addEventListener('click', () => this.stopTrading());
        document.getElementById('startDataFetchBtn')?.addEventListener('click', () => this.startDataFetch());

        // Tab switching
        document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tab => {
            tab.addEventListener('shown.bs.tab', (e) => {
                const target = e.target.getAttribute('data-bs-target');
                this.handleTabSwitch(target);
            });
        });

        // SQL Query tab
        document.getElementById('executeQueryBtn')?.addEventListener('click', () => this.executeQuery());
        document.getElementById('clearQueryBtn')?.addEventListener('click', () => this.clearQuery());
        document.getElementById('queryTemplates')?.addEventListener('change', (e) => this.loadQueryTemplate(e.target.value));

        // Data Integrity tab
        document.getElementById('runQuickIntegrityCheck')?.addEventListener('click', () => this.runQuickIntegrityCheck());
        document.getElementById('runComprehensiveCheck')?.addEventListener('click', () => this.runComprehensiveCheck());
        document.getElementById('refreshPriorityQueue')?.addEventListener('click', () => this.refreshPriorityQueue());

        // Duplicates tab
        document.getElementById('scanDuplicatesNow')?.addEventListener('click', () => this.scanDuplicates());
        document.getElementById('removeDuplicatesNow')?.addEventListener('click', () => this.removeDuplicates());

        // Paper Trading tab
        document.getElementById('refreshLayer2Confirmations')?.addEventListener('click', () => this.refreshLayer2Confirmations());
    }

    setupAutoRefresh() {
        // Auto-refresh Active Positions every 15 seconds
        this.autoRefreshIntervals.positions = setInterval(() => {
            if (this.isTabActive('#positions')) {
                this.refreshActivePositions();
            }
        }, 15000);

        // Auto-refresh Paper Trading every 10 seconds
        this.autoRefreshIntervals.paperTrading = setInterval(() => {
            if (this.isTabActive('#paper-trading')) {
                this.refreshPaperTrading();
            }
        }, 10000);

        // Auto-refresh Priority Queue every 30 seconds
        this.autoRefreshIntervals.priority = setInterval(() => {
            if (this.isTabActive('#integrity')) {
                this.refreshPriorityQueue();
            }
        }, 30000);
    }

    isTabActive(tabId) {
        const tab = document.querySelector(`${tabId}.show.active`);
        return tab !== null;
    }

    async loadInitialData() {
        await this.refreshActivePositions();
        await this.loadDatabaseSchema();
        await this.refreshPriorityQueue();
        await this.refreshPaperTrading();
    }

    handleTabSwitch(target) {
        switch(target) {
            case '#positions':
                this.refreshActivePositions();
                break;
            case '#data-logs':
                this.initializeSymbolExplorer();
                break;
            case '#sql':
                this.initializeSqlTab();
                break;
            case '#integrity':
                this.refreshPriorityQueue();
                break;
            case '#duplicates':
                this.initializeDuplicatesTab();
                break;
            case '#paper-trading':
                this.refreshPaperTrading();
                break;
        }
    }

    // ============================================================================
    // ACTIVE POSITIONS TAB
    // ============================================================================

    async refreshActivePositions() {
        try {
            // Get DB1 signals (Active Signals)
            const signalsResponse = await fetch('/api/db1/signals');
            const signalsData = await signalsResponse.json();

            if (signalsData.success) {
                this.updateActiveSignalsDisplay(signalsData);
            }

            // Get active positions
            const positionsResponse = await fetch('/api/active-positions');
            const positionsData = await positionsResponse.json();

            if (positionsData.success) {
                this.updateActivePositionsTable(positionsData.positions);
                this.updatePositionsSummary(positionsData.positions);
            }

            this.updateLastRefreshTime('positionsLastUpdate');

        } catch (error) {
            console.error('Error refreshing active positions:', error);
            this.showError('Failed to refresh active positions');
        }
    }

    updateActiveSignalsDisplay(data) {
        const summaryElement = document.getElementById('positionsSummary');
        if (!summaryElement) return;

        // Update Active Signals count
        const activeSignalsElement = summaryElement.querySelector('#totalActivePositions');
        if (activeSignalsElement) {
            activeSignalsElement.textContent = data.count || 0;
        }

        // Update other metrics based on signals
        const totalInvestmentElement = summaryElement.querySelector('#totalInvestment');
        if (totalInvestmentElement) {
            const estimatedInvestment = (data.count || 0) * 100000; // ₹1L per signal
            totalInvestmentElement.textContent = `₹${(estimatedInvestment / 100000).toFixed(1)}L`;
        }
    }

    updateActivePositionsTable(positions) {
        const tableBody = document.getElementById('positionsTableBody');
        if (!tableBody) return;

        if (!positions || positions.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="11" class="text-center text-muted py-4">
                        <i class="fas fa-info-circle me-2"></i>
                        No active positions. DB1 signals will appear here when generated.
                    </td>
                </tr>
            `;
            return;
        }

        tableBody.innerHTML = positions.map(position => `
            <tr>
                <td><strong>${position.symbol}</strong></td>
                <td>${position.buy_price}</td>
                <td><span class="text-muted">Real-time</span></td>
                <td>${position.shares_quantity}</td>
                <td>${position.investment}</td>
                <td><span class="text-muted">Calculating...</span></td>
                <td>${position.target_price}</td>
                <td class="${position.current_pnl?.startsWith('-') ? 'text-danger' : 'text-success'}">${position.current_pnl}</td>
                <td class="${position.pnl_percentage?.startsWith('-') ? 'text-danger' : 'text-success'}">${position.pnl_percentage}</td>
                <td><small>${position.buy_time}</small></td>
                <td><span class="badge bg-${position.status === 'ACTIVE' ? 'success' : 'warning'}">${position.status}</span></td>
            </tr>
        `).join('');
    }

    updatePositionsSummary(positions) {
        const summaryElement = document.getElementById('positionsSummary');
        if (!summaryElement) return;

        const totalPositions = positions.length;
        const totalInvestment = positions.reduce((sum, pos) => {
            const investment = parseFloat(pos.investment?.replace(/[₹,]/g, '') || 0);
            return sum + investment;
        }, 0);

        // Update summary metrics
        const elements = {
            'totalActivePositions': totalPositions,
            'totalInvestment': `₹${(totalInvestment / 100000).toFixed(1)}L`,
            'currentValue': `₹${(totalInvestment / 100000).toFixed(1)}L`, // Placeholder
            'totalPnL': '₹0', // Placeholder
            'targetsAchieved': 0, // Placeholder
            'targetsRemaining': totalPositions
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = summaryElement.querySelector(`#${id}`);
            if (element) element.textContent = value;
        });
    }

    // ============================================================================
    // SYMBOL EXPLORER TAB
    // ============================================================================

    async initializeSymbolExplorer() {
        try {
            // Load available symbols
            const response = await fetch('/api/symbols');
            const data = await response.json();

            if (data.success) {
                this.populateSymbolDropdown(data.symbols);
            }
        } catch (error) {
            console.error('Error initializing symbol explorer:', error);
        }
    }

    populateSymbolDropdown(symbols) {
        const select = document.getElementById('symbolSelect');
        if (!select) return;

        select.innerHTML = '<option value="">Select a symbol...</option>' +
            symbols.map(symbol => `<option value="${symbol}">${symbol}</option>`).join('');
    }

    async loadSymbolData() {
        const symbolSelect = document.getElementById('symbolSelect');
        const symbol = symbolSelect?.value;

        if (!symbol) return;

        try {
            const response = await fetch(`/api/symbol-data/${symbol}`);
            const data = await response.json();

            if (data.success) {
                this.displaySymbolData(data);
            }
        } catch (error) {
            console.error('Error loading symbol data:', error);
        }
    }

    displaySymbolData(data) {
        const container = document.getElementById('symbolDataContainer');
        if (!container) return;

        container.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>📊 DB1 Data (Last 25 Points)</h6>
                    <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                        <table class="table table-sm table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>Time</th>
                                    <th>Open</th>
                                    <th>High</th>
                                    <th>Low</th>
                                    <th>Close</th>
                                    <th>Volume</th>
                                    <th>F/R</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${data.db1_data.map(row => `
                                    <tr>
                                        <td><small>${row.timestamp}</small></td>
                                        <td>₹${row.open}</td>
                                        <td>₹${row.high}</td>
                                        <td>₹${row.low}</td>
                                        <td>₹${row.close}</td>
                                        <td>${row.volume}</td>
                                        <td><span class="badge bg-${row.fr_movement === 'F' ? 'danger' : row.fr_movement === 'R' ? 'success' : 'secondary'}">${row.fr_movement || 'N/A'}</span></td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="col-md-6">
                    <h6>💰 DB2 Data</h6>
                    <div class="mb-3">
                        <h7>Signals (${data.db2_signals_count})</h7>
                        <div class="table-responsive" style="max-height: 200px; overflow-y: auto;">
                            <table class="table table-sm">
                                <thead>
                                    <tr><th>Type</th><th>Price</th><th>Status</th><th>Time</th></tr>
                                </thead>
                                <tbody>
                                    ${data.db2_signals.map(signal => `
                                        <tr>
                                            <td>${signal.signal_type}</td>
                                            <td>₹${signal.signal_price}</td>
                                            <td><span class="badge bg-warning">${signal.confirmation_status}</span></td>
                                            <td><small>${signal.received_at}</small></td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div>
                        <h7>Positions (${data.db2_positions_count})</h7>
                        <div class="table-responsive" style="max-height: 200px; overflow-y: auto;">
                            <table class="table table-sm">
                                <thead>
                                    <tr><th>Buy</th><th>Sell</th><th>Qty</th><th>Status</th></tr>
                                </thead>
                                <tbody>
                                    ${data.db2_positions.map(pos => `
                                        <tr>
                                            <td>₹${pos.buy_price}</td>
                                            <td>${pos.sell_price ? '₹' + pos.sell_price : 'N/A'}</td>
                                            <td>${pos.quantity}</td>
                                            <td><span class="badge bg-${pos.status === 'ACTIVE' ? 'success' : 'info'}">${pos.status}</span></td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // ============================================================================
    // UTILITY FUNCTIONS
    // ============================================================================

    updateLastRefreshTime(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = `Last updated: ${new Date().toLocaleTimeString()}`;
        }
    }

    showError(message) {
        console.error(message);
        // You can add a toast notification here
    }

    addLog(category, message, type) {
        console.log(`[${category}] ${message}`);
        // You can add to a log display here
    }

    updateConnectionStatus(connected) {
        const statusIndicator = document.getElementById('connectionStatus');
        const statusText = document.getElementById('connectionText');
        
        if (statusIndicator && statusText) {
            if (connected) {
                statusIndicator.className = 'status-indicator status-active';
                statusText.textContent = 'Connected';
            } else {
                statusIndicator.className = 'status-indicator status-inactive';
                statusText.textContent = 'Disconnected';
            }
        }
    }

    // ============================================================================
    // SQL QUERY TAB
    // ============================================================================

    async initializeSqlTab() {
        await this.loadDatabaseSchema();
        this.setupQueryTemplates();
    }

    async loadDatabaseSchema() {
        try {
            const response = await fetch('/api/database-schema');
            const data = await response.json();

            if (data.success) {
                this.displayDatabaseSchema(data.schema);
            }
        } catch (error) {
            console.error('Error loading database schema:', error);
        }
    }

    displayDatabaseSchema(schema) {
        const container = document.getElementById('databaseSchema');
        if (!container) return;

        let html = '';

        // DB1 Schema
        html += '<div class="mb-3"><h6 class="text-primary">📊 DB1 (trading_data.db)</h6>';
        Object.entries(schema.db1 || {}).forEach(([table, columns]) => {
            html += `<div class="mb-2">
                <strong>${table}</strong>
                <ul class="list-unstyled ms-3">
                    ${columns.map(col => `<li><code>${col.name}</code> <small class="text-muted">${col.type}</small></li>`).join('')}
                </ul>
            </div>`;
        });
        html += '</div>';

        // DB2 Schema
        html += '<div class="mb-3"><h6 class="text-success">💰 DB2 (trading_operations.db)</h6>';
        Object.entries(schema.db2 || {}).forEach(([table, columns]) => {
            html += `<div class="mb-2">
                <strong>${table}</strong>
                <ul class="list-unstyled ms-3">
                    ${columns.map(col => `<li><code>${col.name}</code> <small class="text-muted">${col.type}</small></li>`).join('')}
                </ul>
            </div>`;
        });
        html += '</div>';

        container.innerHTML = html;
    }

    setupQueryTemplates() {
        const templates = {
            'db1_recent_data': 'SELECT symbol, timestamp, close_price, fr_movement FROM trading_data ORDER BY timestamp DESC LIMIT 10',
            'db1_signals': 'SELECT * FROM trading_signals ORDER BY created_at DESC LIMIT 10',
            'db1_fr_summary': 'SELECT symbol, fr_movement, COUNT(*) as count FROM trading_data WHERE fr_movement IS NOT NULL GROUP BY symbol, fr_movement',
            'db2_pending': 'SELECT * FROM db2_signals_received WHERE confirmation_status = "PENDING"',
            'db2_positions': 'SELECT * FROM trading_positions ORDER BY buy_time DESC LIMIT 10',
            'db2_statistics': 'SELECT status, COUNT(*) as count, SUM(profit_amount) as total_profit FROM trading_positions GROUP BY status'
        };

        const select = document.getElementById('queryTemplates');
        if (select) {
            select.innerHTML = '<option value="">Select template...</option>' +
                Object.entries(templates).map(([key, query]) =>
                    `<option value="${key}">${key.replace(/_/g, ' ').toUpperCase()}</option>`
                ).join('');
        }

        this.queryTemplates = templates;
    }

    async loadQueryTemplate(templateKey) {
        if (!templateKey || !this.queryTemplates) return;

        const query = this.queryTemplates[templateKey];
        const textarea = document.getElementById('sqlQuery');
        if (textarea && query) {
            textarea.value = query;
        }
    }

    async executeQuery() {
        const textarea = document.getElementById('sqlQuery');
        const query = textarea?.value?.trim();

        if (!query) {
            this.showQueryStatus('Please enter a query', 'warning');
            return;
        }

        try {
            this.showQueryStatus('Executing query...', 'info');

            const database = query.toLowerCase().includes('trading_positions') ||
                           query.toLowerCase().includes('db2_signals') ? 'db2' : 'db1';

            const response = await fetch('/api/execute-sql', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ query, database })
            });

            const data = await response.json();

            if (data.success) {
                this.displayQueryResults(data);
                this.showQueryStatus(`Query executed successfully. ${data.row_count} rows returned.`, 'success');
            } else {
                this.showQueryStatus(`Query failed: ${data.error}`, 'danger');
            }
        } catch (error) {
            console.error('Error executing query:', error);
            this.showQueryStatus('Error executing query', 'danger');
        }
    }

    displayQueryResults(data) {
        const container = document.getElementById('queryResults');
        if (!container) return;

        if (!data.results || data.results.length === 0) {
            container.innerHTML = '<div class="text-center text-muted">No results found</div>';
            return;
        }

        const table = `
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            ${data.columns.map(col => `<th>${col}</th>`).join('')}
                        </tr>
                    </thead>
                    <tbody>
                        ${data.results.map(row => `
                            <tr>
                                ${data.columns.map(col => `<td>${row[col] || 'NULL'}</td>`).join('')}
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;

        container.innerHTML = table;

        // Show export button
        const exportBtn = document.getElementById('exportCsvBtn');
        if (exportBtn) {
            exportBtn.style.display = 'inline-block';
        }
    }

    showQueryStatus(message, type) {
        const statusElement = document.getElementById('queryStatus');
        if (!statusElement) return;

        statusElement.className = `alert alert-${type}`;
        statusElement.textContent = message;
        statusElement.classList.remove('d-none');

        // Auto-hide after 5 seconds
        setTimeout(() => {
            statusElement.classList.add('d-none');
        }, 5000);
    }

    clearQuery() {
        const textarea = document.getElementById('sqlQuery');
        if (textarea) textarea.value = '';

        const results = document.getElementById('queryResults');
        if (results) results.innerHTML = '<div class="text-center text-muted">Execute a query to see results</div>';

        const exportBtn = document.getElementById('exportCsvBtn');
        if (exportBtn) exportBtn.style.display = 'none';
    }

    // ============================================================================
    // DATA INTEGRITY TAB
    // ============================================================================

    async refreshPriorityQueue() {
        try {
            const response = await fetch('/api/priority-queue/status');
            const data = await response.json();

            if (data.success) {
                // Handle both old and new data structures
                const priorityQueue = data.priority_queue || data;
                this.displayPriorityQueue(priorityQueue);
            } else {
                this.showPriorityQueueError(data.error || 'Unknown error');
            }

            this.updateLastRefreshTime('priorityTimestamp');
        } catch (error) {
            console.error('Error refreshing priority queue:', error);
            this.showPriorityQueueError(`Failed to load priority queue: ${error.message}`);
        }
    }

    displayPriorityQueue(priorityQueue) {
        const container = document.getElementById('priorityBatches');
        if (!container) return;

        const priorities = ['GOLD', 'SILVER', 'BRONZE', 'REMAINING'];
        const colors = { GOLD: 'warning', SILVER: 'secondary', BRONZE: 'info', REMAINING: 'light' };

        // Handle both old and new data structures
        const queueData = priorityQueue.priority_queue || priorityQueue;

        container.innerHTML = priorities.map(priority => {
            const symbols = queueData[priority] || [];
            return `
                <div class="col-md-3">
                    <div class="card border-${colors[priority]}">
                        <div class="card-header bg-${colors[priority]} text-dark">
                            <h6 class="mb-0">${priority}</h6>
                        </div>
                        <div class="card-body">
                            <div class="metric-value">${symbols.length}</div>
                            <div class="metric-label">Symbols</div>
                            ${symbols.length > 0 ? `
                                <div class="mt-2">
                                    <small class="text-muted">
                                        ${symbols.slice(0, 3).join(', ')}
                                        ${symbols.length > 3 ? `... +${symbols.length - 3} more` : ''}
                                    </small>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }

    showPriorityQueueError(error) {
        const container = document.getElementById('priorityBatches');
        if (container) {
            container.innerHTML = `
                <div class="col-12">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Failed to load priority queue: ${error}
                    </div>
                </div>
            `;
        }
    }

    async runQuickIntegrityCheck() {
        try {
            console.log('⚡ Running quick integrity check...');

            const container = document.getElementById('integrityResults');
            if (container) {
                container.innerHTML = `
                    <div class="alert alert-info">
                        <h6><i class="fas fa-play me-2"></i>Quick Integrity Check</h6>
                        <div class="text-center mt-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Running quick data integrity check...</p>
                        </div>
                    </div>
                `;
            }

            const response = await fetch('/api/data-integrity');
            const data = await response.json();

            if (data.success) {
                this.displayIntegrityResults(data.integrity_status, 'Quick Check');
                this.showAlert('Quick check completed successfully', 'success');
            } else {
                this.showAlert(`Quick check failed: ${data.error}`, 'danger');
            }
        } catch (error) {
            console.error('Error running quick integrity check:', error);
            this.showAlert('Quick check failed', 'danger');
        }
    }

    async runComprehensiveCheck() {
        try {
            console.log('🔍 Running comprehensive check...');

            const container = document.getElementById('integrityResults');
            if (container) {
                container.innerHTML = `
                    <div class="alert alert-info">
                        <h6><i class="fas fa-search me-2"></i>Comprehensive Check</h6>
                        <div class="text-center mt-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Running comprehensive data integrity check...</p>
                            <small class="text-muted">✅ Checking for missing 25 intervals per symbol</small>
                        </div>
                    </div>
                `;
            }

            const response = await fetch('/api/comprehensive-check', { method: 'POST' });
            const data = await response.json();

            if (data.success) {
                this.displayIntegrityResults(data.integrity_status, 'Comprehensive Check');
                this.showAlert(data.message || 'Comprehensive check completed successfully', 'success');
            } else {
                this.showAlert(`Comprehensive check failed: ${data.error}`, 'danger');
                if (container) {
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Comprehensive Check Failed</h6>
                            <p>Error: ${data.error}</p>
                        </div>
                    `;
                }
            }
        } catch (error) {
            console.error('Error running comprehensive check:', error);
            this.showAlert('Comprehensive check failed', 'danger');

            const container = document.getElementById('integrityResults');
            if (container) {
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Comprehensive Check Failed</h6>
                        <p>Failed to run comprehensive check: ${error.message}</p>
                    </div>
                `;
            }
        }
    }

    displayIntegrityResults(status, checkType) {
        const container = document.getElementById('integrityResults');
        if (!container) return;

        container.innerHTML = `
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">${checkType} Results</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h7>DB1 Statistics</h7>
                            <ul class="list-unstyled">
                                <li>Total Records: <strong>${status.db1_total_records || 0}</strong></li>
                                <li>F/R Calculated: <strong>${status.db1_fr_calculated || 0}</strong></li>
                                <li>F/R Percentage: <strong>${status.db1_fr_percentage?.toFixed(1) || 0}%</strong></li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h7>DB2 Statistics</h7>
                            <ul class="list-unstyled">
                                <li>Signals Received: <strong>${status.db2_signals_received || 0}</strong></li>
                                <li>Trading Positions: <strong>${status.db2_trading_positions || 0}</strong></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    showAlert(message, type) {
        // Create a temporary alert
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Add to page
        const container = document.querySelector('.container-fluid');
        if (container) {
            container.insertBefore(alert, container.firstChild);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                alert.remove();
            }, 5000);
        }
    }

    // ============================================================================
    // DUPLICATES TAB
    // ============================================================================

    async initializeDuplicatesTab() {
        // Auto-scan for duplicates when tab is opened
        await this.scanDuplicates();
    }

    async scanDuplicates() {
        try {
            const response = await fetch('/api/scan-duplicates', { method: 'POST' });
            const data = await response.json();

            if (data.success) {
                this.displayDuplicateResults(data);
            }
        } catch (error) {
            console.error('Error scanning duplicates:', error);
        }
    }

    displayDuplicateResults(data) {
        const resultsContainer = document.getElementById('duplicateResults');
        const listContainer = document.getElementById('duplicatesList');

        if (resultsContainer) {
            resultsContainer.innerHTML = `
                <div class="alert alert-${data.total_duplicates > 0 ? 'warning' : 'success'}">
                    <h6><i class="fas fa-search me-2"></i>Duplicate Scan Results</h6>
                    <p class="mb-2">
                        <strong>DB1 Duplicates:</strong> ${data.db1_count}<br>
                        <strong>DB2 Duplicates:</strong> ${data.db2_count}<br>
                        <strong>Total:</strong> ${data.total_duplicates}
                    </p>
                    ${data.total_duplicates > 0 ?
                        '<p class="mb-0"><small>Click "Remove Duplicates" to clean up the database.</small></p>' :
                        '<p class="mb-0"><small>No duplicates found. Database is clean!</small></p>'
                    }
                </div>
            `;
        }

        if (listContainer) {
            if (data.total_duplicates === 0) {
                listContainer.innerHTML = `
                    <div class="text-center text-success">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <h6>No Duplicates Found</h6>
                        <p class="text-muted">Your database is clean!</p>
                    </div>
                `;
            } else {
                let html = '';

                if (data.db1_duplicates.length > 0) {
                    html += '<h6>DB1 Duplicates</h6>';
                    html += '<div class="table-responsive mb-3">';
                    html += '<table class="table table-sm table-striped">';
                    html += '<thead><tr><th>Symbol</th><th>Timestamp</th><th>Count</th></tr></thead><tbody>';
                    data.db1_duplicates.forEach(dup => {
                        html += `<tr><td>${dup.symbol}</td><td>${dup.timestamp}</td><td><span class="badge bg-warning">${dup.count}</span></td></tr>`;
                    });
                    html += '</tbody></table></div>';
                }

                if (data.db2_duplicates.length > 0) {
                    html += '<h6>DB2 Duplicates</h6>';
                    html += '<div class="table-responsive">';
                    html += '<table class="table table-sm table-striped">';
                    html += '<thead><tr><th>Symbol</th><th>Received At</th><th>Count</th></tr></thead><tbody>';
                    data.db2_duplicates.forEach(dup => {
                        html += `<tr><td>${dup.symbol}</td><td>${dup.received_at}</td><td><span class="badge bg-warning">${dup.count}</span></td></tr>`;
                    });
                    html += '</tbody></table></div>';
                }

                listContainer.innerHTML = html;
            }
        }
    }

    async removeDuplicates() {
        if (!confirm('Are you sure you want to remove all duplicate records? This action cannot be undone.')) {
            return;
        }

        try {
            // This would need a backend endpoint to actually remove duplicates
            this.showAlert('Duplicate removal feature will be implemented in backend', 'info');
        } catch (error) {
            console.error('Error removing duplicates:', error);
        }
    }

    // ============================================================================
    // PAPER TRADING TAB
    // ============================================================================

    async refreshPaperTrading() {
        await this.refreshDB2Statistics();
        await this.refreshLayer2Confirmations();
        await this.refreshTradingRecords();
    }

    async refreshDB2Statistics() {
        try {
            const response = await fetch('/api/db2/statistics');
            const data = await response.json();

            if (data.success) {
                this.updateDB2Statistics(data.statistics);
            }
        } catch (error) {
            console.error('Error refreshing DB2 statistics:', error);
        }
    }

    updateDB2Statistics(stats) {
        const elements = {
            'db2TotalSignals': stats.total_signals_received || 0,
            'db2PendingBuy': stats.pending_confirmations || 0,
            'db2PendingSell': 0, // Placeholder
            'db2ReadyExecution': 0, // Placeholder
            'db2ActivePositions': stats.active_positions || 0,
            'db2TotalProfit': stats.total_profit || '₹0'
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) element.textContent = value;
        });
    }

    async refreshLayer2Confirmations() {
        try {
            const response = await fetch('/api/layer2-confirmations');
            const data = await response.json();

            if (data.success) {
                this.displayLayer2Confirmations(data.confirmations);
            }

            this.updateLastRefreshTime('layer2LastUpdate');
        } catch (error) {
            console.error('Error refreshing layer2 confirmations:', error);
        }
    }

    displayLayer2Confirmations(confirmations) {
        const container = document.getElementById('layer2ConfirmationsTable');
        if (!container) return;

        if (!confirmations || confirmations.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-info-circle me-2"></i>
                    No pending confirmations. DB1 signals will appear here for RR/FF confirmation.
                </div>
            `;
            return;
        }

        const table = `
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Symbol</th>
                            <th>Signal Price</th>
                            <th>Status</th>
                            <th>Type</th>
                            <th>Timestamp</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${confirmations.map(conf => `
                            <tr>
                                <td><strong>${conf.symbol}</strong></td>
                                <td>${conf.signal_price}</td>
                                <td><span class="badge bg-warning">${conf.status}</span></td>
                                <td><span class="badge bg-info">${conf.type}</span></td>
                                <td><small>${conf.timestamp}</small></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewConfirmationDetails('${conf.symbol}')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;

        container.innerHTML = table;
    }

    async refreshTradingRecords() {
        try {
            const response = await fetch('/api/db2/trading-positions');
            const data = await response.json();

            if (data.success) {
                this.updateTradingRecords(data.positions);
                this.updateTradingStatistics(data.positions);
            }
        } catch (error) {
            console.error('Error refreshing trading records:', error);
        }
    }

    updateTradingRecords(positions) {
        const tableBody = document.getElementById('paperTradesTableBody');
        if (!tableBody) return;

        if (!positions || positions.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center text-muted py-4">
                        <i class="fas fa-info-circle me-2"></i>
                        No trading records yet. Positions will appear here after DB2 execution.
                    </td>
                </tr>
            `;
            return;
        }

        tableBody.innerHTML = positions.map(position => `
            <tr>
                <td><strong>${position.symbol}</strong></td>
                <td>${position.buy_price}</td>
                <td>${position.sell_price}</td>
                <td>${position.shares_quantity}</td>
                <td>${position.investment}</td>
                <td class="${position.final_profit?.startsWith('-') ? 'text-danger' : 'text-success'}">${position.final_profit}</td>
                <td><span class="badge bg-${position.status === 'ACTIVE' ? 'success' : position.status === 'COMPLETED' ? 'info' : 'warning'}">${position.status}</span></td>
                <td><small>${position.buy_timestamp}</small></td>
                <td>
                    ${position.status === 'ACTIVE' ?
                        `<button class="btn btn-sm btn-outline-danger" onclick="manualSell('${position.symbol}')">
                            <i class="fas fa-hand-paper"></i> Manual Sell
                        </button>` :
                        '<span class="text-muted">Completed</span>'
                    }
                </td>
            </tr>
        `).join('');
    }

    updateTradingStatistics(positions) {
        const totalTrades = positions.length;
        const completedTrades = positions.filter(p => p.status === 'COMPLETED').length;
        const activeTrades = positions.filter(p => p.status === 'ACTIVE').length;
        const profitableTrades = positions.filter(p => p.final_profit && parseFloat(p.final_profit.replace(/[₹,]/g, '')) > 0).length;

        const totalProfit = positions.reduce((sum, pos) => {
            if (pos.final_profit) {
                const profit = parseFloat(pos.final_profit.replace(/[₹,]/g, ''));
                return sum + (isNaN(profit) ? 0 : profit);
            }
            return sum;
        }, 0);

        const avgProfit = totalTrades > 0 ? totalProfit / totalTrades : 0;

        const elements = {
            'totalPaperTrades': totalTrades,
            'completedTrades': completedTrades,
            'activeTrades': activeTrades,
            'profitableTrades': profitableTrades,
            'avgProfit': `₹${avgProfit.toFixed(0)}`,
            'uniqueSymbolsTraded': new Set(positions.map(p => p.symbol)).size
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) element.textContent = value;
        });
    }

    // ============================================================================
    // TRADING CONTROL FUNCTIONS
    // ============================================================================

    async startTrading() {
        try {
            const response = await fetch('/api/start-trading', { method: 'POST' });
            const data = await response.json();

            if (data.success) {
                this.showAlert('Trading system started successfully', 'success');
            } else {
                this.showAlert(`Failed to start trading: ${data.message}`, 'danger');
            }
        } catch (error) {
            console.error('Error starting trading:', error);
            this.showAlert('Error starting trading system', 'danger');
        }
    }

    async stopTrading() {
        try {
            const response = await fetch('/api/stop-trading', { method: 'POST' });
            const data = await response.json();

            if (data.success) {
                this.showAlert('Trading system stopped successfully', 'success');
            } else {
                this.showAlert(`Failed to stop trading: ${data.message}`, 'danger');
            }
        } catch (error) {
            console.error('Error stopping trading:', error);
            this.showAlert('Error stopping trading system', 'danger');
        }
    }

    async startDataFetch() {
        try {
            const response = await fetch('/api/start-data-fetch', { method: 'POST' });
            const data = await response.json();

            if (data.success) {
                this.showAlert('Data fetching started successfully', 'success');
            } else {
                this.showAlert(`Failed to start data fetch: ${data.message}`, 'danger');
            }
        } catch (error) {
            console.error('Error starting data fetch:', error);
            this.showAlert('Error starting data fetch', 'danger');
        }
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.tradingDashboard = new NewTradingDashboard();
});

// Global functions for HTML onclick events
function refreshActivePositions() {
    window.tradingDashboard?.refreshActivePositions();
}

function refreshPriorityQueue() {
    window.tradingDashboard?.refreshPriorityQueue();
}

function refreshLayer2Confirmations() {
    window.tradingDashboard?.refreshLayer2Confirmations();
}

function runQuickIntegrityCheck() {
    window.tradingDashboard?.runQuickIntegrityCheck();
}

function runComprehensiveCheck() {
    window.tradingDashboard?.runComprehensiveCheck();
}

function scanDuplicatesNow() {
    window.tradingDashboard?.scanDuplicates();
}

function removeDuplicatesNow() {
    window.tradingDashboard?.removeDuplicates();
}

// Additional global functions for HTML onclick events
function getIntegrityStatus() {
    window.tradingDashboard?.runQuickIntegrityCheck();
}

function runPriorityAnalysis() {
    window.tradingDashboard?.refreshPriorityQueue();
}

function stopAutoFetch() {
    window.tradingDashboard?.stopDataFetch();
}

function refreshIntegrityData() {
    window.tradingDashboard?.refreshPriorityQueue();
}

function analyzePriorityPatterns() {
    window.tradingDashboard?.refreshPriorityQueue();
}

function showPriorityDetails() {
    window.tradingDashboard?.refreshPriorityQueue();
}

function downloadPaperTrades() {
    // Export paper trading records to CSV
    console.log('Downloading paper trades...');
}

function clearPaperTrades() {
    if (confirm('Are you sure you want to clear all paper trading records?')) {
        console.log('Clearing paper trades...');
    }
}

function completeReset() {
    if (confirm('⚠️ DANGER: This will reset ALL trading data. Are you absolutely sure?')) {
        if (confirm('This action cannot be undone. Proceed with complete reset?')) {
            console.log('Complete reset requested...');
        }
    }
}

function viewConfirmationDetails(symbol) {
    console.log('Viewing confirmation details for:', symbol);
}

function manualSell(symbol) {
    if (confirm(`Are you sure you want to manually sell ${symbol}?`)) {
        console.log('Manual sell requested for:', symbol);
    }
}

function refreshSymbolExplorer() {
    window.tradingDashboard?.initializeSymbolExplorer();
}

function loadSymbolData() {
    window.tradingDashboard?.loadSymbolData();
}

function filterSymbolList(searchTerm) {
    // Filter symbol dropdown based on search term
    const select = document.getElementById('symbolSelect');
    if (!select) return;

    const options = Array.from(select.options);
    options.forEach(option => {
        if (option.value === '') return; // Keep the default option

        const matches = option.text.toLowerCase().includes(searchTerm.toLowerCase());
        option.style.display = matches ? '' : 'none';
    });
}
