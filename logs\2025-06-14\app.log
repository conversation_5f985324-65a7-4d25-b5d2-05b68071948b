[E 250614 08:05:30 smartConnect:221] Error occurred while making a POST request to https://apiconnect.angelone.in/rest/auth/angelbroking/user/v1/loginByPassword. Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': 'ac:67:5d:71:1d:52', 'Accept': 'application/json', 'X-PrivateKey': 'BjxCyX7C', 'X-UserType': 'USER', 'X-SourceID': 'WEB'}, Request: {'clientcode': 'AAAM575832', 'password': '1003', 'totp': '627631'}, Response: HTTPSConnectionPool(host='apiconnect.angelone.in', port=443): Max retries exceeded with url: /rest/auth/angelbroking/user/v1/loginByPassword (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x0000028D06BAE190>: Failed to resolve 'apiconnect.angelone.in' ([Errno 11001] getaddrinfo failed)"))
[E 250614 15:09:55 smartConnect:246] Error occurred while making a POST request to https://apiconnect.angelone.in/rest/secure/angelbroking/historical/v1/getCandleData. Error: Invalid Token. URL: https://apiconnect.angelone.in/rest/secure/angelbroking/historical/v1/getCandleData, Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': 'ac:67:5d:71:1d:52', 'Accept': 'application/json', 'X-PrivateKey': 'BjxCyX7C', 'X-UserType': 'USER', 'X-SourceID': 'WEB'}, Request: {'exchange': 'NSE', 'symboltoken': '13061', 'interval': 'FIFTEEN_MINUTE', 'fromdate': '2025-06-13 09:30', 'todate': '2025-06-13 09:45'}, Response: {'success': False, 'message': 'Invalid Token', 'errorCode': 'AG8001', 'data': ''}
