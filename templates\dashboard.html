<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Trading System - ₹2.22 Crore Portfolio</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --success-color: #27ae60;
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
            --info-color: #3498db;
            --dark-bg: #1a1a1a;
            --card-bg: #2d3748;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .navbar {
            background: rgba(44, 62, 80, 0.95) !important;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--success-color);
            padding: 0.25rem 1rem; /* Reduced padding */
            min-height: 45px; /* Fixed smaller height */
        }

        .navbar-brand {
            font-size: 1.1rem; /* Smaller font */
            margin-right: 0.5rem;
        }

        .navbar-text {
            font-size: 0.85rem; /* Smaller text */
            margin: 0 0.25rem;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: none;
            border-radius: 8px; /* Smaller radius */
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* Smaller shadow */
            margin-bottom: 0.75rem; /* Reduced margin */
        }

        .card-header {
            padding: 0.5rem 0.75rem; /* Reduced padding */
            background: rgba(248, 249, 250, 0.8);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .card-body {
            padding: 0.75rem; /* Reduced padding */
        }

        .card:hover {
            transform: translateY(-2px); /* Smaller hover effect */
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        .status-active {
            background-color: var(--success-color);
        }

        .status-inactive {
            background-color: var(--danger-color);
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .metric-card {
            text-align: center;
            padding: 0.75rem; /* Reduced padding */
        }

        .metric-value {
            font-size: 1.5rem; /* Smaller font */
            font-weight: bold;
            margin-bottom: 2px; /* Reduced margin */
        }

        .metric-label {
            color: #6c757d;
            font-size: 0.75rem; /* Smaller font */
            text-transform: uppercase;
            letter-spacing: 0.5px; /* Reduced spacing */
        }

        .btn-trading {
            background: linear-gradient(45deg, var(--success-color), #2ecc71);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .btn-trading:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
            color: white;
        }

        .btn-danger-custom {
            background: linear-gradient(45deg, var(--danger-color), #c0392b);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: bold;
        }

        .log-container {
            background: #1a1a1a;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            height: 300px;
            overflow-y: auto;
            padding: 15px;
            border-radius: 10px;
            font-size: 0.85rem;
        }

        .symbol-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            max-height: 400px;
            overflow-y: auto;
        }

        .symbol-card {
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .symbol-card:hover {
            background: rgba(52, 152, 219, 0.1);
            transform: scale(1.05);
        }

        .price-positive {
            color: var(--success-color);
            font-weight: bold;
        }

        .price-negative {
            color: var(--danger-color);
            font-weight: bold;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert-custom {
            border-radius: 10px;
            border: none;
            font-weight: 500;
        }

        .chart-container {
            position: relative;
            height: 250px; /* Reduced height */
            margin: 10px 0; /* Reduced margin */
        }

        /* Compact layout styles */
        .container-fluid {
            padding: 0.5rem; /* Reduced container padding */
        }

        .row {
            margin: 0.25rem 0; /* Reduced row margins */
        }

        .col-md-3, .col-md-6, .col-md-8, .col-md-4 {
            padding: 0.25rem; /* Reduced column padding */
        }

        /* Compact tabs */
        .nav-tabs {
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 0.5rem; /* Reduced margin */
        }

        .nav-tabs .nav-link {
            padding: 0.375rem 0.75rem; /* Reduced padding */
            font-size: 0.875rem; /* Smaller font */
            border: 1px solid transparent;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
        }

        /* Compact tables */
        .table {
            font-size: 0.85rem; /* Smaller table font */
            margin-bottom: 0.5rem;
        }

        .table th, .table td {
            padding: 0.375rem; /* Reduced cell padding */
            vertical-align: middle;
        }

        /* Compact log container */
        .log-container {
            background: #1a1a1a;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            height: 200px; /* Reduced height */
            overflow-y: auto;
            padding: 0.5rem; /* Reduced padding */
            border-radius: 5px; /* Smaller radius */
            font-size: 0.75rem; /* Smaller font */
            line-height: 1.2; /* Tighter line height */
        }

        /* Compact symbol grid */
        .symbol-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); /* Smaller cards */
            gap: 0.5rem; /* Reduced gap */
            max-height: 300px; /* Reduced height */
            overflow-y: auto;
        }

        .symbol-card {
            background: rgba(255, 255, 255, 0.9);
            padding: 0.5rem; /* Reduced padding */
            border-radius: 5px; /* Smaller radius */
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            font-size: 0.8rem; /* Smaller font */
        }

        /* Compact buttons */
        .btn {
            padding: 0.375rem 0.75rem; /* Reduced button padding */
            font-size: 0.875rem; /* Smaller font */
        }

        .btn-sm {
            padding: 0.25rem 0.5rem; /* Even smaller for small buttons */
            font-size: 0.75rem;
        }

        /* Compact alerts */
        .alert {
            padding: 0.5rem 0.75rem; /* Reduced alert padding */
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }

        /* Maximize data viewing areas */
        .data-viewing-area {
            min-height: calc(100vh - 200px); /* Use most of screen height */
        }

        /* Compact form controls */
        .form-control, .form-select {
            padding: 0.375rem 0.75rem; /* Reduced form padding */
            font-size: 0.875rem;
        }

        /* SQL Query specific optimizations */
        #sqlQuery {
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            line-height: 1.3;
        }

        #databaseSchema {
            font-size: 0.8rem;
        }

        #queryResults {
            max-height: 400px;
            overflow-y: auto;
        }

        /* Data Logs Styling */
        .log-container {
            border: 1px solid #333;
            border-radius: 5px;
        }

        .log-entry {
            font-size: 12px;
            line-height: 1.4;
            padding: 2px 0;
            border-bottom: 1px solid #333;
        }

        .log-entry:last-child {
            border-bottom: none;
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .metric-label {
            font-size: 0.8rem;
            color: #6c757d;
        }

        .loading-spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-chart-line me-2"></i>
                Advanced Trading System
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-wallet me-1"></i>
                    Portfolio: <strong>₹2.22 Crore</strong>
                </span>
                <span class="navbar-text">
                    <span id="connectionStatus" class="status-indicator status-inactive"></span>
                    <span id="connectionText">Connecting...</span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <!-- Compact Control Panel -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <div class="d-flex align-items-center">
                                    <span id="tradingStatus" class="status-indicator status-inactive"></span>
                                    <small id="tradingStatusText">Trading Inactive</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <button id="startTradingBtn" class="btn btn-trading btn-sm">
                                    <i class="fas fa-play me-1"></i>Start
                                </button>
                                <button id="stopTradingBtn" class="btn btn-danger-custom btn-sm d-none">
                                    <i class="fas fa-stop me-1"></i>Stop
                                </button>
                            </div>
                            <div class="col-md-2">
                                <div class="d-flex align-items-center">
                                    <span id="dataFetchStatus" class="status-indicator status-inactive"></span>
                                    <small id="dataFetchStatusText">Data Inactive</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <button id="startDataFetchBtn" class="btn btn-info btn-sm">
                                    <i class="fas fa-download me-1"></i>Data
                                </button>
                            </div>
                            <!-- Portfolio metrics moved to Paper Trading tab -->

                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Tabs -->
        <div class="row">
            <div class="col-12">
                <div class="card data-viewing-area">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="mainTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="positions-tab" data-bs-toggle="tab" data-bs-target="#positions" type="button" role="tab">
                                    <i class="fas fa-briefcase me-2"></i>Active Positions
                                </button>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="data-logs-tab" data-bs-toggle="tab" data-bs-target="#data-logs" type="button" role="tab">
                                    <i class="fas fa-chart-line me-2"></i>Symbol Explorer
                                </a>
                            </li>

                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="sql-tab" data-bs-toggle="tab" data-bs-target="#sql" type="button" role="tab">
                                    <i class="fas fa-database me-2"></i>SQL Query
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="integrity-tab" data-bs-toggle="tab" data-bs-target="#integrity" type="button" role="tab">
                                    <i class="fas fa-shield-alt me-2"></i>Data Integrity
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="duplicates-tab" data-bs-toggle="tab" data-bs-target="#duplicates" type="button" role="tab">
                                    <i class="fas fa-copy me-2"></i>Duplicates
                                </button>
                            </li>

                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="paper-trading-tab" data-bs-toggle="tab" data-bs-target="#paper-trading" type="button" role="tab">
                                    <i class="fas fa-file-csv me-2"></i>Paper Trading
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="symbol-manager-tab" data-bs-toggle="tab" data-bs-target="#symbol-manager" type="button" role="tab">
                                    <i class="fas fa-cogs me-2"></i>Symbol Manager
                                </button>
                            </li>

                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="mainTabsContent">
                            <!-- Active Positions Tab -->
                            <div class="tab-pane fade show active" id="positions" role="tabpanel">
                                <!-- Real-time P&L Header -->
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <div class="card">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-signal me-1"></i>🎯 Active Signals - DB1 Generated
                                                </h6>
                                                <div class="d-flex align-items-center gap-2">
                                                    <span class="badge bg-success" id="autoRefreshBadge">
                                                        <i class="fas fa-sync-alt fa-spin me-1"></i>Auto-Refresh Every 15min
                                                    </span>
                                                    <small class="text-muted" id="positionsLastUpdate">Last updated: --:--:--</small>
                                                    <button class="btn btn-outline-primary btn-sm" onclick="refreshActivePositions()">
                                                        <i class="fas fa-sync me-1"></i>Refresh Now
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="card-body py-2">
                                                <div class="row text-center" id="positionsSummary">
                                                    <div class="col-md-2">
                                                        <div class="metric-value text-primary" id="totalActivePositions">0</div>
                                                        <div class="metric-label">Active Positions</div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="metric-value text-warning" id="totalInvestment">₹0</div>
                                                        <div class="metric-label">Total Investment</div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="metric-value text-info" id="currentValue">₹0</div>
                                                        <div class="metric-label">Current Value</div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="metric-value" id="totalPnL">₹0</div>
                                                        <div class="metric-label">Total P&L</div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="metric-value text-success" id="targetsAchieved">0</div>
                                                        <div class="metric-label">Targets Achieved</div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="metric-value text-danger" id="targetsRemaining">0</div>
                                                        <div class="metric-label">Targets Remaining</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Active Positions Table -->
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Symbol</th>
                                                <th>Buy Price</th>
                                                <th>Current Price</th>
                                                <th>Quantity</th>
                                                <th>Investment</th>
                                                <th>Current Value</th>
                                                <th>Target Price</th>
                                                <th>Current P&L</th>
                                                <th>P&L %</th>
                                                <th>Buy Time</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody id="positionsTableBody">
                                            <tr>
                                                <td colspan="11" class="text-center text-muted">
                                                    <div class="py-3">
                                                        <div class="loading-spinner"></div>
                                                        <p class="mt-2">Loading active positions...</p>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Legend -->
                                <div class="row mt-3">
                                    <div class="col-md-12">
                                        <div class="card">
                                            <div class="card-body py-2">
                                                <div class="d-flex justify-content-center gap-4">
                                                    <div class="d-flex align-items-center">
                                                        <div class="badge bg-success me-2">TARGET ACHIEVED</div>
                                                        <small>Symbol reached ₹800 target price</small>
                                                    </div>
                                                    <div class="d-flex align-items-center">
                                                        <div class="badge bg-success me-2" style="background-color: #28a745 !important;">PROFIT</div>
                                                        <small>Positive P&L</small>
                                                    </div>
                                                    <div class="d-flex align-items-center">
                                                        <div class="badge bg-danger me-2">LOSS</div>
                                                        <small>Negative P&L</small>
                                                    </div>
                                                    <div class="d-flex align-items-center">
                                                        <div class="badge bg-warning me-2">PENDING</div>
                                                        <small>Target not yet achieved</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>



                            <!-- SQL Query Tab -->
                            <div class="tab-pane fade" id="sql" role="tabpanel">
                                <div class="row">
                                    <!-- Left Panel: Query Interface -->
                                    <div class="col-md-8">
                                        <div class="card">
                                            <div class="card-body">
                                                <!-- Query Templates and Controls in one row -->
                                                <div class="row mb-2">
                                                    <div class="col-md-6">
                                                        <select class="form-select form-select-sm" id="queryTemplates">
                                                            <option value="">Select template...</option>
                                                        </select>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="d-flex gap-1">
                                                            <button class="btn btn-primary btn-sm" id="executeQueryBtn">
                                                                <i class="fas fa-play me-1"></i>Execute
                                                            </button>
                                                            <button class="btn btn-outline-secondary btn-sm" id="clearQueryBtn">
                                                                <i class="fas fa-eraser me-1"></i>Clear
                                                            </button>
                                                            <button class="btn btn-outline-info btn-sm" id="formatQueryBtn">
                                                                <i class="fas fa-magic me-1"></i>Format
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- SQL Query Textarea -->
                                                <div class="mb-2">
                                                    <textarea class="form-control" id="sqlQuery" rows="6"
                                                              placeholder="SELECT symbol, close_price, volume FROM trading_data WHERE DATE(timestamp) = DATE('now') ORDER BY volume DESC LIMIT 10"></textarea>
                                                </div>

                                                <!-- Query Status -->
                                                <div id="queryStatus" class="alert d-none"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Right Panel: Database Schema -->
                                    <div class="col-md-4">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-table me-1"></i>Schema
                                                </h6>
                                            </div>
                                            <div class="card-body" style="max-height: 300px; overflow-y: auto;">
                                                <div id="databaseSchema">
                                                    <div class="text-center">
                                                        <div class="loading-spinner"></div>
                                                        <p class="mt-2">Loading...</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Query Results -->
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-table me-1"></i>Results
                                                </h6>
                                                <button class="btn btn-outline-success btn-sm" id="exportCsvBtn" style="display: none;">
                                                    <i class="fas fa-download me-1"></i>CSV
                                                </button>
                                            </div>
                                            <div class="card-body">
                                                <div id="queryResults">
                                                    <div class="text-center text-muted">
                                                        <i class="fas fa-info-circle me-1"></i>
                                                        Execute a query to see results
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Data Integrity Tab -->
                            <div class="tab-pane fade" id="integrity" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-shield-alt me-1"></i>Data Integrity Check
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="alert alert-info py-2 mb-3">
                                                    <small>
                                                        <i class="fas fa-lightbulb me-1"></i>
                                                        <strong>Complete Workflow:</strong> Symbol Manager → Add Symbols →
                                                        <span class="badge bg-warning text-dark">Click Comprehensive</span> →
                                                        Symbols become trading-ready with last 25 data points (DB1→DB2→BUY/SELL)
                                                    </small>
                                                </div>
                                                <div class="d-flex gap-2 mb-3">
                                                    <button class="btn btn-primary" onclick="window.tradingDashboard?.runQuickIntegrityCheck()">
                                                        <i class="fas fa-play me-1"></i>Quick Check
                                                    </button>
                                                    <button class="btn btn-outline-info" onclick="window.tradingDashboard?.refreshPriorityQueue()">
                                                        <i class="fas fa-info-circle me-1"></i>Status
                                                    </button>
                                                </div>
                                                <div class="d-flex gap-2 mb-3">
                                                    <button class="btn btn-success" onclick="window.tradingDashboard?.runComprehensiveCheck()">
                                                        <i class="fas fa-search me-1"></i>Comprehensive
                                                    </button>
                                                    <button class="btn btn-warning" onclick="runPriorityAnalysis()">
                                                        <i class="fas fa-trophy me-1"></i>Priority Analysis
                                                    </button>
                                                    <button class="btn btn-danger" onclick="stopAutoFetch()">
                                                        <i class="fas fa-stop me-1"></i>🛑 STOP Auto Fetch
                                                    </button>
                                                    <button class="btn btn-outline-info btn-sm" onclick="refreshIntegrityData()">
                                                        <i class="fas fa-sync me-1"></i>Refresh
                                                    </button>
                                                </div>
                                                <div class="alert alert-info">
                                                    <i class="fas fa-info-circle me-2"></i>
                                                    <strong>Priority Analysis:</strong> Analyzes SQL data to create GOLD/SILVER/BRONZE batches (no API calls). Enhanced data fetching runs automatically during trading hours with priority optimization.
                                                </div>

                                                <!-- Real-time Priority Queue Status -->
                                                <div class="card mb-3">
                                                    <div class="card-header d-flex justify-content-between align-items-center">
                                                        <h6 class="mb-0">🎯 Real-time Priority Queue Status</h6>
                                                        <small class="text-muted" id="priorityTimestamp">Last updated: --:--:--</small>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="row" id="priorityBatches">
                                                            <div class="text-center">
                                                                <div class="loading-spinner"></div>
                                                                <p class="mt-2">Loading priority queue status...</p>
                                                            </div>
                                                        </div>

                                                        <!-- Priority Queue Controls -->
                                                        <div class="mt-3 d-flex gap-2">
                                                            <button class="btn btn-warning btn-sm" onclick="refreshPriorityQueue()">
                                                                <i class="fas fa-sync me-1"></i>Refresh Queue
                                                            </button>
                                                            <button class="btn btn-outline-info btn-sm" onclick="analyzePriorityPatterns()">
                                                                <i class="fas fa-chart-line me-1"></i>Analyze Patterns
                                                            </button>
                                                            <button class="btn btn-outline-success btn-sm" onclick="showPriorityDetails()">
                                                                <i class="fas fa-list me-1"></i>Show Details
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div id="integrityResults"></div>


                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-chart-bar me-1"></i>Integrity Status
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="integrityStatus">
                                                    <div class="text-center text-muted">
                                                        <i class="fas fa-info-circle me-1"></i>
                                                        Run integrity check to see status
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Duplicates Tab -->
                            <div class="tab-pane fade" id="duplicates" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-copy me-1"></i>Duplicate Scanner
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="d-flex gap-2 mb-3">
                                                    <button class="btn btn-warning" onclick="scanDuplicatesNow()">
                                                        <i class="fas fa-search me-1"></i>Scan Duplicates
                                                    </button>
                                                    <button class="btn btn-danger" onclick="removeDuplicatesNow()">
                                                        <i class="fas fa-trash me-1"></i>Remove Duplicates
                                                    </button>
                                                </div>
                                                <div id="duplicateResults"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-list me-1"></i>Found Duplicates
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="duplicatesList">
                                                    <div class="text-center text-muted">
                                                        <i class="fas fa-info-circle me-1"></i>
                                                        Scan for duplicates to see results
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>



                            <!-- Paper Trading Tab -->
                            <div class="tab-pane fade" id="paper-trading" role="tabpanel">
                                <!-- DB2 Brain Status -->
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-brain me-1"></i>🧠 DB2 - Paper Trading Brain (2-Minute Confirmations)
                                                </h6>
                                            </div>
                                            <div class="card-body py-2">
                                                <div class="row text-center">
                                                    <div class="col-md-2">
                                                        <div class="metric-value text-primary" id="db2TotalSignals">0</div>
                                                        <div class="metric-label">Total Signals</div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="metric-value text-warning" id="db2PendingBuy">0</div>
                                                        <div class="metric-label">Pending BUY</div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="metric-value text-danger" id="db2PendingSell">0</div>
                                                        <div class="metric-label">Pending SELL</div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="metric-value text-success" id="db2ReadyExecution">0</div>
                                                        <div class="metric-label">Ready for Execution</div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="metric-value text-info" id="db2ActivePositions">0</div>
                                                        <div class="metric-label">Active Positions</div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="metric-value text-success" id="db2TotalProfit">₹0</div>
                                                        <div class="metric-label">Total Profit</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>



                                <!-- Layer 2 Confirmations Status -->
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <div class="card">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-layer-group me-1"></i>🔄 Layer 2 Confirmations - Trend Tracking
                                                </h6>
                                                <div class="d-flex align-items-center gap-2">
                                                    <small class="text-muted" id="layer2LastUpdate">Last updated: --:--:--</small>
                                                    <button class="btn btn-outline-info btn-sm" onclick="refreshLayer2Confirmations()">
                                                        <i class="fas fa-sync me-1"></i>Refresh
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                <div class="alert alert-info py-2 mb-3">
                                                    <small>
                                                        <i class="fas fa-info-circle me-1"></i>
                                                        <strong>Layer 2 Logic:</strong>
                                                        BUY Signal (410) → Check every 2min → Compare with PREVIOUS price only (not original 410) →
                                                        Track: [RISE, FALL, RISE, RISE] → Execute on 2 consecutive RISE or FALL
                                                    </small>
                                                </div>
                                                <div id="layer2ConfirmationsTable">
                                                    <div class="text-center">
                                                        <div class="loading-spinner"></div>
                                                        <p class="mt-2">Loading Layer 2 confirmations...</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>



                                <!-- Paper Trading Records -->
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="card">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-chart-bar me-1"></i>📊 Paper Trading Records (DB2)
                                                </h6>
                                                <div class="d-flex gap-1">
                                                    <span class="badge bg-success me-2">
                                                        <i class="fas fa-sync-alt fa-spin me-1"></i>Auto-Refresh
                                                    </span>
                                                    <button class="btn btn-outline-primary btn-sm" onclick="downloadPaperTrades()">
                                                        <i class="fas fa-download me-1"></i>Download CSV
                                                    </button>
                                                    <button class="btn btn-outline-warning btn-sm" onclick="clearPaperTrades()">
                                                        <i class="fas fa-broom me-1"></i>Clear Records
                                                    </button>
                                                    <button class="btn btn-danger btn-sm fw-bold" onclick="completeReset()" title="⚠️ DANGER: Complete reset of all trading data">
                                                        <i class="fas fa-exclamation-triangle me-1"></i>🔄 COMPLETE RESET
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                <!-- Trading Statistics -->
                                                <div id="paperTradingStats" class="row mb-3">
                                                    <div class="col-md-2">
                                                        <div class="text-center">
                                                            <div class="metric-value text-primary" id="totalPaperTrades">0</div>
                                                            <div class="metric-label">Total Trades</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="text-center">
                                                            <div class="metric-value text-success" id="completedTrades">0</div>
                                                            <div class="metric-label">Completed</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="text-center">
                                                            <div class="metric-value text-warning" id="activeTrades">0</div>
                                                            <div class="metric-label">Active</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="text-center">
                                                            <div class="metric-value text-info" id="profitableTrades">0</div>
                                                            <div class="metric-label">Profitable</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="text-center">
                                                            <div class="metric-value text-success" id="avgProfit">₹0</div>
                                                            <div class="metric-label">Avg Profit</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="text-center">
                                                            <div class="metric-value text-info" id="uniqueSymbolsTraded">0</div>
                                                            <div class="metric-label">Unique Symbols</div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Trading Records Table -->
                                                <div class="table-responsive">
                                                    <table class="table table-striped table-hover">
                                                        <thead class="table-dark">
                                                            <tr>
                                                                <th>Symbol</th>
                                                                <th>Buy Price</th>
                                                                <th>Sell Price</th>
                                                                <th>Quantity</th>
                                                                <th>Investment</th>
                                                                <th>Profit/Loss</th>
                                                                <th>Status</th>
                                                                <th>Buy Time</th>
                                                                <th>Manual Controls</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="paperTradesTableBody">
                                                            <tr>
                                                                <td colspan="9" class="text-center text-muted">
                                                                    <div class="py-3">
                                                                        <div class="loading-spinner"></div>
                                                                        <p class="mt-2">Loading paper trading records...</p>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Symbol Manager Tab -->
                            <div class="tab-pane fade" id="symbol-manager" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-plus me-2"></i>Add New Symbols
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <label for="newSymbols" class="form-label">Symbols (one per line)</label>
                                                    <textarea class="form-control" id="newSymbols" rows="5" placeholder="TCS&#10;RELIANCE&#10;INFY"></textarea>
                                                </div>
                                                <button class="btn btn-success" onclick="addSymbols()">
                                                    <i class="fas fa-plus me-1"></i>Add Symbols
                                                </button>
                                            </div>
                                        </div>

                                        <div class="card mt-3">
                                            <div class="card-header">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-trash me-2"></i>Delete Symbols
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <label for="deleteSymbols" class="form-label">Symbols to Delete (one per line)</label>
                                                    <textarea class="form-control" id="deleteSymbols" rows="3" placeholder="SYMBOL1&#10;SYMBOL2"></textarea>
                                                </div>
                                                <button class="btn btn-danger" onclick="deleteSymbols()">
                                                    <i class="fas fa-trash me-1"></i>Delete Symbols
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-chart-bar me-2"></i>Symbol Statistics
                                                </h6>
                                                <button class="btn btn-outline-primary btn-sm" onclick="loadSymbolStats()">
                                                    <i class="fas fa-sync me-1"></i>Refresh
                                                </button>
                                            </div>
                                            <div class="card-body">
                                                <div id="symbolStats">
                                                    <div class="text-center">
                                                        <div class="spinner-border text-primary" role="status">
                                                            <span class="visually-hidden">Loading...</span>
                                                        </div>
                                                        <p class="mt-2">Loading statistics...</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="card mt-3">
                                            <div class="card-header">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-search me-2"></i>Search Symbols
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="input-group mb-3">
                                                    <input type="text" class="form-control" id="symbolSearch" placeholder="Search symbols or company names">
                                                    <button class="btn btn-outline-secondary" onclick="searchSymbols()">
                                                        <i class="fas fa-search"></i>
                                                    </button>
                                                </div>
                                                <div id="searchResults"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-exclamation-triangle me-2"></i>Symbols Without Tokens
                                                </h6>
                                                <button class="btn btn-outline-warning btn-sm" onclick="loadMissingTokens()">
                                                    <i class="fas fa-sync me-1"></i>Refresh
                                                </button>
                                            </div>
                                            <div class="card-body">
                                                <div id="missingTokens">
                                                    <div class="text-center">
                                                        <div class="spinner-border text-warning" role="status">
                                                            <span class="visually-hidden">Loading...</span>
                                                        </div>
                                                        <p class="mt-2">Loading missing tokens...</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Symbol Data Explorer Tab -->
                            <div class="tab-pane fade" id="data-logs" role="tabpanel">
                                <div class="row">
                                    <!-- Symbol Data Explorer -->
                                    <div class="col-md-8">
                                        <div class="card">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h5 class="mb-0">
                                                    <i class="fas fa-chart-line me-2"></i>Symbol Data Explorer
                                                </h5>
                                                <div class="d-flex gap-2">
                                                    <select class="form-select form-select-sm" id="dataSourceSelect" style="width: 150px;">
                                                        <option value="db1">DB1 - Market Data</option>
                                                        <option value="db2">DB2 - Trading Data</option>
                                                    </select>
                                                    <button class="btn btn-sm btn-outline-primary" id="refreshSymbolDataBtn">
                                                        <i class="fas fa-sync-alt me-1"></i>Refresh
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                <!-- Symbol Selection -->
                                                <div class="row mb-3">
                                                    <div class="col-md-6">
                                                        <label class="form-label">Select Symbol:</label>
                                                        <select class="form-select" id="symbolSelect">
                                                            <option value="">Choose a symbol...</option>
                                                        </select>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="form-label">Quick Search:</label>
                                                        <input type="text" class="form-control" id="symbolSearchInput" placeholder="Type symbol name...">
                                                    </div>
                                                </div>

                                                <!-- Symbol Data Display -->
                                                <div class="mb-3">
                                                    <button class="btn btn-primary btn-sm" onclick="loadSymbolData()">
                                                        <i class="fas fa-search me-1"></i>Load Symbol Data
                                                    </button>
                                                    <button class="btn btn-outline-secondary btn-sm" onclick="refreshSymbolExplorer()">
                                                        <i class="fas fa-sync me-1"></i>Refresh
                                                    </button>
                                                </div>
                                                <div id="symbolDataContainer" style="height: 400px; overflow-y: auto;">
                                                    <div class="text-center text-muted">
                                                        <i class="fas fa-info-circle me-2"></i>Select a symbol and click "Load Symbol Data" to view DB1/DB2 data
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- All Symbols List -->
                                    <div class="col-md-4">
                                        <div class="card">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h5 class="mb-0">
                                                    <i class="fas fa-list me-2"></i>All Symbols
                                                </h5>
                                                <span class="badge bg-primary" id="symbolCount">0</span>
                                            </div>
                                            <div class="card-body p-0">
                                                <div id="allSymbolsList" style="height: 400px; overflow-y: auto; padding: 10px;">
                                                    <div class="text-center text-muted">
                                                        <i class="fas fa-spinner fa-spin me-2"></i>Loading symbols...
                                                    </div>
                                                </div>
                                            </div>
                                        </div>


                                    </div>
                                </div>

                                <!-- Database Summary -->
                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-database me-2"></i>DB1 - Market Data Summary
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="db1Summary">
                                                    <div class="text-center text-muted">
                                                        <i class="fas fa-spinner fa-spin me-2"></i>Loading DB1 summary...
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header bg-warning text-dark">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-chart-line me-2"></i>DB2 - Trading Operations Summary
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="db2Summary">
                                                    <div class="text-center text-muted">
                                                        <i class="fas fa-spinner fa-spin me-2"></i>Loading DB2 summary...
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- NEW Custom JavaScript - Complete Frontend System -->
    <script src="{{ url_for('static', filename='js/new_dashboard.js') }}?v={{ range(1000, 9999) | random }}"></script>

    <!-- Duplicate Scanner Functions -->
    <script>
        async function scanDuplicatesNow() {
            console.log('🔍 Scanning for duplicates...');

            const container = document.getElementById('duplicateResults');
            container.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-warning" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Scanning for duplicates...</p>
                </div>
            `;

            try {
                const response = await fetch('/api/symbols/scan-duplicates');
                const data = await response.json();

                console.log('📊 Scan response:', data);

                if (data.success) {
                    displayDuplicateResults(data.duplicates);
                } else {
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>Error: ${data.error}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('❌ Scan error:', error);
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>Failed to scan duplicates: ${error.message}
                    </div>
                `;
            }
        }

        async function removeDuplicatesNow() {
            console.log('🗑️ Removing duplicates...');

            if (!confirm('Are you sure you want to remove duplicate records? This action cannot be undone.')) {
                return;
            }

            const container = document.getElementById('duplicateResults');
            container.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-danger" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Removing duplicates...</p>
                </div>
            `;

            try {
                const response = await fetch('/api/symbols/clean-duplicates', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const data = await response.json();

                console.log('🗑️ Remove response:', data);

                if (data.success) {
                    container.innerHTML = `
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>Duplicates Removed</h6>
                            <p>${data.message}</p>
                        </div>
                    `;
                } else {
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>Error: ${data.error}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('❌ Remove error:', error);
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>Failed to remove duplicates: ${error.message}
                    </div>
                `;
            }
        }

        function displayDuplicateResults(duplicates) {
            const container = document.getElementById('duplicateResults');
            const count = duplicates.total_count;

            if (count === 0) {
                container.innerHTML = `
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>No Duplicates Found</h6>
                        <p>Your database is clean! No duplicate records were found in Excel, DB1, or DB2.</p>
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-database me-1"></i>All databases are optimized and ready for trading
                            </small>
                        </div>
                    </div>
                `;
            } else {
                let duplicatesList = '';

                // Show duplicates from all sources
                const allDuplicates = duplicates.all_duplicates || [];
                allDuplicates.slice(0, 10).forEach(symbol => {
                    let sources = [];
                    if (duplicates.excel.includes(symbol)) sources.push('Excel');
                    if (duplicates.db1.includes(symbol)) sources.push('DB1');
                    if (duplicates.db2.includes(symbol)) sources.push('DB2');

                    duplicatesList += `
                        <tr>
                            <td><strong>${symbol}</strong></td>
                            <td>${sources.join(', ')}</td>
                            <td><span class="badge bg-warning">${sources.length}</span></td>
                        </tr>
                    `;
                });

                container.innerHTML = `
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Duplicates Found</h6>
                        <p>Found <strong>${count}</strong> duplicate symbols across Excel, DB1, and DB2.</p>

                        <div class="row mb-3">
                            <div class="col-4 text-center">
                                <div class="metric-value text-warning">${duplicates.excel.length}</div>
                                <div class="metric-label">Excel</div>
                            </div>
                            <div class="col-4 text-center">
                                <div class="metric-value text-warning">${duplicates.db1.length}</div>
                                <div class="metric-label">DB1</div>
                            </div>
                            <div class="col-4 text-center">
                                <div class="metric-value text-warning">${duplicates.db2.length}</div>
                                <div class="metric-label">DB2</div>
                            </div>
                        </div>

                        <div class="table-responsive mt-3">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Symbol</th>
                                        <th>Found In</th>
                                        <th>Sources</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${duplicatesList}
                                </tbody>
                            </table>
                        </div>

                        ${count > 10 ? `<small class="text-muted">Showing first 10 of ${count} duplicates</small>` : ''}

                        <div class="mt-3">
                            <button class="btn btn-warning btn-sm" onclick="removeDuplicatesNow()">
                                <i class="fas fa-trash me-1"></i>Clean All Duplicates
                            </button>
                        </div>
                    </div>
                `;
            }
        }

        // Symbol Manager Functions
        async function addSymbolsNow() {
            console.log('➕ Adding tradeable symbols...');

            const symbolsText = document.getElementById('symbolsToAdd').value.trim();
            if (!symbolsText) {
                alert('Please enter symbols to add');
                return;
            }

            const symbols = symbolsText.split('\n').map(s => s.trim().toUpperCase()).filter(s => s);
            const container = document.getElementById('symbolManagerResults');

            container.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-success" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Adding ${symbols.length} symbols...</p>
                    <small class="text-muted">Validating trading tokens...</small>
                </div>
            `;

            try {
                const response = await fetch('/api/symbols/add', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ symbols })
                });
                const data = await response.json();

                console.log('➕ Add response:', data);

                if (data.success) {
                    // Check if symbols have tokens
                    let tokenStatus = '';
                    for (const symbol of symbols) {
                        const searchResponse = await fetch(`/api/symbols/search?q=${symbol}`);
                        const searchData = await searchResponse.json();
                        if (searchData.success && searchData.results.length > 0) {
                            tokenStatus += `✅ ${symbol}: Ready for trading<br>`;
                        } else {
                            tokenStatus += `⚠️ ${symbol}: Added but no trading token found<br>`;
                        }
                    }

                    container.innerHTML = `
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>Symbols Added Successfully</h6>
                            <p>${data.message}</p>
                            <div class="mt-2">
                                <strong>Trading Status:</strong><br>
                                ${tokenStatus}
                            </div>
                            <div class="mt-3">
                                <div class="alert alert-warning py-2">
                                    <h6><i class="fas fa-lightbulb me-1"></i>🎯 Next Steps for Complete Trading Flow:</h6>
                                    <ol class="mb-2">
                                        <li><strong>Go to Data Integrity tab</strong></li>
                                        <li><strong>Click "Comprehensive" button</strong></li>
                                        <li><strong>System fetches last 25 data points</strong></li>
                                        <li><strong>Symbols become fully trading-ready</strong></li>
                                    </ol>
                                    <div class="text-center">
                                        <button class="btn btn-warning btn-sm" onclick="document.getElementById('integrity-tab').click()">
                                            <i class="fas fa-arrow-right me-1"></i>Go to Data Integrity Now
                                        </button>
                                    </div>
                                </div>
                                <small class="text-info">
                                    <i class="fas fa-info-circle me-1"></i>
                                    <strong>Complete Flow:</strong> DB1 (historical data) → DB2 (confirmations) → BUY/SELL signals
                                </small>
                            </div>
                        </div>
                    `;
                    document.getElementById('symbolsToAdd').value = '';

                    // Refresh symbol stats
                    refreshSymbolStatsNow();
                } else {
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>Error: ${data.error}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('❌ Add error:', error);
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>Failed to add symbols: ${error.message}
                    </div>
                `;
            }
        }

        async function deleteSymbolsNow() {
            console.log('🗑️ Deleting symbols completely...');

            const symbolsText = document.getElementById('symbolsToDelete').value.trim();
            if (!symbolsText) {
                alert('Please enter symbols to delete');
                return;
            }

            const symbols = symbolsText.split('\n').map(s => s.trim().toUpperCase()).filter(s => s);

            // Enhanced confirmation dialog
            const confirmMessage = `⚠️ COMPLETE SYMBOL DELETION ⚠️\n\nAre you sure you want to PERMANENTLY delete ${symbols.length} symbols?\n\nSymbols to delete:\n${symbols.join(', ')}\n\n🗑️ This will COMPLETELY remove them from:\n✅ Excel symbol list\n✅ Token mappings (angelone_tokens.txt)\n✅ DB1 (market data & trading history)\n✅ DB2 (trading positions & confirmations)\n✅ All trading records and data\n\n❌ THIS ACTION CANNOT BE UNDONE!\n\nType 'DELETE' to confirm:`;

            const userInput = prompt(confirmMessage);
            if (userInput !== 'DELETE') {
                alert('Deletion cancelled. You must type "DELETE" to confirm.');
                return;
            }

            const container = document.getElementById('symbolManagerResults');

            container.innerHTML = `
                <div class="alert alert-warning">
                    <h6><i class="fas fa-trash me-2"></i>Complete Symbol Deletion in Progress</h6>
                    <div class="text-center mt-3">
                        <div class="spinner-border text-danger" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Deleting ${symbols.length} symbols from all systems...</p>
                        <small class="text-muted">
                            Removing from Excel, Tokens, DB1, DB2...
                        </small>
                    </div>
                </div>
            `;

            try {
                const response = await fetch('/api/symbols/delete', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ symbols })
                });
                const data = await response.json();

                console.log('🗑️ Delete response:', data);

                if (data.success) {
                    container.innerHTML = `
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>Complete Deletion Successful</h6>
                            <p>${data.message}</p>
                            <div class="mt-3">
                                <h6>🗑️ Removed from all systems:</h6>
                                <div class="row text-center">
                                    <div class="col-3">
                                        <div class="text-success">✅</div>
                                        <small>Excel</small>
                                    </div>
                                    <div class="col-3">
                                        <div class="text-success">✅</div>
                                        <small>Tokens</small>
                                    </div>
                                    <div class="col-3">
                                        <div class="text-success">✅</div>
                                        <small>DB1</small>
                                    </div>
                                    <div class="col-3">
                                        <div class="text-success">✅</div>
                                        <small>DB2</small>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Symbols: ${symbols.join(', ')} have been completely removed from the trading system.
                                </small>
                            </div>
                        </div>
                    `;
                    document.getElementById('symbolsToDelete').value = '';

                    // Refresh symbol stats
                    refreshSymbolStatsNow();
                } else {
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Deletion Failed</h6>
                            <p>Error: ${data.error}</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('❌ Delete error:', error);
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Deletion Failed</h6>
                        <p>Failed to delete symbols: ${error.message}</p>
                    </div>
                `;
            }
        }





        // Additional Symbol Manager Functions
        async function searchSymbolsNow() {
            console.log('🔍 Searching symbols...');

            const query = document.getElementById('symbolSearchQuery').value.trim();
            if (!query) {
                alert('Please enter a search query');
                return;
            }

            const container = document.getElementById('symbolManagerResults');
            container.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-info" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Searching for "${query}"...</p>
                </div>
            `;

            try {
                const response = await fetch(`/api/symbols/search?q=${encodeURIComponent(query)}`);
                const data = await response.json();

                console.log('🔍 Search response:', data);

                if (data.success) {
                    const results = data.results || [];
                    let resultsHtml = '';

                    if (results.length === 0) {
                        resultsHtml = `
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>No symbols found matching "${query}"
                            </div>
                        `;
                    } else {
                        resultsHtml = `
                            <div class="alert alert-success">
                                <h6><i class="fas fa-search me-2"></i>Search Results</h6>
                                <p>Found ${results.length} symbols matching "${query}":</p>
                                <div class="mt-2">
                                    ${results.map(symbol => `
                                        <span class="badge bg-primary me-1 mb-1">${symbol}</span>
                                    `).join('')}
                                </div>
                            </div>
                        `;
                    }

                    container.innerHTML = resultsHtml;
                } else {
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>Error: ${data.error}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('❌ Search error:', error);
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>Failed to search symbols: ${error.message}
                    </div>
                `;
            }
        }

        async function cleanSymbolsNow() {
            console.log('🧹 Cleaning symbols...');

            if (!confirm('This will clean duplicate symbols and invalid entries. Continue?')) {
                return;
            }

            const container = document.getElementById('symbolManagerResults');
            container.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-warning" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Cleaning symbols...</p>
                </div>
            `;

            try {
                const response = await fetch('/api/symbols/clean-duplicates', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const data = await response.json();

                console.log('🧹 Clean response:', data);

                if (data.success) {
                    container.innerHTML = `
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>${data.message}
                        </div>
                    `;
                    refreshSymbolStatsNow();
                } else {
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>Error: ${data.error}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('❌ Clean error:', error);
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>Failed to clean symbols: ${error.message}
                    </div>
                `;
            }
        }

        async function exportSymbolsNow() {
            console.log('📥 Exporting symbols...');

            const container = document.getElementById('symbolManagerResults');
            container.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-success" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Preparing export...</p>
                </div>
            `;

            try {
                const response = await fetch('/api/symbols/export');

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `symbols_export_${new Date().toISOString().split('T')[0]}.xlsx`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    container.innerHTML = `
                        <div class="alert alert-success">
                            <i class="fas fa-download me-2"></i>Symbols exported successfully!
                        </div>
                    `;
                } else {
                    const data = await response.json();
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>Export failed: ${data.error}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('❌ Export error:', error);
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>Failed to export symbols: ${error.message}
                    </div>
                `;
            }
        }

        async function refreshSymbolStatsNow() {
            console.log('🔄 Refreshing symbol stats...');

            // Load symbol statistics
            const statsContainer = document.getElementById('symbolStats');
            statsContainer.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading stats...</p>
                </div>
            `;

            try {
                const response = await fetch('/api/symbols/stats');
                const data = await response.json();

                console.log('📊 Stats response:', data);

                if (data.success) {
                    const stats = data.stats;
                    statsContainer.innerHTML = `
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="metric-value text-primary">${stats.total_symbols}</div>
                                <div class="metric-label">Total Symbols</div>
                            </div>
                            <div class="col-6">
                                <div class="metric-value text-success">${stats.symbols_with_tokens}</div>
                                <div class="metric-label">With Tokens</div>
                            </div>
                        </div>
                        <div class="row text-center mt-2">
                            <div class="col-6">
                                <div class="metric-value text-warning">${stats.symbols_without_tokens}</div>
                                <div class="metric-label">Missing Tokens</div>
                            </div>
                            <div class="col-6">
                                <div class="metric-value text-info">${stats.coverage_percentage}%</div>
                                <div class="metric-label">Coverage</div>
                            </div>
                        </div>
                    `;
                } else {
                    statsContainer.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>Error loading stats: ${data.error}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('❌ Stats error:', error);
                statsContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>Failed to load stats: ${error.message}
                    </div>
                `;
            }

            // Load missing tokens
            const missingContainer = document.getElementById('missingTokensSection');
            missingContainer.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-warning" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading missing tokens...</p>
                </div>
            `;

            try {
                const response = await fetch('/api/symbols/missing-tokens');
                const data = await response.json();

                console.log('⚠️ Missing tokens response:', data);

                if (data.success) {
                    const missingTokens = data.missing_tokens || [];

                    if (missingTokens.length === 0) {
                        missingContainer.innerHTML = `
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>All symbols have tokens!
                            </div>
                        `;
                    } else {
                        missingContainer.innerHTML = `
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>Missing Tokens (${missingTokens.length})</h6>
                                <div class="mt-2" style="max-height: 200px; overflow-y: auto;">
                                    ${missingTokens.map(symbol => `
                                        <span class="badge bg-warning text-dark me-1 mb-1">${symbol}</span>
                                    `).join('')}
                                </div>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        Coverage: ${data.coverage_percentage}% (${data.total_symbols - missingTokens.length}/${data.total_symbols})
                                    </small>
                                </div>
                            </div>
                        `;
                    }
                } else {
                    missingContainer.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>Error loading missing tokens: ${data.error}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('❌ Missing tokens error:', error);
                missingContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>Failed to load missing tokens: ${error.message}
                    </div>
                `;
            }
        }



        // Auto-load symbol stats when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Load stats when Symbol Manager tab is clicked
            const symbolManagerTab = document.getElementById('symbol-manager-tab');
            if (symbolManagerTab) {
                symbolManagerTab.addEventListener('click', function() {
                    setTimeout(refreshSymbolStatsNow, 100);
                });
            }

            // Load integrity status when Data Integrity tab is clicked
            const integrityTab = document.getElementById('integrity-tab');
            if (integrityTab) {
                integrityTab.addEventListener('click', function() {
                    setTimeout(getIntegrityStatus, 100);
                    setTimeout(loadLayer2Confirmations, 200);
                });
            }

            // Auto-refresh Layer 2 confirmations every 30 seconds
            setInterval(loadLayer2Confirmations, 30000);



            // Auto-refresh Priority Queue every 60 seconds
            setInterval(refreshPriorityQueue, 60000);

            // Load priority queue when Data Integrity tab is clicked
            if (integrityTab) {
                integrityTab.addEventListener('click', function() {
                    setTimeout(refreshPriorityQueue, 300);
                });
            }
        });

        // ===== DATA INTEGRITY CHECK FUNCTIONS =====
        // NOTE: Functions moved to new_dashboard.js - these are just placeholders for compatibility

        async function getIntegrityStatus() {
            console.log('📊 Getting integrity status...');

            const container = document.getElementById('integrityStatus');
            container.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-info" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading integrity status...</p>
                </div>
            `;

            try {
                const response = await fetch('/api/data-integrity/status');
                const data = await response.json();

                console.log('📊 Status response:', data);

                if (data.success) {
                    const status = data.status;
                    container.innerHTML = `
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="metric-value text-primary">${status.total_symbols}</div>
                                <div class="metric-label">Total Symbols</div>
                            </div>
                            <div class="col-6">
                                <div class="metric-value text-success">${status.records_today}</div>
                                <div class="metric-label">Records Today</div>
                            </div>
                        </div>
                        <div class="row text-center mt-2">
                            <div class="col-6">
                                <div class="metric-value text-warning">${status.missing_intervals || 0}</div>
                                <div class="metric-label">Missing Intervals</div>
                            </div>
                            <div class="col-6">
                                <div class="metric-value text-info">${status.coverage_percentage || 100}%</div>
                                <div class="metric-label">Coverage</div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                Last updated: ${new Date().toLocaleTimeString()}
                            </small>
                        </div>
                    `;
                } else {
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>Error loading status: ${data.error}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('❌ Status error:', error);
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>Failed to load status: ${error.message}
                    </div>
                `;
            }
        }

        async function runComprehensiveCheck() {
            console.log('🔍 Running comprehensive integrity check...');

            const container = document.getElementById('integrityResults');
            container.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-search me-2"></i>Comprehensive Integrity Check</h6>
                    <div class="text-center mt-3">
                        <div class="spinner-border text-success" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Running comprehensive check for ALL 222 symbols...</p>
                        <small class="text-muted">This may take several minutes. Check logs for progress.</small>
                    </div>
                </div>
            `;

            try {
                const response = await fetch('/api/data-integrity/comprehensive-check', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const data = await response.json();

                console.log('🔍 Comprehensive check response:', data);

                if (data.success) {
                    container.innerHTML = `
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>Comprehensive Check Started</h6>
                            <p>${data.message}</p>
                            <div class="mt-3">
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-success"
                                         role="progressbar" style="width: 100%"></div>
                                </div>
                                <small class="text-success mt-2 d-block">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Running in background. Check logs for real-time progress.
                                </small>
                            </div>
                        </div>
                    `;
                } else {
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Comprehensive Check Failed</h6>
                            <p>Error: ${data.error}</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('❌ Comprehensive check error:', error);
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Comprehensive Check Failed</h6>
                        <p>Failed to run comprehensive check: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function runPriorityAnalysis() {
            console.log('🎯 Running priority analysis...');

            const container = document.getElementById('integrityResults');
            container.innerHTML = `
                <div class="alert alert-warning">
                    <h6><i class="fas fa-trophy me-2"></i>Priority Analysis</h6>
                    <div class="text-center mt-3">
                        <div class="spinner-border text-warning" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Analyzing SQL data for GOLD/SILVER/BRONZE batches...</p>
                        <small class="text-muted">Creating priority queue for optimized data fetching</small>
                    </div>
                </div>
            `;

            try {
                const response = await fetch('/api/priority-queue/fetch', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const data = await response.json();

                console.log('🎯 Priority analysis response:', data);

                if (data.success) {
                    container.innerHTML = `
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-trophy me-2"></i>Priority Analysis Complete</h6>
                            <p>${data.message}</p>
                            <div class="mt-3">
                                <h6>🎯 Priority Batches Created:</h6>
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="text-warning">🥇</div>
                                        <small>GOLD</small>
                                    </div>
                                    <div class="col-4">
                                        <div class="text-secondary">🥈</div>
                                        <small>SILVER</small>
                                    </div>
                                    <div class="col-4">
                                        <div class="text-warning">🥉</div>
                                        <small>BRONZE</small>
                                    </div>
                                </div>
                                <small class="text-muted mt-2 d-block">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Priority optimization active for enhanced data fetching
                                </small>
                            </div>
                        </div>
                    `;
                } else {
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Priority Analysis Failed</h6>
                            <p>Error: ${data.error}</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('❌ Priority analysis error:', error);
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Priority Analysis Failed</h6>
                        <p>Failed to run priority analysis: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function stopAutoFetch() {
            console.log('🛑 Stopping auto fetch...');

            const container = document.getElementById('integrityResults');

            if (!confirm('🛑 STOP AUTO FETCH\n\nThis will stop automatic data fetching.\nAre you sure you want to continue?')) {
                return;
            }

            container.innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-stop me-2"></i>Stopping Auto Fetch</h6>
                    <div class="text-center mt-3">
                        <div class="spinner-border text-danger" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Stopping automatic data fetching...</p>
                    </div>
                </div>
            `;

            try {
                const response = await fetch('/api/stop-data-fetching', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const data = await response.json();

                console.log('🛑 Stop fetch response:', data);

                if (data.success) {
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-stop-circle me-2"></i>Auto Fetch Stopped</h6>
                            <p>${data.message}</p>
                            <div class="mt-3">
                                <small class="text-danger">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    Automatic data fetching has been stopped. You can restart it from the main controls.
                                </small>
                            </div>
                        </div>
                    `;
                } else {
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Stop Failed</h6>
                            <p>Error: ${data.error}</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('❌ Stop fetch error:', error);
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Stop Failed</h6>
                        <p>Failed to stop auto fetch: ${error.message}</p>
                    </div>
                `;
            }
        }



        // ===== PRIORITY QUEUE FUNCTIONS =====

        async function refreshPriorityQueue() {
            console.log('🎯 Refreshing priority queue status...');

            const container = document.getElementById('priorityBatches');
            const timestampElement = document.getElementById('priorityTimestamp');

            container.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-warning" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Analyzing SQL DB1 for priority patterns...</p>
                </div>
            `;

            try {
                const response = await fetch('/api/priority-queue/status');
                const data = await response.json();

                console.log('🎯 Priority queue response:', data);

                if (data.success) {
                    const batches = data.batches;
                    const timestamp = data.timestamp;

                    // Update timestamp
                    if (timestampElement) {
                        timestampElement.textContent = `Last updated: ${timestamp}`;
                    }

                    // Display priority batches
                    container.innerHTML = `
                        <div class="col-3">
                            <div class="text-center">
                                <div class="priority-badge gold">
                                    <i class="fas fa-trophy text-warning"></i>
                                    <div class="priority-count">${batches.GOLD?.count || 0}</div>
                                </div>
                                <div class="priority-label">🥇 GOLD</div>
                                <small class="text-muted">Critical Signals</small>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="text-center">
                                <div class="priority-badge silver">
                                    <i class="fas fa-medal text-secondary"></i>
                                    <div class="priority-count">${batches.SILVER?.count || 0}</div>
                                </div>
                                <div class="priority-label">🥈 SILVER</div>
                                <small class="text-muted">High Priority</small>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="text-center">
                                <div class="priority-badge bronze">
                                    <i class="fas fa-award text-warning"></i>
                                    <div class="priority-count">${batches.BRONZE?.count || 0}</div>
                                </div>
                                <div class="priority-label">🥉 BRONZE</div>
                                <small class="text-muted">Medium Priority</small>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="text-center">
                                <div class="priority-badge remaining">
                                    <i class="fas fa-circle text-muted"></i>
                                    <div class="priority-count">${batches.REMAINING?.count || 0}</div>
                                </div>
                                <div class="priority-label">⚪ REMAINING</div>
                                <small class="text-muted">Discovery Mode</small>
                            </div>
                        </div>
                    `;

                    // Add CSS for priority badges if not already added
                    if (!document.getElementById('priority-queue-styles')) {
                        const style = document.createElement('style');
                        style.id = 'priority-queue-styles';
                        style.textContent = `
                            .priority-badge {
                                background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                                border: 2px solid #dee2e6;
                                border-radius: 15px;
                                padding: 15px;
                                margin-bottom: 10px;
                                transition: all 0.3s ease;
                            }
                            .priority-badge:hover {
                                transform: translateY(-2px);
                                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                            }
                            .priority-badge.gold {
                                border-color: #ffc107;
                                background: linear-gradient(135deg, #fff3cd, #ffeaa7);
                            }
                            .priority-badge.silver {
                                border-color: #6c757d;
                                background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                            }
                            .priority-badge.bronze {
                                border-color: #fd7e14;
                                background: linear-gradient(135deg, #fff3cd, #ffeaa7);
                            }
                            .priority-badge.remaining {
                                border-color: #adb5bd;
                                background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                            }
                            .priority-count {
                                font-size: 24px;
                                font-weight: bold;
                                margin: 5px 0;
                            }
                            .priority-label {
                                font-weight: bold;
                                margin-bottom: 5px;
                            }
                        `;
                        document.head.appendChild(style);
                    }
                } else {
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>Error loading priority queue: ${data.error}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('❌ Priority queue error:', error);
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>Failed to load priority queue: ${error.message}
                    </div>
                `;
            }
        }

        async function analyzePriorityPatterns() {
            console.log('📊 Analyzing priority patterns...');

            const container = document.getElementById('priorityBatches');

            container.innerHTML = `
                <div class="alert alert-info">
                    <h6><i class="fas fa-chart-line me-2"></i>Analyzing SQL DB1 Patterns</h6>
                    <div class="text-center mt-3">
                        <div class="spinner-border text-info" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Running pattern analysis on all 223 symbols...</p>
                        <small class="text-muted">Checking 4-FALL+2-RISE patterns, positions, and profit levels</small>
                    </div>
                </div>
            `;

            try {
                const response = await fetch('/api/priority-queue/analyze', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const data = await response.json();

                console.log('📊 Manual pattern analysis response:', data);

                if (data.success) {
                    container.innerHTML = `
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>Pattern Analysis Complete</h6>
                            <p>${data.message}</p>
                            <div class="mt-3">
                                <small class="text-success">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Priority queue updated based on SQL DB1 analysis. Check logs for detailed results.
                                </small>
                            </div>
                        </div>
                    `;

                    // Refresh the priority queue display
                    setTimeout(refreshPriorityQueue, 2000);
                } else {
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Pattern Analysis Failed</h6>
                            <p>Error: ${data.error}</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('❌ Pattern analysis error:', error);
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Pattern Analysis Failed</h6>
                        <p>Failed to analyze patterns: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function showPriorityDetails() {
            console.log('📋 Showing priority details...');

            try {
                const response = await fetch('/api/priority-queue/status');
                const data = await response.json();

                if (data.success) {
                    const batches = data.batches;
                    let detailsHtml = '';

                    // Show details for each batch
                    for (const [tier, batch] of Object.entries(batches)) {
                        const tierEmoji = {'GOLD': '🥇', 'SILVER': '🥈', 'BRONZE': '🥉', 'REMAINING': '⚪'}[tier];
                        const symbols = batch.symbols || [];

                        detailsHtml += `
                            <div class="mb-3">
                                <h6>${tierEmoji} ${tier} (${symbols.length} symbols)</h6>
                                <div class="row">
                        `;

                        symbols.slice(0, 8).forEach(symbol => {
                            const statusClass = symbol.position_status ? 'success' : 'info';
                            const statusText = symbol.position_status || symbol.pattern_status || 'DISCOVERY';

                            detailsHtml += `
                                <div class="col-3 mb-2">
                                    <div class="card card-sm">
                                        <div class="card-body p-2">
                                            <div class="d-flex justify-content-between">
                                                <strong>${symbol.symbol}</strong>
                                                <span class="badge bg-${statusClass}">${symbol.priority_score}</span>
                                            </div>
                                            <small class="text-muted">${statusText}</small>
                                            ${symbol.profit_amount ? `<br><small class="text-success">₹${symbol.profit_amount}</small>` : ''}
                                        </div>
                                    </div>
                                </div>
                            `;
                        });

                        if (symbols.length > 8) {
                            detailsHtml += `
                                <div class="col-12">
                                    <small class="text-muted">... and ${symbols.length - 8} more symbols</small>
                                </div>
                            `;
                        }

                        detailsHtml += `
                                </div>
                            </div>
                        `;
                    }

                    const container = document.getElementById('priorityBatches');
                    container.innerHTML = `
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-list me-2"></i>Priority Queue Details</h6>
                                <div class="mt-3">
                                    ${detailsHtml}
                                </div>
                                <div class="mt-3">
                                    <button class="btn btn-outline-secondary btn-sm" onclick="refreshPriorityQueue()">
                                        <i class="fas fa-arrow-left me-1"></i>Back to Summary
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    alert('Error loading priority details: ' + data.error);
                }
            } catch (error) {
                console.error('❌ Priority details error:', error);
                alert('Failed to load priority details: ' + error.message);
            }
        }

        // Refresh all Data Integrity data
        async function refreshIntegrityData() {
            console.log('🔄 Refreshing all integrity data...');

            // Refresh status
            await getIntegrityStatus();

            // Refresh Layer 2 confirmations
            await loadLayer2Confirmations();

            // Show refresh confirmation
            const container = document.getElementById('integrityResults');
            container.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-sync me-2"></i>Data Refreshed</h6>
                    <p>All integrity data has been refreshed successfully.</p>
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        Last refreshed: ${new Date().toLocaleTimeString()}
                    </small>
                </div>
            `;
        }

        // Real-time Active Positions Management
        let positionsRefreshInterval;

        // Load active signals from DB1
        async function loadActivePositions() {
            console.log('🎯 Loading active signals from DB1...');

            try {
                const response = await fetch('/api/active-positions/realtime');
                const data = await response.json();

                if (data.success) {
                    displayActivePositions(data.positions);
                    updatePositionsSummary(data.positions);

                    // Update last refresh time
                    document.getElementById('positionsLastUpdate').textContent = `Last updated: ${data.last_updated}`;

                    console.log(`✅ Loaded ${data.total_positions} active signals from DB1`);
                } else {
                    console.error('❌ Failed to load active positions:', data.error);
                    showNoPositionsMessage();
                }
            } catch (error) {
                console.error('❌ Error loading active positions:', error);
                showNoPositionsMessage();
            }
        }

        // Display active positions in table
        function displayActivePositions(positions) {
            const tbody = document.getElementById('positionsTableBody');

            if (!positions || positions.length === 0) {
                showNoPositionsMessage();
                return;
            }

            tbody.innerHTML = positions.map(position => {
                // Determine row styling based on target achievement and P&L
                let rowClass = '';
                let symbolBadge = '';
                let pnlClass = position.pnl >= 0 ? 'text-success' : 'text-danger';
                let pnlIcon = position.pnl >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';

                if (position.target_achieved) {
                    rowClass = 'table-success';
                    symbolBadge = '<span class="badge bg-success ms-2">TARGET ACHIEVED</span>';
                }

                return `
                    <tr class="${rowClass}">
                        <td>
                            <strong>${position.symbol}</strong>
                            ${symbolBadge}
                        </td>
                        <td>₹${position.buy_price}</td>
                        <td>
                            <strong>₹${position.current_price}</strong>
                            <br><small class="text-muted">${position.last_update}</small>
                        </td>
                        <td>${position.quantity}</td>
                        <td>₹${position.investment}</td>
                        <td>₹${position.current_value}</td>
                        <td>
                            ₹${position.target_price}
                            ${position.target_achieved ? '<i class="fas fa-check-circle text-success ms-1"></i>' : ''}
                        </td>
                        <td class="${pnlClass}">
                            <i class="fas ${pnlIcon} me-1"></i>
                            <strong>₹${position.pnl}</strong>
                        </td>
                        <td class="${pnlClass}">
                            <strong>${position.pnl_percentage}%</strong>
                        </td>
                        <td>
                            <small>${position.buy_time}</small>
                        </td>
                        <td>
                            <span class="badge ${position.target_achieved ? 'bg-success' : 'bg-warning'}">${position.status}</span>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // Update positions summary metrics
        function updatePositionsSummary(positions) {
            if (!positions || positions.length === 0) {
                // Reset all metrics to 0
                document.getElementById('totalActivePositions').textContent = '0';
                document.getElementById('totalInvestment').textContent = '₹0';
                document.getElementById('currentValue').textContent = '₹0';
                document.getElementById('totalPnL').textContent = '₹0';
                document.getElementById('totalPnL').className = 'metric-value';
                document.getElementById('targetsAchieved').textContent = '0';
                document.getElementById('targetsRemaining').textContent = '0';
                return;
            }

            // Calculate summary metrics
            const totalPositions = positions.length;
            const totalInvestment = positions.reduce((sum, p) => sum + p.investment, 0);
            const totalCurrentValue = positions.reduce((sum, p) => sum + p.current_value, 0);
            const totalPnL = positions.reduce((sum, p) => sum + p.pnl, 0);
            const targetsAchieved = positions.filter(p => p.target_achieved).length;
            const targetsRemaining = totalPositions - targetsAchieved;

            // Update display
            document.getElementById('totalActivePositions').textContent = totalPositions;
            document.getElementById('totalInvestment').textContent = `₹${totalInvestment.toFixed(2)}`;
            document.getElementById('currentValue').textContent = `₹${totalCurrentValue.toFixed(2)}`;

            // Style total P&L based on profit/loss
            const totalPnLElement = document.getElementById('totalPnL');
            totalPnLElement.textContent = `₹${totalPnL.toFixed(2)}`;
            totalPnLElement.className = `metric-value ${totalPnL >= 0 ? 'text-success' : 'text-danger'}`;

            document.getElementById('targetsAchieved').textContent = targetsAchieved;
            document.getElementById('targetsRemaining').textContent = targetsRemaining;
        }

        // Show no positions message
        function showNoPositionsMessage() {
            const tbody = document.getElementById('positionsTableBody');
            tbody.innerHTML = `
                <tr>
                    <td colspan="11" class="text-center text-muted">
                        <div class="py-3">
                            <i class="fas fa-info-circle me-2"></i>
                            No active positions. Start trading to see positions here.
                        </div>
                    </td>
                </tr>
            `;

            // Reset summary metrics
            updatePositionsSummary([]);
        }

        // Refresh active positions manually
        async function refreshActivePositions() {
            console.log('🔄 Manually refreshing active positions...');

            // Show loading state
            const tbody = document.getElementById('positionsTableBody');
            tbody.innerHTML = `
                <tr>
                    <td colspan="11" class="text-center">
                        <div class="py-3">
                            <div class="loading-spinner"></div>
                            <p class="mt-2">Refreshing positions...</p>
                        </div>
                    </td>
                </tr>
            `;

            await loadActivePositions();
        }

        // Start auto-refresh for positions (every 15 minutes)
        function startPositionsAutoRefresh() {
            // Clear any existing interval
            if (positionsRefreshInterval) {
                clearInterval(positionsRefreshInterval);
            }

            // Set up 15-minute auto-refresh
            positionsRefreshInterval = setInterval(() => {
                console.log('⏰ Auto-refreshing active positions (15-minute interval)...');
                loadActivePositions();
            }, 15 * 60 * 1000); // 15 minutes

            console.log('✅ Auto-refresh enabled for active positions (every 15 minutes)');
        }

        // Stop auto-refresh
        function stopPositionsAutoRefresh() {
            if (positionsRefreshInterval) {
                clearInterval(positionsRefreshInterval);
                positionsRefreshInterval = null;
                console.log('🛑 Auto-refresh stopped for active positions');
            }
        }



        // Paper Trading Management (DB2 Brain)
        let paperTradingRefreshInterval;

        // Load paper trading records from DB2
        async function loadPaperTradingRecords() {
            console.log('📊 Loading paper trading records from DB2...');

            try {
                const response = await fetch('/api/paper-trading/records');
                const data = await response.json();

                if (data.success) {
                    displayPaperTradingRecords(data.records);
                    updatePaperTradingStats(data.stats);
                    console.log(`✅ Loaded ${data.records.length} paper trading records`);
                } else {
                    console.error('❌ Failed to load paper trading records:', data.error);
                    showNoPaperTradingMessage();
                }
            } catch (error) {
                console.error('❌ Error loading paper trading records:', error);
                showNoPaperTradingMessage();
            }
        }

        // Display paper trading records in table
        function displayPaperTradingRecords(records) {
            const tbody = document.getElementById('paperTradesTableBody');

            if (!records || records.length === 0) {
                showNoPaperTradingMessage();
                return;
            }

            tbody.innerHTML = records.map(record => {
                let statusClass = '';
                let statusBadge = '';
                let profitClass = '';
                let profitIcon = '';

                // Get profit value (handle different field names)
                const profitValue = parseFloat(record.profit_loss || record.actual_profit || 0);

                if (record.status === 'ACTIVE') {
                    statusClass = 'table-warning';
                    statusBadge = '<span class="badge bg-warning">ACTIVE</span>';
                } else if (record.status === 'SOLD' || record.status === 'COMPLETED') {
                    statusClass = profitValue >= 0 ? 'table-success' : 'table-danger';
                    statusBadge = '<span class="badge bg-success">COMPLETED</span>';
                }

                if (profitValue > 0) {
                    profitClass = 'text-success';
                    profitIcon = 'fa-arrow-up';
                } else if (profitValue < 0) {
                    profitClass = 'text-danger';
                    profitIcon = 'fa-arrow-down';
                } else {
                    profitClass = 'text-muted';
                    profitIcon = 'fa-minus';
                }

                return `
                    <tr class="${statusClass}">
                        <td><strong>${record.symbol}</strong></td>
                        <td>₹${record.buy_price}</td>
                        <td>${record.sell_price && record.sell_price !== 'N/A' ? '₹' + record.sell_price : 'N/A'}</td>
                        <td>${record.quantity || 0}</td>
                        <td>₹${record.investment}</td>
                        <td class="${profitClass}">
                            <i class="fas ${profitIcon} me-1"></i>
                            <strong>₹${profitValue.toFixed(0)}</strong>
                        </td>
                        <td>${statusBadge}</td>
                        <td><small>${record.buy_time}</small></td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button class="btn btn-outline-warning btn-sm" onclick="forceAction('${record.symbol}', 'HOLD', ${parseFloat(record.buy_price)})" title="Force Hold">
                                    <i class="fas fa-hand-paper"></i>
                                </button>
                                ${record.status === 'ACTIVE' ? `
                                <button class="btn btn-outline-success btn-sm" onclick="forceAction('${record.symbol}', 'BUY', ${parseFloat(record.buy_price)})" title="Force Buy">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button class="btn btn-outline-danger btn-sm" onclick="forceAction('${record.symbol}', 'SELL', ${parseFloat(record.buy_price)})" title="Force Sell">
                                    <i class="fas fa-minus"></i>
                                </button>
                                ` : ''}
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // Update paper trading statistics
        function updatePaperTradingStats(stats) {
            document.getElementById('totalPaperTrades').textContent = stats.total_trades || 0;
            document.getElementById('completedTrades').textContent = stats.completed_trades || 0;
            document.getElementById('activeTrades').textContent = stats.active_trades || 0;
            document.getElementById('profitableTrades').textContent = stats.profitable_trades || 0;
            document.getElementById('avgProfit').textContent = `₹${stats.avg_profit || 0}`;
            document.getElementById('uniqueSymbolsTraded').textContent = stats.unique_symbols || 0;

            // Update DB2 brain metrics - fetch from dedicated API
            updateDB2BrainMetrics();
        }

        // Show no paper trading message
        function showNoPaperTradingMessage() {
            const tbody = document.getElementById('paperTradesTableBody');
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center text-muted">
                        <div class="py-3">
                            <i class="fas fa-info-circle me-2"></i>
                            No paper trading records found. Start trading to see records here.
                        </div>
                    </td>
                </tr>
            `;

            // Reset stats
            updatePaperTradingStats({});
        }

        // Load Layer 2 confirmations
        async function loadLayer2Confirmations() {
            console.log('🔄 Loading Layer 2 confirmations...');

            try {
                const response = await fetch('/api/layer2-confirmations');
                const data = await response.json();

                if (data.success) {
                    displayLayer2Confirmations(data.confirmations);
                    updateLayer2Summary(data.summary);
                    document.getElementById('layer2LastUpdate').textContent = `Last updated: ${data.last_updated}`;
                    console.log(`✅ Loaded ${data.confirmations.length} Layer 2 confirmations`);
                } else {
                    console.error('❌ Failed to load Layer 2 confirmations:', data.error);
                }
            } catch (error) {
                console.error('❌ Error loading Layer 2 confirmations:', error);
            }
        }

        // Display Layer 2 confirmations
        function displayLayer2Confirmations(confirmations) {
            const container = document.getElementById('layer2ConfirmationsTable');
            const integrityContainer = document.getElementById('layer2Confirmations');

            if (!confirmations || confirmations.length === 0) {
                // Update DB2 section
                container.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-info-circle me-2"></i>
                        No pending Layer 2 confirmations
                    </div>
                `;

                // Update Data Integrity section
                if (integrityContainer) {
                    integrityContainer.innerHTML = `
                        <div class="text-center text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            No pending confirmations
                        </div>
                    `;
                }
                return;
            }

            const tableHtml = `
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>Symbol</th>
                                <th>Original Signal</th>
                                <th>Price Trend</th>
                                <th>Pattern Sequence</th>
                                <th>Confirmation Status</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${confirmations.map(conf => {
                                let statusBadge = '';
                                let actionButton = '';

                                if (conf.buy_confirmation) {
                                    statusBadge = '<span class="badge bg-success">BUY READY</span>';
                                    actionButton = '<button class="btn btn-success btn-sm" onclick="executeDB2Signal(\'' + conf.symbol + '\', \'BUY\', ' + conf.current_price + ')">Execute BUY</button>';
                                } else if (conf.sell_confirmation) {
                                    statusBadge = '<span class="badge bg-danger">SELL READY</span>';
                                    actionButton = '<button class="btn btn-danger btn-sm" onclick="executeDB2Signal(\'' + conf.symbol + '\', \'SELL\', ' + conf.current_price + ')">Execute SELL</button>';
                                } else {
                                    statusBadge = '<span class="badge bg-warning">PENDING</span>';
                                    actionButton = '<small class="text-muted">Waiting...</small>';
                                }

                                return `
                                    <tr>
                                        <td><strong>${conf.symbol}</strong></td>
                                        <td><span class="badge bg-info">${conf.original_signal}</span></td>
                                        <td>
                                            ₹${conf.previous_price} → ₹${conf.current_price}
                                            <span class="badge ${conf.trend === 'RISE' ? 'bg-success' : 'bg-danger'}">${conf.trend}</span>
                                        </td>
                                        <td><small>${conf.pattern_sequence}</small></td>
                                        <td>${statusBadge}</td>
                                        <td>${actionButton}</td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            // Update DB2 section
            container.innerHTML = tableHtml;

            // Update Data Integrity section with simplified view
            if (integrityContainer) {
                const pendingCount = confirmations.filter(conf => !conf.ready_for_execution).length;
                const readyCount = confirmations.filter(conf => conf.ready_for_execution).length;

                integrityContainer.innerHTML = `
                    <div class="alert alert-info">
                        <h6><i class="fas fa-clock me-2"></i>Layer 2 Confirmations Active</h6>
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="metric-value text-warning">${pendingCount}</div>
                                <div class="metric-label">Pending</div>
                            </div>
                            <div class="col-6">
                                <div class="metric-value text-success">${readyCount}</div>
                                <div class="metric-label">Ready</div>
                            </div>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                ${confirmations.length} total confirmation${confirmations.length !== 1 ? 's' : ''} in progress
                            </small>
                        </div>
                    </div>
                `;
            }
        }

        // Update Layer 2 summary
        function updateLayer2Summary(summary) {
            // Update DB2 section stats
            document.getElementById('db2PendingBuy').textContent = summary.pending_buy || 0;
            document.getElementById('db2PendingSell').textContent = summary.pending_sell || 0;
            document.getElementById('db2ReadyExecution').textContent = summary.ready_for_execution || 0;

            // Update Data Integrity section stats
            document.getElementById('pendingBuyCount').textContent = summary.pending_buy || 0;
            document.getElementById('pendingSellCount').textContent = summary.pending_sell || 0;
            document.getElementById('confirmedTodayCount').textContent = summary.confirmed_today || 0;
        }

        // Load active positions on page load and start auto-refresh
        document.addEventListener('DOMContentLoaded', function() {
            loadActivePositions();
            startPositionsAutoRefresh();

            // Load paper trading data
            loadPaperTradingRecords();
            loadLayer2Confirmations();
            updateDB2BrainMetrics();

            // Set up auto-refresh for paper trading and DB2 metrics
            setInterval(() => {
                loadPaperTradingRecords();
                loadLayer2Confirmations();
                updateDB2BrainMetrics();
            }, 30000); // Refresh every 30 seconds
        });

        // Manual override controls
        async function forceAction(symbol, action, price) {
            console.log(`🎯 Force ${action} for ${symbol} at ₹${price}`);

            const confirmMessage = `⚠️ MANUAL OVERRIDE ⚠️\n\nAre you sure you want to FORCE ${action} for ${symbol}?\n\nPrice: ₹${price}\nAction: ${action}\n\nThis will override all automatic trading logic.`;

            if (!confirm(confirmMessage)) {
                return;
            }

            try {
                const response = await fetch('/api/paper-trading/force-action', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ symbol, action, price })
                });

                const data = await response.json();

                if (data.success) {
                    console.log(`✅ Force ${action} successful:`, data.message);
                    alert(`✅ ${data.message}`);

                    // Refresh data
                    loadPaperTradingRecords();
                    loadLayer2Confirmations();
                } else {
                    console.error(`❌ Force ${action} failed:`, data.error);
                    alert(`❌ Error: ${data.error}`);
                }
            } catch (error) {
                console.error(`❌ Error executing force ${action}:`, error);
                alert(`❌ Failed to execute force ${action}: ${error.message}`);
            }
        }

        // Execute Layer 2 confirmation
        async function executeConfirmation(symbol, action, price) {
            console.log(`🔄 Executing Layer 2 confirmation: ${action} for ${symbol} at ₹${price}`);

            const confirmMessage = `🔄 LAYER 2 CONFIRMATION\n\nExecute ${action} for ${symbol}?\n\nPrice: ₹${price}\nConfirmation: Layer 2 pattern completed\n\nProceed with execution?`;

            if (!confirm(confirmMessage)) {
                return;
            }

            try {
                const response = await fetch('/api/paper-trading/force-action', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ symbol, action, price })
                });

                const data = await response.json();

                if (data.success) {
                    console.log(`✅ Layer 2 execution successful:`, data.message);
                    alert(`✅ ${data.message}`);

                    // Refresh data
                    loadPaperTradingRecords();
                    loadLayer2Confirmations();
                } else {
                    console.error(`❌ Layer 2 execution failed:`, data.error);
                    alert(`❌ Error: ${data.error}`);
                }
            } catch (error) {
                console.error(`❌ Error executing Layer 2 confirmation:`, error);
                alert(`❌ Failed to execute confirmation: ${error.message}`);
            }
        }

        // Refresh Layer 2 confirmations manually
        async function refreshLayer2Confirmations() {
            console.log('🔄 Manually refreshing Layer 2 confirmations...');
            await loadLayer2Confirmations();
        }

        // Update DB2 Brain Metrics
        async function updateDB2BrainMetrics() {
            try {
                const response = await fetch('/api/db2-signals');
                const data = await response.json();

                if (data.success) {
                    document.getElementById('db2TotalSignals').textContent = data.total_signals || 0;
                    document.getElementById('db2PendingBuy').textContent = data.pending_buy || 0;
                    document.getElementById('db2PendingSell').textContent = data.pending_sell || 0;
                    document.getElementById('db2ReadyExecution').textContent = data.ready_for_execution || 0;
                    document.getElementById('db2ActivePositions').textContent = data.active_positions || 0;
                    document.getElementById('db2TotalProfit').textContent = `₹${data.total_profit || 0}`;

                    console.log('✅ DB2 brain metrics updated');
                } else {
                    console.error('❌ Failed to fetch DB2 signals:', data.error);
                }
            } catch (error) {
                console.error('❌ Error updating DB2 brain metrics:', error);
            }
        }







        // Download paper trades
        async function downloadPaperTrades() {
            console.log('📥 Downloading paper trades...');

            try {
                const response = await fetch('/api/paper-trading/records');
                const data = await response.json();

                if (data.success && data.records.length > 0) {
                    // Convert to CSV
                    const headers = ['Symbol', 'Buy Price', 'Sell Price', 'Quantity', 'Investment', 'Profit/Loss', 'Status', 'Buy Time', 'Sell Time'];
                    const csvContent = [
                        headers.join(','),
                        ...data.records.map(record => [
                            record.symbol,
                            record.buy_price,
                            record.sell_price || '',
                            record.quantity || 0,
                            record.investment,
                            record.profit_loss || record.actual_profit || 0,
                            record.status,
                            record.buy_time,
                            record.sell_time || ''
                        ].join(','))
                    ].join('\n');

                    // Download file
                    const blob = new Blob([csvContent], { type: 'text/csv' });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `paper_trades_${new Date().toISOString().split('T')[0]}.csv`;
                    a.click();
                    window.URL.revokeObjectURL(url);

                    console.log('✅ Paper trades downloaded successfully');
                } else {
                    alert('No paper trading records to download');
                }
            } catch (error) {
                console.error('❌ Error downloading paper trades:', error);
                alert('Failed to download paper trades');
            }
        }

        // Clear paper trades
        async function clearPaperTrades() {
            console.log('🗑️ Clearing paper trades...');

            const confirmMessage = '⚠️ CLEAR PAPER TRADES ⚠️\n\nAre you sure you want to clear all paper trading records?\n\nThis will remove:\n✅ All completed trades\n✅ All trading history\n✅ All profit/loss records\n\n❌ THIS ACTION CANNOT BE UNDONE!\n\nType "CLEAR" to confirm:';

            const userInput = prompt(confirmMessage);
            if (userInput !== 'CLEAR') {
                alert('Clear operation cancelled. You must type "CLEAR" to confirm.');
                return;
            }

            // Implementation would go here
            alert('Clear paper trades functionality - to be implemented');
        }

        // Complete reset
        async function completeReset() {
            console.log('🔄 Complete system reset...');

            const confirmMessage = '⚠️ COMPLETE SYSTEM RESET ⚠️\n\nAre you sure you want to perform a COMPLETE RESET?\n\nThis will remove:\n✅ All paper trading records\n✅ All active positions\n✅ All Layer 2 confirmations\n✅ All trading signals\n✅ All profit/loss data\n\n❌ THIS ACTION CANNOT BE UNDONE!\n\nType "RESET" to confirm:';

            const userInput = prompt(confirmMessage);
            if (userInput !== 'RESET') {
                alert('Reset operation cancelled. You must type "RESET" to confirm.');
                return;
            }

            // Implementation would go here
            alert('Complete reset functionality - to be implemented');
        }



        // Execute DB2 signal manually
        async function executeDB2Signal(symbol, action, price) {
            console.log(`💰 Executing DB2 signal: ${action} for ${symbol} at ₹${price}`);

            try {
                const response = await fetch('/api/db2-execution', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ symbol, action, price })
                });

                const data = await response.json();

                if (data.success) {
                    console.log('✅ DB2 execution successful:', data);
                    alert(`✅ DB2 Execution Successful!\n\n${data.message}\n\nExecution: ${data.execution.action} ${data.execution.symbol} @ ₹${data.execution.price}\nTime: ${data.execution.timestamp}`);

                    // Refresh data to show results
                    loadPaperTradingRecords();
                    loadLayer2Confirmations();
                } else {
                    console.error('❌ DB2 execution failed:', data.error);
                    alert(`❌ DB2 Execution Failed: ${data.error}`);
                }
            } catch (error) {
                console.error('❌ Error executing DB2 signal:', error);
                alert(`❌ Error executing DB2 signal: ${error.message}`);
            }
        }





        // Start auto-fetch system
        async function startAutoFetch() {
            console.log('🚀 Starting auto-fetch system...');

            try {
                const response = await fetch('/api/auto-fetch/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const data = await response.json();

                if (data.success) {
                    console.log('✅ Auto-fetch started:', data);
                    alert(`✅ Auto-Fetch Started!\n\n${data.message}\n\nStatus: ${data.status}\nSchedule: ${data.schedule}`);

                    // Update status display
                    loadAutoFetchStatus();
                } else {
                    console.error('❌ Failed to start auto-fetch:', data.message);
                    alert(`❌ Failed to start auto-fetch: ${data.message}`);
                }
            } catch (error) {
                console.error('❌ Error starting auto-fetch:', error);
                alert(`❌ Error starting auto-fetch: ${error.message}`);
            }
        }

        // Stop auto-fetch system
        async function stopAutoFetch() {
            console.log('🛑 Stopping auto-fetch system...');

            try {
                const response = await fetch('/api/auto-fetch/stop', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const data = await response.json();

                if (data.success) {
                    console.log('✅ Auto-fetch stopped:', data);
                    alert(`✅ Auto-Fetch Stopped!\n\n${data.message}\n\nStatus: ${data.status}`);

                    // Update status display
                    loadAutoFetchStatus();
                } else {
                    console.error('❌ Failed to stop auto-fetch:', data.message);
                    alert(`❌ Failed to stop auto-fetch: ${data.message}`);
                }
            } catch (error) {
                console.error('❌ Error stopping auto-fetch:', error);
                alert(`❌ Error stopping auto-fetch: ${error.message}`);
            }
        }

        // Load auto-fetch status
        async function loadAutoFetchStatus() {
            console.log('📊 Loading auto-fetch status...');

            try {
                const response = await fetch('/api/auto-fetch/status');
                const data = await response.json();

                if (data.success) {
                    console.log('✅ Auto-fetch status loaded:', data);

                    const status = data.status;

                    // Show detailed status
                    let statusText = `📊 Auto-Fetch System Status\n\n`;
                    statusText += `Auto-Fetch Active: ${status.auto_fetch_active ? '✅ YES' : '❌ NO'}\n`;
                    statusText += `Database Status: ${status.database_status}\n`;
                    statusText += `Last Fetch Time: ${status.last_fetch_time || 'Never'}\n`;
                    statusText += `Symbols Updated: ${status.symbols_updated}\n`;
                    statusText += `Records Last Hour: ${status.total_records_last_hour}\n`;
                    statusText += `Recent Records: ${status.recent_records}\n\n`;
                    statusText += `Schedule: ${status.schedule}\n\n`;
                    statusText += `Trading Intervals:\n`;

                    // Show intervals in groups of 5
                    for (let i = 0; i < status.next_intervals.length; i += 5) {
                        const group = status.next_intervals.slice(i, i + 5);
                        statusText += `${group.join(', ')}\n`;
                    }

                    alert(statusText);
                } else {
                    console.error('❌ Failed to load auto-fetch status:', data.error);
                    alert(`❌ Failed to load auto-fetch status: ${data.error}`);
                }
            } catch (error) {
                console.error('❌ Error loading auto-fetch status:', error);
                alert(`❌ Error loading auto-fetch status: ${error.message}`);
            }
        }

        // ===== SYMBOL MANAGER FUNCTIONS =====

        // Load symbol statistics
        async function loadSymbolStats() {
            console.log('📊 Loading symbol statistics...');

            const container = document.getElementById('symbolStats');
            container.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading statistics...</p>
                </div>
            `;

            try {
                const response = await fetch('/api/symbols/stats');
                const data = await response.json();

                if (data.success) {
                    const stats = data.stats;
                    container.innerHTML = `
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border rounded p-2 mb-2">
                                    <div class="h4 mb-0 text-primary">${stats.total_symbols}</div>
                                    <small class="text-muted">Total Symbols</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="border rounded p-2 mb-2">
                                    <div class="h4 mb-0 text-success">${stats.symbols_with_tokens}</div>
                                    <small class="text-muted">With Tokens</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="border rounded p-2 mb-2">
                                    <div class="h4 mb-0 text-warning">${stats.symbols_without_tokens}</div>
                                    <small class="text-muted">Missing Tokens</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="border rounded p-2 mb-2">
                                    <div class="h4 mb-0 text-info">${stats.coverage_percentage.toFixed(1)}%</div>
                                    <small class="text-muted">Coverage</small>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>Error: ${data.error}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('❌ Error loading symbol stats:', error);
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>Failed to load statistics: ${error.message}
                    </div>
                `;
            }
        }

        // Add new symbols
        async function addSymbols() {
            const symbolsText = document.getElementById('newSymbols').value.trim();
            if (!symbolsText) {
                alert('Please enter symbols to add');
                return;
            }

            const symbols = symbolsText.split('\n').map(s => s.trim().toUpperCase()).filter(s => s);

            console.log('➕ Adding symbols:', symbols);

            try {
                const response = await fetch('/api/symbols/add', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ symbols })
                });

                const data = await response.json();

                if (data.success) {
                    alert(`✅ Successfully added ${data.added_count} symbols!\n\nAdded: ${data.added_symbols.join(', ')}\n${data.duplicates_count > 0 ? `Duplicates skipped: ${data.duplicates.join(', ')}` : ''}`);
                    document.getElementById('newSymbols').value = '';
                    loadSymbolStats();
                    loadMissingTokens();
                } else {
                    alert(`❌ Error adding symbols: ${data.error}`);
                }
            } catch (error) {
                console.error('❌ Error adding symbols:', error);
                alert(`❌ Error adding symbols: ${error.message}`);
            }
        }

        // Delete symbols
        async function deleteSymbols() {
            const symbolsText = document.getElementById('deleteSymbols').value.trim();
            if (!symbolsText) {
                alert('Please enter symbols to delete');
                return;
            }

            const symbols = symbolsText.split('\n').map(s => s.trim().toUpperCase()).filter(s => s);

            if (!confirm(`⚠️ Are you sure you want to PERMANENTLY DELETE these symbols?\n\n${symbols.join(', ')}\n\nThis will remove them from:\n- Excel file\n- Token mappings\n- All databases\n\nThis action CANNOT be undone!`)) {
                return;
            }

            console.log('🗑️ Deleting symbols:', symbols);

            try {
                const response = await fetch('/api/symbols/delete', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ symbols })
                });

                const data = await response.json();

                if (data.success) {
                    alert(`✅ Successfully deleted symbols!\n\nDeleted: ${data.deleted_symbols.join(', ')}\n${data.not_found.length > 0 ? `Not found: ${data.not_found.join(', ')}` : ''}`);
                    document.getElementById('deleteSymbols').value = '';
                    loadSymbolStats();
                    loadMissingTokens();
                    loadAllSymbols();
                } else {
                    alert(`❌ Error deleting symbols: ${data.error}`);
                }
            } catch (error) {
                console.error('❌ Error deleting symbols:', error);
                alert(`❌ Error deleting symbols: ${error.message}`);
            }
        }

        // Search symbols
        async function searchSymbols() {
            const query = document.getElementById('symbolSearch').value.trim();
            if (!query) {
                document.getElementById('searchResults').innerHTML = '';
                return;
            }

            console.log('🔍 Searching symbols:', query);

            try {
                const response = await fetch(`/api/symbols/search?q=${encodeURIComponent(query)}`);
                const data = await response.json();

                const container = document.getElementById('searchResults');

                if (data.success && data.results.length > 0) {
                    let html = `<div class="mt-2"><small class="text-muted">Found ${data.results.length} results:</small></div>`;
                    html += '<div class="list-group mt-1">';

                    data.results.forEach(result => {
                        html += `
                            <div class="list-group-item list-group-item-action py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>${result.Symbol}</strong>
                                        ${result['Company Name'] ? `<br><small class="text-muted">${result['Company Name']}</small>` : ''}
                                    </div>
                                    <small class="text-muted">${result.Industry || 'N/A'}</small>
                                </div>
                            </div>
                        `;
                    });

                    html += '</div>';
                    container.innerHTML = html;
                } else {
                    container.innerHTML = `
                        <div class="alert alert-info mt-2">
                            <i class="fas fa-info-circle me-2"></i>No symbols found matching "${query}"
                        </div>
                    `;
                }
            } catch (error) {
                console.error('❌ Error searching symbols:', error);
                document.getElementById('searchResults').innerHTML = `
                    <div class="alert alert-danger mt-2">
                        <i class="fas fa-exclamation-triangle me-2"></i>Search failed: ${error.message}
                    </div>
                `;
            }
        }

        // Load missing tokens
        async function loadMissingTokens() {
            console.log('⚠️ Loading missing tokens...');

            const container = document.getElementById('missingTokens');
            container.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-warning" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading missing tokens...</p>
                </div>
            `;

            try {
                const response = await fetch('/api/symbols/missing-tokens');
                const data = await response.json();

                if (data.success) {
                    if (data.missing_tokens.length === 0) {
                        container.innerHTML = `
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>All symbols have tokens!</strong><br>
                                Coverage: ${data.coverage_percentage}% (${data.total_symbols - data.count}/${data.total_symbols})
                            </div>
                        `;
                    } else {
                        let html = `
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>${data.count} symbols missing tokens</strong><br>
                                Coverage: ${data.coverage_percentage}% (${data.total_symbols - data.count}/${data.total_symbols})
                            </div>
                        `;

                        html += '<div class="row">';
                        data.missing_tokens.forEach((symbol, index) => {
                            html += `
                                <div class="col-md-3 col-sm-4 col-6 mb-2">
                                    <span class="badge bg-warning text-dark">${symbol}</span>
                                </div>
                            `;
                        });
                        html += '</div>';

                        container.innerHTML = html;
                    }
                } else {
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>Error: ${data.error}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('❌ Error loading missing tokens:', error);
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>Failed to load missing tokens: ${error.message}
                    </div>
                `;
            }
        }




    </script>
</body>
</html>
