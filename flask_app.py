from flask import Flask, render_template, jsonify, request
from flask_socketio import SocketIO
import sqlite3
import logging
from datetime import datetime

# Flask app setup
app = Flask(__name__)
app.config['SECRET_KEY'] = 'trading_dashboard_secret_key'
socketio = SocketIO(app, cors_allowed_origins="*")

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database paths
DB1_PATH = 'Data/trading_data.db'
DB2_PATH = 'Data/trading_operations.db'

@app.route('/')
def dashboard():
    return render_template('dashboard.html')

@app.route('/api/db1/signals')
def get_db1_signals():
    try:
        conn = sqlite3.connect(DB1_PATH, timeout=30.0)
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT symbol, signal_type, signal_price, pattern_sequence, created_at
        FROM trading_signals
        ORDER BY created_at DESC LIMIT 20
        ''')
        
        signals = []
        for row in cursor.fetchall():
            signals.append({
                'symbol': row[0],
                'signal_type': row[1],
                'price': f"Rs{row[2]:.2f}" if row[2] else "N/A",
                'pattern': row[3],
                'created_at': row[4],
                'status': 'ACTIVE'
            })
        
        conn.close()
        
        return jsonify({
            'success': True,
            'signals': signals,
            'count': len(signals),
            'source': 'DB1_SIGNALS'
        })
        
    except Exception as e:
        logger.error(f"Error getting DB1 signals: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/active-positions')
def get_active_positions():
    try:
        conn = sqlite3.connect(DB2_PATH, timeout=30.0)
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT symbol, buy_price, sell_price, buy_quantity, buy_investment,
               profit_amount, status, buy_time, sell_time
        FROM trading_positions
        WHERE status = 'ACTIVE'
        ORDER BY buy_time DESC
        ''')
        
        positions = []
        for row in cursor.fetchall():
            positions.append({
                'symbol': row[0],
                'buy_price': f"Rs{row[1]:.2f}" if row[1] else "N/A",
                'sell_price': f"Rs{row[2]:.2f}" if row[2] else "N/A",
                'shares_quantity': row[3] or 0,
                'investment': f"Rs{row[4]:.0f}" if row[4] else "Rs100,000",
                'current_pnl': f"Rs{row[5]:.0f}" if row[5] else "Rs0",
                'pnl_percentage': "0.0%",
                'target_price': "Rs800+",
                'buy_time': row[7],
                'status': row[6]
            })
        
        conn.close()
        
        return jsonify({
            'success': True,
            'positions': positions,
            'count': len(positions),
            'source': 'DB2_POSITIONS'
        })
        
    except Exception as e:
        logger.error(f"Error getting active positions: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/symbols')
def get_symbols():
    try:
        conn = sqlite3.connect(DB1_PATH, timeout=30.0)
        cursor = conn.cursor()
        
        cursor.execute('SELECT DISTINCT symbol FROM trading_data ORDER BY symbol')
        symbols = [row[0] for row in cursor.fetchall()]
        
        conn.close()
        
        return jsonify({
            'success': True,
            'symbols': symbols,
            'count': len(symbols),
            'source': 'DB1_TRADING_DATA'
        })
        
    except Exception as e:
        logger.error(f"Error getting symbols: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/symbol-data/<symbol>')
def get_symbol_data(symbol):
    try:
        # Get DB1 data
        conn1 = sqlite3.connect(DB1_PATH, timeout=30.0)
        cursor1 = conn1.cursor()
        
        cursor1.execute('''
        SELECT timestamp, open_price, high_price, low_price, close_price,
               volume, fr_movement, previous_close
        FROM trading_data
        WHERE symbol = ?
        ORDER BY timestamp DESC LIMIT 25
        ''', (symbol,))
        
        db1_data = []
        for row in cursor1.fetchall():
            db1_data.append({
                'timestamp': row[0],
                'open': row[1],
                'high': row[2],
                'low': row[3],
                'close': row[4],
                'volume': row[5],
                'fr_movement': row[6],
                'previous_close': row[7]
            })
        
        conn1.close()
        
        # Get DB2 data
        conn2 = sqlite3.connect(DB2_PATH, timeout=30.0)
        cursor2 = conn2.cursor()
        
        cursor2.execute('''
        SELECT signal_type, signal_price, confirmation_status, received_at
        FROM db2_signals_received
        WHERE symbol = ?
        ORDER BY received_at DESC LIMIT 10
        ''', (symbol,))
        
        db2_signals = []
        for row in cursor2.fetchall():
            db2_signals.append({
                'signal_type': row[0],
                'signal_price': row[1],
                'confirmation_status': row[2],
                'received_at': row[3]
            })
        
        cursor2.execute('''
        SELECT buy_price, sell_price, buy_quantity, status, buy_time, sell_time
        FROM trading_positions
        WHERE symbol = ?
        ORDER BY buy_time DESC LIMIT 10
        ''', (symbol,))
        
        db2_positions = []
        for row in cursor2.fetchall():
            db2_positions.append({
                'buy_price': row[0],
                'sell_price': row[1],
                'quantity': row[2],
                'status': row[3],
                'buy_time': row[4],
                'sell_time': row[5]
            })
        
        conn2.close()
        
        return jsonify({
            'success': True,
            'symbol': symbol,
            'db1_data': db1_data,
            'db2_signals': db2_signals,
            'db2_positions': db2_positions,
            'db1_count': len(db1_data),
            'db2_signals_count': len(db2_signals),
            'db2_positions_count': len(db2_positions)
        })
        
    except Exception as e:
        logger.error(f"Error getting symbol data for {symbol}: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/database-schema')
def get_database_schema():
    try:
        schema = {}
        
        # Get DB1 schema
        conn1 = sqlite3.connect(DB1_PATH, timeout=30.0)
        cursor1 = conn1.cursor()
        
        cursor1.execute("SELECT name FROM sqlite_master WHERE type='table'")
        db1_tables = [row[0] for row in cursor1.fetchall()]
        
        schema['db1'] = {}
        for table in db1_tables:
            cursor1.execute(f"PRAGMA table_info({table})")
            columns = cursor1.fetchall()
            schema['db1'][table] = [{'name': col[1], 'type': col[2]} for col in columns]
        
        conn1.close()
        
        # Get DB2 schema
        conn2 = sqlite3.connect(DB2_PATH, timeout=30.0)
        cursor2 = conn2.cursor()
        
        cursor2.execute("SELECT name FROM sqlite_master WHERE type='table'")
        db2_tables = [row[0] for row in cursor2.fetchall()]
        
        schema['db2'] = {}
        for table in db2_tables:
            cursor2.execute(f"PRAGMA table_info({table})")
            columns = cursor2.fetchall()
            schema['db2'][table] = [{'name': col[1], 'type': col[2]} for col in columns]
        
        conn2.close()
        
        return jsonify({
            'success': True,
            'schema': schema
        })
        
    except Exception as e:
        logger.error(f"Error getting database schema: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/execute-sql', methods=['POST'])
def execute_sql():
    try:
        data = request.get_json()
        query = data.get('query', '').strip()
        database = data.get('database', 'db1')
        
        if not query:
            return jsonify({'success': False, 'error': 'No query provided'})
        
        if not query.upper().startswith('SELECT'):
            return jsonify({'success': False, 'error': 'Only SELECT statements are allowed'})
        
        db_path = DB1_PATH if database == 'db1' else DB2_PATH
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        cursor.execute(query)
        rows = cursor.fetchall()
        
        columns = [description[0] for description in cursor.description]
        
        conn.close()
        
        results = []
        for row in rows:
            results.append(dict(zip(columns, row)))
        
        return jsonify({
            'success': True,
            'results': results,
            'columns': columns,
            'row_count': len(results),
            'database': database
        })
        
    except Exception as e:
        logger.error(f"Error executing SQL: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/data-integrity')
def get_data_integrity():
    try:
        conn1 = sqlite3.connect(DB1_PATH, timeout=30.0)
        cursor1 = conn1.cursor()

        cursor1.execute('SELECT COUNT(*) FROM trading_data')
        total_records = cursor1.fetchone()[0]

        cursor1.execute('SELECT COUNT(DISTINCT symbol) FROM trading_data')
        total_symbols = cursor1.fetchone()[0]

        conn1.close()

        conn2 = sqlite3.connect(DB2_PATH, timeout=30.0)
        cursor2 = conn2.cursor()

        cursor2.execute('SELECT COUNT(*) FROM db2_signals_received')
        db2_signals = cursor2.fetchone()[0]

        cursor2.execute('SELECT COUNT(*) FROM trading_positions')
        db2_positions = cursor2.fetchone()[0]

        conn2.close()

        return jsonify({
            'success': True,
            'integrity_status': {
                'db1_total_records': total_records,
                'db1_total_symbols': total_symbols,
                'db2_signals_received': db2_signals,
                'db2_trading_positions': db2_positions
            }
        })

    except Exception as e:
        logger.error(f"Error getting data integrity: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/priority-queue/status')
def get_priority_queue_status():
    try:
        conn = sqlite3.connect(DB1_PATH, timeout=30.0)
        cursor = conn.cursor()

        cursor.execute('''
        SELECT symbol, pattern_sequence, COUNT(*) as signal_count
        FROM trading_signals
        GROUP BY symbol, pattern_sequence
        ORDER BY signal_count DESC
        ''')

        priority_queue = {
            'GOLD': [],
            'SILVER': [],
            'BRONZE': [],
            'REMAINING': []
        }

        for row in cursor.fetchall():
            symbol, pattern, count = row
            if pattern and 'FFFFR' in pattern:
                priority_queue['GOLD'].append(symbol)
            elif pattern and 'XFFFF' in pattern:
                priority_queue['SILVER'].append(symbol)
            elif pattern and ('XXFFF' in pattern or 'XXXFF' in pattern):
                priority_queue['BRONZE'].append(symbol)
            else:
                priority_queue['REMAINING'].append(symbol)

        conn.close()

        return jsonify({
            'success': True,
            'priority_queue': priority_queue,
            'timestamp': datetime.now().strftime('%I:%M:%S %p')
        })

    except Exception as e:
        logger.error(f"Error getting priority queue status: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/scan-duplicates', methods=['POST'])
def scan_duplicates():
    try:
        conn1 = sqlite3.connect(DB1_PATH, timeout=30.0)
        cursor1 = conn1.cursor()

        cursor1.execute('''
        SELECT symbol, timestamp, COUNT(*) as count
        FROM trading_data
        GROUP BY symbol, timestamp
        HAVING COUNT(*) > 1
        ORDER BY count DESC
        ''')

        db1_duplicates = []
        for row in cursor1.fetchall():
            db1_duplicates.append({
                'symbol': row[0],
                'timestamp': row[1],
                'count': row[2]
            })

        conn1.close()

        conn2 = sqlite3.connect(DB2_PATH, timeout=30.0)
        cursor2 = conn2.cursor()

        cursor2.execute('''
        SELECT symbol, received_at, COUNT(*) as count
        FROM db2_signals_received
        GROUP BY symbol, received_at
        HAVING COUNT(*) > 1
        ORDER BY count DESC
        ''')

        db2_duplicates = []
        for row in cursor2.fetchall():
            db2_duplicates.append({
                'symbol': row[0],
                'received_at': row[1],
                'count': row[2]
            })

        conn2.close()

        return jsonify({
            'success': True,
            'db1_duplicates': db1_duplicates,
            'db2_duplicates': db2_duplicates,
            'db1_count': len(db1_duplicates),
            'db2_count': len(db2_duplicates),
            'total_duplicates': len(db1_duplicates) + len(db2_duplicates)
        })

    except Exception as e:
        logger.error(f"Error scanning duplicates: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/db2/statistics')
def get_db2_statistics():
    try:
        conn = sqlite3.connect(DB2_PATH, timeout=30.0)
        cursor = conn.cursor()

        cursor.execute('SELECT COUNT(*) FROM db2_signals_received')
        total_signals = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM db2_signals_received WHERE confirmation_status = 'PENDING'")
        pending_signals = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM trading_positions WHERE status = 'ACTIVE'")
        active_positions = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM trading_positions WHERE status = 'COMPLETED'")
        completed_positions = cursor.fetchone()[0]

        cursor.execute("SELECT SUM(profit_amount) FROM trading_positions WHERE status = 'COMPLETED'")
        total_profit = cursor.fetchone()[0] or 0.0

        conn.close()

        return jsonify({
            'success': True,
            'statistics': {
                'total_signals_received': total_signals,
                'pending_confirmations': pending_signals,
                'active_positions': active_positions,
                'completed_positions': completed_positions,
                'total_profit': f"Rs{total_profit:.0f}"
            }
        })

    except Exception as e:
        logger.error(f"Error getting DB2 statistics: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/db2/trading-positions')
def get_db2_trading_positions():
    try:
        conn = sqlite3.connect(DB2_PATH, timeout=30.0)
        cursor = conn.cursor()

        cursor.execute('''
        SELECT symbol, buy_price, sell_price, buy_quantity, buy_investment,
               profit_amount, status, buy_time, sell_time
        FROM trading_positions
        ORDER BY buy_time DESC LIMIT 50
        ''')

        positions = []
        for row in cursor.fetchall():
            symbol, buy_price, sell_price, buy_qty, buy_investment, profit, status, buy_time, sell_time = row

            positions.append({
                'symbol': symbol,
                'buy_price': f"Rs{buy_price:.2f}" if buy_price else "N/A",
                'sell_price': f"Rs{sell_price:.2f}" if sell_price else "N/A",
                'shares_quantity': buy_qty,
                'investment': f"Rs{buy_investment:.0f}" if buy_investment else "N/A",
                'final_profit': f"Rs{profit:.0f}" if profit else "N/A",
                'status': status,
                'buy_timestamp': buy_time,
                'sell_timestamp': sell_time
            })

        conn.close()

        return jsonify({
            'success': True,
            'positions': positions,
            'count': len(positions)
        })

    except Exception as e:
        logger.error(f"Error getting trading positions: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/layer2-confirmations')
def get_layer2_confirmations():
    try:
        conn = sqlite3.connect(DB2_PATH, timeout=30.0)
        cursor = conn.cursor()

        cursor.execute('''
        SELECT symbol, signal_price, confirmation_status, received_at, db1_signal_id
        FROM db2_signals_received
        WHERE confirmation_status = 'PENDING'
        ORDER BY received_at DESC
        ''')

        confirmations = []
        for row in cursor.fetchall():
            confirmations.append({
                'symbol': row[0],
                'signal_price': f"Rs{row[1]:.2f}" if row[1] else "N/A",
                'status': row[2],
                'type': 'RR_PENDING',
                'timestamp': row[3],
                'db1_signal_id': row[4]
            })

        conn.close()

        return jsonify({
            'success': True,
            'confirmations': confirmations,
            'count': len(confirmations)
        })

    except Exception as e:
        logger.error(f"Error getting layer2 confirmations: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/start-trading', methods=['POST'])
def start_trading():
    try:
        logger.info("🚀 STARTING TRADING SYSTEM")
        return jsonify({'success': True, 'message': 'Trading system started successfully'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/stop-trading', methods=['POST'])
def stop_trading():
    try:
        logger.info("🛑 STOPPING TRADING SYSTEM")
        return jsonify({'success': True, 'message': 'Trading system stopped successfully'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/start-data-fetch', methods=['POST'])
def start_data_fetch():
    try:
        logger.info("📊 STARTING DATA FETCH")
        return jsonify({'success': True, 'message': 'Data fetching started successfully'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@socketio.on('connect')
def handle_connect():
    logger.info("Client connected to WebSocket")

@socketio.on('disconnect')
def handle_disconnect():
    logger.info("Client disconnected from WebSocket")

if __name__ == '__main__':
    logger.info("🚀 STARTING CLEAN FLASK APP - 6 TABS ONLY")
    logger.info("📊 DB1 Access: Data/trading_data.db")
    logger.info("💰 DB2 Access: Data/trading_operations.db")
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
