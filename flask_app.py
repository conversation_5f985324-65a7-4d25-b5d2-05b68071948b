"""
Flask Trading Application - FRESH REWRITE
Modern web-based trading system with DB1/DB2 direct database access
Reads directly from trading_data.db (DB1) and trading_operations.db (DB2)
NO API dependencies - Pure SQL database access
"""
import os
import logging
import sqlite3
from datetime import datetime, time as dt_time
from flask import Flask, render_template, request, jsonify
from flask_socketio import Socket<PERSON>
from flask_cors import CORS
import threading
import json

# Import new simplified trading components
try:
    from simple_integration import simple_integration
    from db1_engine import db1_engine
    from db2_engine import db2_engine
    TRADING_SYSTEM_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Trading system components not available: {e}")
    simple_integration = None
    db1_engine = None
    db2_engine = None
    TRADING_SYSTEM_AVAILABLE = False

try:
    from symbol_manager import SymbolManager
    symbol_manager = SymbolManager()
except ImportError:
    print("⚠️ SymbolManager not available")
    symbol_manager = None

try:
    from realtime_data_fetcher import RealtimeDataFetcher
    realtime_fetcher = RealtimeDataFetcher()
except ImportError:
    print("⚠️ RealtimeDataFetcher not available")
    realtime_fetcher = None

# Database paths - Direct access to DB1 and DB2
DB1_PATH = "Data/trading_data.db"        # DB1: 15-minute data, patterns, signals
DB2_PATH = "Data/trading_operations.db"  # DB2: 2-minute data, trades, positions

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'trading-system-secret-key'
socketio = SocketIO(app, cors_allowed_origins="*")
CORS(app)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global variables
trading_active = False
data_fetching_active = False

logger.info("🚀 FRESH FLASK APP INITIALIZED - DIRECT DB1/DB2 ACCESS")

# ============================================================================
# MAIN ROUTES
# ============================================================================

@app.route('/')
def dashboard():
    """Main trading dashboard"""
    return render_template('dashboard.html')

@app.route('/data-integrity-check')
def data_integrity_check_page():
    """Data Integrity Check page"""
    return render_template('data_integrity_check.html')

# ============================================================================
# LEGACY API COMPATIBILITY
# ============================================================================

@app.route('/api/sql-dashboard/summary')
def get_sql_dashboard_summary():
    """Legacy API endpoint for dashboard summary - redirects to new system"""
    try:
        # Get DB1 signals
        conn1 = sqlite3.connect(DB1_PATH, timeout=30.0)
        cursor1 = conn1.cursor()

        cursor1.execute('SELECT COUNT(*) FROM trading_signals')
        total_signals = cursor1.fetchone()[0]

        cursor1.execute('SELECT COUNT(*) FROM trading_data')
        total_data_points = cursor1.fetchone()[0]

        conn1.close()

        # Get DB2 statistics
        conn2 = sqlite3.connect(DB2_PATH, timeout=30.0)
        cursor2 = conn2.cursor()

        cursor2.execute('SELECT COUNT(*) FROM trading_positions WHERE status = "ACTIVE"')
        active_positions = cursor2.fetchone()[0]

        cursor2.execute('SELECT COUNT(*) FROM db2_signals_received WHERE confirmation_status = "PENDING"')
        pending_confirmations = cursor2.fetchone()[0]

        cursor2.execute('SELECT SUM(profit_amount) FROM trading_positions WHERE status = "COMPLETED"')
        total_profit = cursor2.fetchone()[0] or 0.0

        conn2.close()

        return jsonify({
            'success': True,
            'summary': {
                'db1_signals': total_signals,
                'db1_data_points': total_data_points,
                'db2_active_positions': active_positions,
                'db2_pending_confirmations': pending_confirmations,
                'total_profit': f"₹{total_profit:.0f}",
                'system_status': 'ACTIVE' if TRADING_SYSTEM_AVAILABLE else 'INACTIVE',
                'market_hours': simple_integration.is_market_hours() if TRADING_SYSTEM_AVAILABLE else False,
                'timestamp': datetime.now().isoformat()
            },
            'source': 'NEW_SYSTEM_COMPATIBILITY'
        })

    except Exception as e:
        logger.error(f"Error getting dashboard summary: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'summary': {
                'db1_signals': 0,
                'db1_data_points': 0,
                'db2_active_positions': 0,
                'db2_pending_confirmations': 0,
                'total_profit': '₹0',
                'system_status': 'ERROR',
                'market_hours': False,
                'timestamp': datetime.now().isoformat()
            }
        }), 500

@app.route('/api/portfolio-status')
def get_portfolio_status():
    """Legacy portfolio status endpoint"""
    try:
        conn = sqlite3.connect(DB2_PATH, timeout=30.0)
        cursor = conn.cursor()

        # Get active positions
        cursor.execute('SELECT COUNT(*) FROM trading_positions WHERE status = "ACTIVE"')
        active_positions = cursor.fetchone()[0]

        # Get total investment
        cursor.execute('SELECT SUM(buy_investment) FROM trading_positions WHERE status = "ACTIVE"')
        used_funds = cursor.fetchone()[0] or 0.0

        # Get total profit
        cursor.execute('SELECT SUM(profit_amount) FROM trading_positions WHERE status = "COMPLETED"')
        total_profit = cursor.fetchone()[0] or 0.0

        conn.close()

        # Calculate portfolio values
        total_pot = 22200000.0  # ₹2.22 Cr (222 symbols × ₹1L each)
        available_pot = total_pot - used_funds

        return jsonify({
            'success': True,
            'portfolio': {
                'total_pot': total_pot,
                'available_pot': available_pot,
                'used_funds': used_funds,
                'vault_amount': total_profit,
                'active_positions': active_positions,
                'total_profit': total_profit,
                'last_updated': datetime.now().isoformat()
            }
        })

    except Exception as e:
        logger.error(f"Error getting portfolio status: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/active-positions')
def get_active_positions():
    """Legacy active positions endpoint"""
    try:
        conn = sqlite3.connect(DB2_PATH, timeout=30.0)
        cursor = conn.cursor()

        cursor.execute('''
        SELECT symbol, buy_price, buy_quantity, buy_investment,
               buy_time, status
        FROM trading_positions
        WHERE status = 'ACTIVE'
        ORDER BY buy_time DESC
        ''')

        positions = []
        for row in cursor.fetchall():
            symbol, buy_price, buy_qty, buy_investment, buy_time, status = row

            positions.append({
                'symbol': symbol,
                'buy_price': f"₹{buy_price:.2f}" if buy_price else "N/A",
                'current_price': "N/A",  # Would need real-time data
                'shares_quantity': buy_qty,
                'investment': f"₹{buy_investment:.0f}" if buy_investment else "N/A",
                'current_value': "N/A",
                'target_price': f"₹{buy_price + 0.8:.2f}" if buy_price else "N/A",
                'current_pnl': "N/A",
                'pnl_percentage': "N/A",
                'buy_time': buy_time,
                'status': status
            })

        conn.close()

        return jsonify({
            'success': True,
            'positions': positions,
            'count': len(positions),
            'source': 'DB2_DIRECT_SQL',
            'timestamp': datetime.now().strftime('%H:%M:%S')
        })

    except Exception as e:
        logger.error(f"Error getting active positions: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/priority-queue/status')
def get_priority_queue_status():
    """Legacy priority queue status endpoint"""
    try:
        if not TRADING_SYSTEM_AVAILABLE or not db1_engine:
            return jsonify({
                'success': False,
                'error': 'Trading system not available',
                'priority_queue': {'GOLD': [], 'SILVER': [], 'BRONZE': [], 'REMAINING': []}
            })

        priority_status = db1_engine.get_priority_queue_status()

        return jsonify({
            'success': True,
            'priority_queue': priority_status,
            'source': 'DB1_ENGINE'
        })

    except Exception as e:
        logger.error(f"Error getting priority queue status: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'priority_queue': {'GOLD': [], 'SILVER': [], 'BRONZE': [], 'REMAINING': []}
        }), 500

# ============================================================================
# ADDITIONAL LEGACY API ENDPOINTS
# ============================================================================

@app.route('/api/db2-signals')
def get_db2_signals():
    """Legacy DB2 signals endpoint"""
    try:
        conn = sqlite3.connect(DB2_PATH, timeout=30.0)
        cursor = conn.cursor()

        cursor.execute('''
        SELECT symbol, signal_type, signal_price, confirmation_status,
               received_at, db1_signal_id
        FROM db2_signals_received
        ORDER BY received_at DESC LIMIT 20
        ''')

        signals = []
        for row in cursor.fetchall():
            signals.append({
                'symbol': row[0],
                'signal_type': row[1],
                'signal_price': f"₹{row[2]:.2f}",
                'confirmation_status': row[3],
                'received_at': row[4],
                'db1_signal_id': row[5]
            })

        conn.close()

        return jsonify({
            'success': True,
            'signals': signals,
            'count': len(signals),
            'source': 'DB2_DIRECT_SQL'
        })

    except Exception as e:
        logger.error(f"Error getting DB2 signals: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/layer2-confirmations')
def get_layer2_confirmations():
    """Legacy Layer 2 confirmations endpoint"""
    try:
        conn = sqlite3.connect(DB2_PATH, timeout=30.0)
        cursor = conn.cursor()

        cursor.execute('''
        SELECT symbol, signal_price, confirmation_status, received_at
        FROM db2_signals_received
        WHERE confirmation_status IN ('PENDING', 'RR_CONFIRMED', 'FF_CONFIRMED')
        ORDER BY received_at DESC
        ''')

        confirmations = []
        for row in cursor.fetchall():
            confirmations.append({
                'symbol': row[0],
                'signal_price': f"₹{row[1]:.2f}",
                'status': row[2],
                'timestamp': row[3],
                'type': 'RR_CONFIRMATION' if row[2] == 'PENDING' else row[2]
            })

        conn.close()

        return jsonify({
            'success': True,
            'confirmations': confirmations,
            'count': len(confirmations),
            'source': 'DB2_DIRECT_SQL'
        })

    except Exception as e:
        logger.error(f"Error getting layer2 confirmations: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/comprehensive-check', methods=['POST'])
def run_comprehensive_check():
    """Run comprehensive data integrity check"""
    try:
        if not TRADING_SYSTEM_AVAILABLE or not simple_integration:
            return jsonify({
                'success': False,
                'error': 'Trading system not available'
            })

        # Trigger complete system cycle
        result = simple_integration.trigger_complete_system_cycle()

        # Get data integrity status
        integrity_status = simple_integration.check_data_integrity()

        return jsonify({
            'success': True,
            'message': 'Comprehensive check completed',
            'system_cycle_result': result,
            'integrity_status': integrity_status,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error running comprehensive check: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/symbols')
def get_symbols():
    """Get all available symbols"""
    try:
        if symbol_manager:
            symbols = symbol_manager.get_all_symbols()
            return jsonify({
                'success': True,
                'symbols': symbols,
                'count': len(symbols)
            })
        else:
            # Fallback - get symbols from database
            conn = sqlite3.connect(DB1_PATH, timeout=30.0)
            cursor = conn.cursor()

            cursor.execute('SELECT DISTINCT symbol FROM trading_data ORDER BY symbol')
            symbols = [row[0] for row in cursor.fetchall()]

            conn.close()

            return jsonify({
                'success': True,
                'symbols': symbols,
                'count': len(symbols),
                'source': 'DATABASE_FALLBACK'
            })

    except Exception as e:
        logger.error(f"Error fetching symbols: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/trading-data/<symbol>')
def get_trading_data(symbol):
    """Get trading data for a specific symbol"""
    try:
        date = request.args.get('date', datetime.now().strftime('%Y-%m-%d'))

        conn = sqlite3.connect(DB1_PATH, timeout=30.0)
        cursor = conn.cursor()

        query = """
        SELECT timestamp, open_price, high_price, low_price, close_price, volume, fr_movement
        FROM trading_data
        WHERE symbol = ? AND DATE(timestamp) = ?
        ORDER BY timestamp DESC
        """

        cursor.execute(query, (symbol, date))
        rows = cursor.fetchall()
        conn.close()

        data = []
        for row in rows:
            data.append({
                'timestamp': row[0],
                'open': row[1],
                'high': row[2],
                'low': row[3],
                'close': row[4],
                'volume': row[5],
                'fr_movement': row[6]
            })

        return jsonify({
            'success': True,
            'symbol': symbol,
            'date': date,
            'data': data,
            'count': len(data)
        })

    except Exception as e:
        logger.error(f"Error fetching trading data for {symbol}: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/start-data-fetch', methods=['POST'])
def start_data_fetch():
    """Start real-time data fetching"""
    try:
        if realtime_fetcher:
            # This would start the data fetching process
            return jsonify({
                'success': True,
                'message': 'Data fetching started (simulated)',
                'status': 'ACTIVE'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'RealtimeDataFetcher not available'
            })

    except Exception as e:
        logger.error(f"Error starting data fetch: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/stop-data-fetch', methods=['POST'])
def stop_data_fetch():
    """Stop real-time data fetching"""
    try:
        return jsonify({
            'success': True,
            'message': 'Data fetching stopped (simulated)',
            'status': 'INACTIVE'
        })

    except Exception as e:
        logger.error(f"Error stopping data fetch: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# ============================================================================
# DB1 APIs - DIRECT DATABASE ACCESS
# ============================================================================

@app.route('/api/db1/signals')
def get_db1_signals():
    """Get DB1 signals for 'Active Patterns' display - DIRECT SQL"""
    try:
        conn = sqlite3.connect(DB1_PATH, timeout=30.0)
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT symbol, signal_type, signal_price, pattern_sequence, created_at
        FROM trading_signals
        ORDER BY created_at DESC LIMIT 20
        ''')
        
        signals = []
        for row in cursor.fetchall():
            signals.append({
                'symbol': row[0],
                'signal_type': row[1],
                'price': f"₹{row[2]:.2f}",
                'pattern': row[3],
                'created_at': row[4],
                'status': 'ACTIVE'
            })
        
        conn.close()
        
        return jsonify({
            'success': True,
            'signals': signals,
            'count': len(signals),
            'source': 'DB1_DIRECT_SQL'
        })
        
    except Exception as e:
        logger.error(f"Error getting DB1 signals: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/db1/fr-movements/<symbol>')
def get_db1_fr_movements(symbol):
    """Get F/R movements for a symbol - DIRECT SQL"""
    try:
        conn = sqlite3.connect(DB1_PATH, timeout=30.0)
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT timestamp, close_price, fr_movement, previous_close
        FROM trading_data 
        WHERE symbol = ? 
        ORDER BY timestamp DESC LIMIT 10
        ''', (symbol,))
        
        movements = []
        for row in cursor.fetchall():
            movements.append({
                'timestamp': row[0],
                'close_price': row[1],
                'fr_movement': row[2],
                'previous_close': row[3]
            })
        
        conn.close()
        
        return jsonify({
            'success': True,
            'symbol': symbol,
            'movements': movements,
            'count': len(movements)
        })
        
    except Exception as e:
        logger.error(f"Error getting F/R movements for {symbol}: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/db1/priority-queue')
def get_db1_priority_queue():
    """Get GOLD/SILVER/BRONZE priority queue - DIRECT SQL"""
    try:
        if not TRADING_SYSTEM_AVAILABLE or not db1_engine:
            return jsonify({
                'success': False,
                'error': 'Trading system not available',
                'priority_queue': {'GOLD': [], 'SILVER': [], 'BRONZE': [], 'REMAINING': []}
            })

        priority_status = db1_engine.get_priority_queue_status()

        return jsonify({
            'success': True,
            'priority_queue': priority_status,
            'source': 'DB1_ENGINE'
        })

    except Exception as e:
        logger.error(f"Error getting priority queue: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# ============================================================================
# DB2 APIs - DIRECT DATABASE ACCESS
# ============================================================================

@app.route('/api/db2/pending-confirmations')
def get_db2_pending_confirmations():
    """Get DB2 pending confirmations for 'Paper Trading Brain' display - DIRECT SQL"""
    try:
        conn = sqlite3.connect(DB2_PATH, timeout=30.0)
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT symbol, signal_price, confirmation_status, received_at, db1_signal_id
        FROM db2_signals_received
        WHERE confirmation_status = 'PENDING'
        ORDER BY received_at DESC
        ''')
        
        confirmations = []
        for row in cursor.fetchall():
            confirmations.append({
                'symbol': row[0],
                'signal_price': f"₹{row[1]:.2f}",
                'status': row[2],
                'received_time': row[3],
                'db1_signal_id': row[4],
                'confirmation_type': 'RR_PENDING'
            })
        
        conn.close()
        
        return jsonify({
            'success': True,
            'pending_confirmations': confirmations,
            'count': len(confirmations),
            'source': 'DB2_DIRECT_SQL'
        })
        
    except Exception as e:
        logger.error(f"Error getting DB2 pending confirmations: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/db2/trading-positions')
def get_db2_trading_positions():
    """Get trading positions for 'Paper Trading Records' display - DIRECT SQL"""
    try:
        conn = sqlite3.connect(DB2_PATH, timeout=30.0)
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT symbol, buy_price, sell_price, buy_quantity, buy_investment,
               profit_amount, profit_amount, status, buy_time, sell_time
        FROM trading_positions
        ORDER BY buy_time DESC LIMIT 50
        ''')
        
        positions = []
        for row in cursor.fetchall():
            symbol, buy_price, sell_price, buy_qty, buy_investment, current_profit, final_profit, status, buy_time, sell_time = row

            positions.append({
                'symbol': symbol,
                'buy_price': f"₹{buy_price:.2f}" if buy_price else "N/A",
                'sell_price': f"₹{sell_price:.2f}" if sell_price else "N/A",
                'shares_quantity': buy_qty,
                'investment': f"₹{buy_investment:.0f}" if buy_investment else "N/A",
                'current_profit': f"₹{current_profit:.0f}" if current_profit else "N/A",
                'final_profit': f"₹{final_profit:.0f}" if final_profit else "N/A",
                'status': status,
                'buy_timestamp': buy_time,
                'sell_timestamp': sell_time
            })
        
        conn.close()
        
        return jsonify({
            'success': True,
            'positions': positions,
            'count': len(positions),
            'source': 'DB2_DIRECT_SQL'
        })
        
    except Exception as e:
        logger.error(f"Error getting trading positions: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/db2/statistics')
def get_db2_statistics():
    """Get DB2 statistics - DIRECT SQL"""
    try:
        conn = sqlite3.connect(DB2_PATH, timeout=30.0)
        cursor = conn.cursor()
        
        # Get various statistics
        cursor.execute('SELECT COUNT(*) FROM db2_signals_received')
        total_signals = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM db2_signals_received WHERE confirmation_status = 'PENDING'")
        pending_signals = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM trading_positions WHERE status = 'ACTIVE'")
        active_positions = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM trading_positions WHERE status = 'COMPLETED'")
        completed_positions = cursor.fetchone()[0]

        cursor.execute("SELECT SUM(profit_amount) FROM trading_positions WHERE status = 'COMPLETED'")
        total_profit = cursor.fetchone()[0] or 0.0
        
        conn.close()
        
        return jsonify({
            'success': True,
            'statistics': {
                'total_signals_received': total_signals,
                'pending_confirmations': pending_signals,
                'active_positions': active_positions,
                'completed_positions': completed_positions,
                'total_profit': f"₹{total_profit:.0f}"
            },
            'source': 'DB2_DIRECT_SQL'
        })
        
    except Exception as e:
        logger.error(f"Error getting DB2 statistics: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# ============================================================================
# SYSTEM CONTROL APIs
# ============================================================================

@app.route('/api/start-trading', methods=['POST'])
def start_trading():
    """Start the complete trading system"""
    global trading_active
    try:
        if not trading_active:
            trading_active = True
            
            logger.info("🚀 STARTING COMPLETE TRADING SYSTEM")
            logger.info("📊 DB1: Pattern detection + Signal generation")
            logger.info("💰 DB2: Trade execution + Profit monitoring")
            
            # Emit status update
            socketio.emit('trading_status', {
                'active': True,
                'message': 'Complete trading system started: DB1 → DB2 flow active'
            })
            
            return jsonify({
                'success': True, 
                'message': 'Complete trading system started successfully'
            })
        else:
            return jsonify({
                'success': False, 
                'message': 'Trading already active'
            })
            
    except Exception as e:
        logger.error(f"Error starting trading: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/stop-trading', methods=['POST'])
def stop_trading():
    """Stop the complete trading system"""
    global trading_active
    try:
        if trading_active:
            trading_active = False
            
            logger.info("🛑 STOPPING COMPLETE TRADING SYSTEM")
            
            # Emit status update
            socketio.emit('trading_status', {
                'active': False,
                'message': 'Complete trading system stopped'
            })
            
            return jsonify({
                'success': True, 
                'message': 'Complete trading system stopped'
            })
        else:
            return jsonify({
                'success': False, 
                'message': 'Trading not active'
            })
            
    except Exception as e:
        logger.error(f"Error stopping trading: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/trigger-system-cycle', methods=['POST'])
def trigger_system_cycle():
    """Manually trigger complete system cycle"""
    try:
        if not TRADING_SYSTEM_AVAILABLE or not simple_integration:
            return jsonify({
                'success': False,
                'error': 'Trading system not available'
            })

        result = simple_integration.trigger_complete_system_cycle()

        return jsonify({
            'success': True,
            'message': 'System cycle triggered successfully',
            'result': result
        })

    except Exception as e:
        logger.error(f"Error triggering system cycle: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# ============================================================================
# DATA INTEGRITY APIs
# ============================================================================

@app.route('/api/data-integrity')
def get_data_integrity():
    """Get data integrity status - DIRECT SQL"""
    try:
        if not TRADING_SYSTEM_AVAILABLE or not simple_integration:
            return jsonify({
                'success': False,
                'error': 'Trading system not available',
                'integrity_status': {}
            })

        integrity_status = simple_integration.check_data_integrity()

        return jsonify({
            'success': True,
            'integrity_status': integrity_status,
            'source': 'SIMPLE_INTEGRATION'
        })

    except Exception as e:
        logger.error(f"Error getting data integrity: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# ============================================================================
# ADDITIONAL FRONTEND SUPPORT APIs
# ============================================================================

@app.route('/api/symbol-data/<symbol>')
def get_symbol_data(symbol):
    """Get comprehensive symbol data for Symbol Explorer"""
    try:
        # Get DB1 data (last 25 data points)
        conn1 = sqlite3.connect(DB1_PATH, timeout=30.0)
        cursor1 = conn1.cursor()

        cursor1.execute('''
        SELECT timestamp, open_price, high_price, low_price, close_price,
               volume, fr_movement, previous_close
        FROM trading_data
        WHERE symbol = ?
        ORDER BY timestamp DESC LIMIT 25
        ''', (symbol,))

        db1_data = []
        for row in cursor1.fetchall():
            db1_data.append({
                'timestamp': row[0],
                'open': row[1],
                'high': row[2],
                'low': row[3],
                'close': row[4],
                'volume': row[5],
                'fr_movement': row[6],
                'previous_close': row[7]
            })

        conn1.close()

        # Get DB2 data
        conn2 = sqlite3.connect(DB2_PATH, timeout=30.0)
        cursor2 = conn2.cursor()

        # DB2 signals for this symbol
        cursor2.execute('''
        SELECT signal_type, signal_price, confirmation_status, received_at
        FROM db2_signals_received
        WHERE symbol = ?
        ORDER BY received_at DESC LIMIT 10
        ''', (symbol,))

        db2_signals = []
        for row in cursor2.fetchall():
            db2_signals.append({
                'signal_type': row[0],
                'signal_price': row[1],
                'confirmation_status': row[2],
                'received_at': row[3]
            })

        # DB2 positions for this symbol
        cursor2.execute('''
        SELECT buy_price, sell_price, buy_quantity, status, buy_time, sell_time
        FROM trading_positions
        WHERE symbol = ?
        ORDER BY buy_time DESC LIMIT 10
        ''', (symbol,))

        db2_positions = []
        for row in cursor2.fetchall():
            db2_positions.append({
                'buy_price': row[0],
                'sell_price': row[1],
                'quantity': row[2],
                'status': row[3],
                'buy_time': row[4],
                'sell_time': row[5]
            })

        conn2.close()

        return jsonify({
            'success': True,
            'symbol': symbol,
            'db1_data': db1_data,
            'db2_signals': db2_signals,
            'db2_positions': db2_positions,
            'db1_count': len(db1_data),
            'db2_signals_count': len(db2_signals),
            'db2_positions_count': len(db2_positions)
        })

    except Exception as e:
        logger.error(f"Error getting symbol data for {symbol}: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/execute-sql', methods=['POST'])
def execute_sql():
    """Execute SQL query for SQL Query tab"""
    try:
        data = request.get_json()
        query = data.get('query', '').strip()
        database = data.get('database', 'db1')  # db1 or db2

        if not query:
            return jsonify({'success': False, 'error': 'No query provided'})

        # Security check - only allow SELECT statements
        if not query.upper().startswith('SELECT'):
            return jsonify({'success': False, 'error': 'Only SELECT statements are allowed'})

        # Choose database
        db_path = DB1_PATH if database == 'db1' else DB2_PATH

        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()

        cursor.execute(query)
        rows = cursor.fetchall()

        # Get column names
        columns = [description[0] for description in cursor.description]

        conn.close()

        # Format results
        results = []
        for row in rows:
            results.append(dict(zip(columns, row)))

        return jsonify({
            'success': True,
            'results': results,
            'columns': columns,
            'row_count': len(results),
            'database': database
        })

    except Exception as e:
        logger.error(f"Error executing SQL: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/database-schema')
def get_database_schema():
    """Get database schema for SQL Query tab"""
    try:
        schema = {}

        # Get DB1 schema
        conn1 = sqlite3.connect(DB1_PATH, timeout=30.0)
        cursor1 = conn1.cursor()

        cursor1.execute("SELECT name FROM sqlite_master WHERE type='table'")
        db1_tables = [row[0] for row in cursor1.fetchall()]

        schema['db1'] = {}
        for table in db1_tables:
            cursor1.execute(f"PRAGMA table_info({table})")
            columns = cursor1.fetchall()
            schema['db1'][table] = [{'name': col[1], 'type': col[2]} for col in columns]

        conn1.close()

        # Get DB2 schema
        conn2 = sqlite3.connect(DB2_PATH, timeout=30.0)
        cursor2 = conn2.cursor()

        cursor2.execute("SELECT name FROM sqlite_master WHERE type='table'")
        db2_tables = [row[0] for row in cursor2.fetchall()]

        schema['db2'] = {}
        for table in db2_tables:
            cursor2.execute(f"PRAGMA table_info({table})")
            columns = cursor2.fetchall()
            schema['db2'][table] = [{'name': col[1], 'type': col[2]} for col in columns]

        conn2.close()

        return jsonify({
            'success': True,
            'schema': schema
        })

    except Exception as e:
        logger.error(f"Error getting database schema: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/scan-duplicates', methods=['POST'])
def scan_duplicates():
    """Scan for duplicate records"""
    try:
        # Scan DB1 duplicates
        conn1 = sqlite3.connect(DB1_PATH, timeout=30.0)
        cursor1 = conn1.cursor()

        cursor1.execute('''
        SELECT symbol, timestamp, COUNT(*) as count
        FROM trading_data
        GROUP BY symbol, timestamp
        HAVING COUNT(*) > 1
        ORDER BY count DESC
        ''')

        db1_duplicates = []
        for row in cursor1.fetchall():
            db1_duplicates.append({
                'symbol': row[0],
                'timestamp': row[1],
                'count': row[2]
            })

        conn1.close()

        # Scan DB2 duplicates
        conn2 = sqlite3.connect(DB2_PATH, timeout=30.0)
        cursor2 = conn2.cursor()

        cursor2.execute('''
        SELECT symbol, received_at, COUNT(*) as count
        FROM db2_signals_received
        GROUP BY symbol, received_at
        HAVING COUNT(*) > 1
        ORDER BY count DESC
        ''')

        db2_duplicates = []
        for row in cursor2.fetchall():
            db2_duplicates.append({
                'symbol': row[0],
                'received_at': row[1],
                'count': row[2]
            })

        conn2.close()

        return jsonify({
            'success': True,
            'db1_duplicates': db1_duplicates,
            'db2_duplicates': db2_duplicates,
            'db1_count': len(db1_duplicates),
            'db2_count': len(db2_duplicates),
            'total_duplicates': len(db1_duplicates) + len(db2_duplicates)
        })

    except Exception as e:
        logger.error(f"Error scanning duplicates: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# ============================================================================
# WEBSOCKET EVENTS
# ============================================================================

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    logger.info("Client connected to WebSocket")

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    logger.info("Client disconnected from WebSocket")

# ============================================================================
# MAIN EXECUTION
# ============================================================================

if __name__ == '__main__':
    logger.info("🚀 STARTING FRESH FLASK APP")
    logger.info("📊 DB1 Direct Access: trading_data.db")
    logger.info("💰 DB2 Direct Access: trading_operations.db")
    logger.info("🔗 Integration: simple_integration.py")
    
    # Run the Flask app
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
