#!/usr/bin/env python3
"""
Missing Data Fetcher - Manual Data Integrity Check
Specifically for comprehensive check - fetches missing 25 intervals
Works independently of market hours and simple_integration.py
"""

import datetime
import sqlite3
import logging
import time
from typing import List, Dict, Tuple
import pyotp
from SmartApi import SmartConnect
from config import Config, DATA_FETCH_CONFIG

class MissingDataFetcher:
    def __init__(self, config_file: str = "angelone_smartapi_config.json"):
        self.config = Config(config_file)
        self.smart_api = None
        self.logger = self._setup_logging()
        
        # API rate limiting - conservative for missing data fetch
        self.REQUEST_DELAY_SECONDS = 2.0
        self.MAX_RETRIES = 3
        self.RETRY_DELAYS = [5.0, 10.0, 15.0]
        
        # Database path
        self.DB_PATH = 'Data/trading_data.db'
        
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)
    
    def connect_to_api(self) -> bool:
        """Connect to Angel One SmartAPI"""
        try:
            credentials = self.config.api_credentials
            
            # Generate TOTP
            totp = pyotp.TOTP(credentials["totp_secret"])
            totp_code = totp.now()
            
            # Initialize SmartConnect
            self.smart_api = SmartConnect(api_key=credentials["api_key"])
            
            # Login
            data = self.smart_api.generateSession(
                clientCode=credentials["username"],
                password=credentials["password"],
                totp=totp_code
            )
            
            if data and data.get('status'):
                self.logger.info("✅ Connected to Angel One API for missing data fetch")
                return True
            else:
                self.logger.error(f"❌ API connection failed: {data}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error connecting to API: {e}")
            return False
    
    def load_symbols_from_token_file(self) -> List[Tuple[str, str]]:
        """Load symbols and tokens from angelone_tokens.txt"""
        try:
            symbols_with_tokens = []
            with open('Data/angelone_tokens.txt', 'r') as f:
                for line in f:
                    line = line.strip()
                    if ':' in line:
                        symbol, token = line.split(':', 1)
                        symbols_with_tokens.append((symbol, token))
            
            self.logger.info(f"📊 Loaded {len(symbols_with_tokens)} symbols from token file")
            return symbols_with_tokens
            
        except Exception as e:
            self.logger.error(f"❌ Error loading symbols: {e}")
            return []
    
    def get_last_25_trading_intervals(self) -> List[datetime.datetime]:
        """Get the last 25 trading intervals (15-minute intervals from 9:15 to 15:15)"""
        intervals = []
        current_date = datetime.date.today()
        
        # Go back up to 5 trading days to find last 25 intervals
        for days_back in range(10):
            check_date = current_date - datetime.timedelta(days=days_back)
            
            # Skip weekends (Saturday=5, Sunday=6)
            if check_date.weekday() >= 5:
                continue
            
            # Generate intervals for this day (9:15 to 15:15 in 15-minute steps)
            day_intervals = []
            current_time = datetime.datetime.combine(check_date, datetime.time(9, 15))
            end_time = datetime.datetime.combine(check_date, datetime.time(15, 15))
            
            while current_time <= end_time:
                day_intervals.append(current_time)
                current_time += datetime.timedelta(minutes=15)
            
            # For today, only include intervals that have already passed
            if check_date == datetime.date.today():
                now = datetime.datetime.now()
                day_intervals = [interval for interval in day_intervals if interval <= now]
            
            # Add intervals in reverse order (most recent first)
            intervals.extend(reversed(day_intervals))
            
            # Stop when we have enough intervals
            if len(intervals) >= 25:
                break
        
        # Return the last 25 intervals (most recent 25)
        result = intervals[-25:] if len(intervals) >= 25 else intervals
        
        self.logger.info(f"📊 Generated {len(result)} intervals for missing data check")
        if result:
            self.logger.info(f"📊 From: {result[0].strftime('%Y-%m-%d %H:%M')} To: {result[-1].strftime('%Y-%m-%d %H:%M')}")
        
        return result
    
    def check_missing_data_for_symbol(self, symbol: str, intervals: List[datetime.datetime]) -> List[datetime.datetime]:
        """Check which intervals are missing for a symbol"""
        try:
            conn = sqlite3.connect(self.DB_PATH, timeout=30.0)
            cursor = conn.cursor()
            
            missing_intervals = []
            
            for interval in intervals:
                cursor.execute('''
                SELECT COUNT(*) FROM trading_data
                WHERE symbol = ? AND timestamp = ?
                ''', (symbol, interval.strftime('%Y-%m-%d %H:%M:%S')))
                
                count = cursor.fetchone()[0]
                if count == 0:
                    missing_intervals.append(interval)
            
            conn.close()
            
            if missing_intervals:
                self.logger.info(f"📊 {symbol}: Missing {len(missing_intervals)}/{len(intervals)} intervals")
            
            return missing_intervals
            
        except Exception as e:
            self.logger.error(f"❌ Error checking missing data for {symbol}: {e}")
            return intervals  # Assume all are missing if error
    
    def fetch_candle_data(self, symbol: str, token: str, interval_time: datetime.datetime) -> Dict:
        """Fetch candle data for a specific interval"""
        for attempt in range(self.MAX_RETRIES):
            try:
                if not self.smart_api:
                    if not self.connect_to_api():
                        return None
                
                # Create time range for the specific interval
                from_time = interval_time
                to_time = interval_time + datetime.timedelta(minutes=15)
                
                from_date = from_time.strftime("%Y-%m-%d %H:%M")
                to_date = to_time.strftime("%Y-%m-%d %H:%M")
                
                # Fetch candle data
                params = {
                    "exchange": DATA_FETCH_CONFIG["exchange"],
                    "symboltoken": token,
                    "interval": DATA_FETCH_CONFIG["interval"],
                    "fromdate": from_date,
                    "todate": to_date
                }
                
                response = self.smart_api.getCandleData(params)
                
                if response and response.get('status') and response.get('data'):
                    candles = response['data']
                    
                    # Find the candle that matches our interval
                    for candle in candles:
                        candle_time_str = candle[0].replace('Z', '+00:00')
                        candle_time = datetime.datetime.fromisoformat(candle_time_str)
                        candle_time = candle_time.replace(tzinfo=None)
                        
                        # Check if this candle matches our interval (within 1 minute tolerance)
                        time_diff = abs((candle_time - interval_time).total_seconds())
                        if time_diff <= 60:  # Within 1 minute
                            return {
                                "symbol": symbol,
                                "token": token,
                                "exchange": DATA_FETCH_CONFIG["exchange"],
                                "timestamp": candle_time,
                                "open_price": float(candle[1]),
                                "high_price": float(candle[2]),
                                "low_price": float(candle[3]),
                                "close_price": float(candle[4]),
                                "volume": int(candle[5]),
                                "interval_type": DATA_FETCH_CONFIG["interval"]
                            }
                
                return None
                
            except Exception as e:
                if attempt < self.MAX_RETRIES - 1:
                    self.logger.warning(f"⚠️ Attempt {attempt + 1} failed for {symbol}: {e}")
                    time.sleep(self.RETRY_DELAYS[attempt])
                    continue
                else:
                    self.logger.error(f"❌ All attempts failed for {symbol}: {e}")
                    return None
        
        return None
    
    def store_candle_data(self, candle_data_list: List[Dict]) -> bool:
        """Store candle data in database"""
        try:
            if not candle_data_list:
                return True
            
            conn = sqlite3.connect(self.DB_PATH, timeout=30.0)
            cursor = conn.cursor()
            
            # Create table if not exists
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS trading_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                token TEXT,
                exchange TEXT,
                timestamp DATETIME NOT NULL,
                open_price REAL,
                high_price REAL,
                low_price REAL,
                close_price REAL,
                volume INTEGER,
                interval_type TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, timestamp)
            )
            ''')
            
            # Insert data
            for candle in candle_data_list:
                cursor.execute('''
                INSERT OR REPLACE INTO trading_data 
                (symbol, token, exchange, timestamp, open_price, high_price, 
                 low_price, close_price, volume, interval_type)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    candle['symbol'],
                    candle['token'],
                    candle['exchange'],
                    candle['timestamp'].strftime('%Y-%m-%d %H:%M:%S'),
                    candle['open_price'],
                    candle['high_price'],
                    candle['low_price'],
                    candle['close_price'],
                    candle['volume'],
                    candle['interval_type']
                ))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"✅ Stored {len(candle_data_list)} candle records in database")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error storing candle data: {e}")
            return False

    def fetch_missing_data_for_all_symbols(self) -> Dict:
        """Main method to fetch missing data for all symbols"""
        try:
            self.logger.info("🚀 STARTING MISSING DATA FETCH FOR ALL SYMBOLS")

            # Step 1: Load symbols
            symbols_with_tokens = self.load_symbols_from_token_file()
            if not symbols_with_tokens:
                return {'success': 0, 'failed': 0, 'total': 0, 'error': 'No symbols loaded'}

            # Step 2: Get last 25 trading intervals
            intervals = self.get_last_25_trading_intervals()
            if not intervals:
                return {'success': 0, 'failed': 0, 'total': 0, 'error': 'No intervals generated'}

            # Step 3: Connect to API
            if not self.connect_to_api():
                return {'success': 0, 'failed': 0, 'total': 0, 'error': 'API connection failed'}

            # Step 4: Process each symbol
            total_symbols = len(symbols_with_tokens)
            total_fetched = 0
            total_failed = 0
            all_candle_data = []

            self.logger.info(f"📊 Processing {total_symbols} symbols for {len(intervals)} intervals")

            for i, (symbol, token) in enumerate(symbols_with_tokens):
                try:
                    self.logger.info(f"📊 Processing {symbol} ({i+1}/{total_symbols})")

                    # Check which intervals are missing for this symbol
                    missing_intervals = self.check_missing_data_for_symbol(symbol, intervals)

                    if not missing_intervals:
                        self.logger.info(f"✅ {symbol}: All data complete")
                        total_fetched += 1
                        continue

                    # Fetch missing data
                    symbol_success = 0
                    for interval in missing_intervals:
                        candle_data = self.fetch_candle_data(symbol, token, interval)

                        if candle_data:
                            all_candle_data.append(candle_data)
                            symbol_success += 1
                            self.logger.info(f"✅ {symbol} {interval.strftime('%H:%M')}: ₹{candle_data['close_price']:.2f}")
                        else:
                            self.logger.warning(f"❌ {symbol} {interval.strftime('%H:%M')}: Failed to fetch")

                        # Rate limiting
                        time.sleep(self.REQUEST_DELAY_SECONDS)

                    if symbol_success > 0:
                        total_fetched += 1
                        self.logger.info(f"✅ {symbol}: Fetched {symbol_success}/{len(missing_intervals)} intervals")
                    else:
                        total_failed += 1
                        self.logger.error(f"❌ {symbol}: Failed to fetch any data")

                    # Store data in batches of 50 records
                    if len(all_candle_data) >= 50:
                        self.store_candle_data(all_candle_data)
                        all_candle_data = []

                    # Progress update every 10 symbols
                    if (i + 1) % 10 == 0:
                        self.logger.info(f"📊 Progress: {i+1}/{total_symbols} symbols processed")

                except Exception as e:
                    self.logger.error(f"❌ Error processing {symbol}: {e}")
                    total_failed += 1
                    continue

            # Store remaining data
            if all_candle_data:
                self.store_candle_data(all_candle_data)

            self.logger.info(f"🎉 MISSING DATA FETCH COMPLETED")
            self.logger.info(f"📊 Results: {total_fetched} success, {total_failed} failed out of {total_symbols} symbols")

            return {
                'success': total_fetched,
                'failed': total_failed,
                'total': total_symbols,
                'intervals_processed': len(intervals),
                'records_added': len(all_candle_data)
            }

        except Exception as e:
            self.logger.error(f"❌ Error in fetch_missing_data_for_all_symbols: {e}")
            return {'success': 0, 'failed': 0, 'total': 0, 'error': str(e)}

# Main execution for testing
if __name__ == "__main__":
    fetcher = MissingDataFetcher()
    result = fetcher.fetch_missing_data_for_all_symbols()
    print(f"📊 Final Result: {result}")
