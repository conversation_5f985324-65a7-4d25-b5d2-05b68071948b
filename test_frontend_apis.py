#!/usr/bin/env python3
"""
Test the frontend API endpoints
"""

import requests
import time

def test_apis():
    print("📊 Testing Frontend API Endpoints...")
    
    try:
        # Test 1: Symbols endpoint
        print("\n1. Testing /api/symbols...")
        response = requests.get('http://localhost:5000/api/symbols', timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            success = data.get('success', False)
            count = data.get('count', 0)
            symbols = data.get('symbols', [])
            print(f"   ✅ Success: {success}")
            print(f"   ✅ Count: {count}")
            print(f"   ✅ Symbols: {symbols}")
        else:
            print(f"   ❌ Error: {response.text[:100]}")
        
        # Test 2: DB1 signals endpoint
        print("\n2. Testing /api/db1/signals...")
        response = requests.get('http://localhost:5000/api/db1/signals', timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            success = data.get('success', False)
            count = data.get('count', 0)
            print(f"   ✅ Success: {success}")
            print(f"   ✅ Count: {count}")
        else:
            print(f"   ❌ Error: {response.text[:100]}")
        
        # Test 3: Data integrity endpoint
        print("\n3. Testing /api/data-integrity...")
        response = requests.get('http://localhost:5000/api/data-integrity', timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            success = data.get('success', False)
            integrity = data.get('integrity_status', {})
            print(f"   ✅ Success: {success}")
            print(f"   ✅ DB1 Records: {integrity.get('db1_total_records', 0)}")
            print(f"   ✅ DB1 Symbols: {integrity.get('db1_total_symbols', 0)}")
        else:
            print(f"   ❌ Error: {response.text[:100]}")
        
        print("\n🎯 SUMMARY:")
        print("✅ Testing complete - check if frontend loads correctly now")
        
    except Exception as e:
        print(f"❌ Test error: {e}")

if __name__ == "__main__":
    test_apis()
