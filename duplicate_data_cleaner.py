#!/usr/bin/env python3
"""
Duplicate Data Cleaner and Retry Fetcher
Detects duplicate/FLAT data and retries fetching with fresh API calls
Ensures real-time data integrity
"""

import sqlite3
import logging
import time
from typing import List, Dict, Tuple
import pyotp
from SmartApi import SmartConnect
from config import Config, DATA_FETCH_CONFIG
from datetime import datetime, timedelta

class DuplicateDataCleaner:
    def __init__(self, config_file: str = "angelone_smartapi_config.json"):
        self.config = Config(config_file)
        self.smart_api = None
        self.logger = self._setup_logging()
        
        # Database path
        self.DB_PATH = 'Data/trading_data.db'
        
        # API settings for retry
        self.REQUEST_DELAY_SECONDS = 3.0  # Longer delay for retry
        self.MAX_RETRIES = 3
        self.RETRY_DELAYS = [10.0, 20.0, 30.0]  # Longer delays for fresh data
        
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)
    
    def connect_to_api(self) -> bool:
        """Connect to Angel One SmartAPI with fresh session"""
        try:
            credentials = self.config.api_credentials
            
            # Generate fresh TOTP
            totp = pyotp.TOTP(credentials["totp_secret"])
            totp_code = totp.now()
            
            # Initialize fresh SmartConnect
            self.smart_api = SmartConnect(api_key=credentials["api_key"])
            
            # Login with fresh session
            data = self.smart_api.generateSession(
                clientCode=credentials["username"],
                password=credentials["password"],
                totp=totp_code
            )
            
            if data and data.get('status'):
                self.logger.info("✅ Connected to Angel One API with FRESH session")
                return True
            else:
                self.logger.error(f"❌ API connection failed: {data}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error connecting to API: {e}")
            return False
    
    def detect_duplicate_data(self) -> List[Dict]:
        """Detect duplicate/FLAT data in database"""
        try:
            conn = sqlite3.connect(self.DB_PATH, timeout=30.0)
            cursor = conn.cursor()
            
            # Find records with FLAT movement (duplicate data indicator)
            cursor.execute('''
            SELECT symbol, timestamp, close_price, fr_movement, id
            FROM trading_data 
            WHERE fr_movement = 'FLAT'
            ORDER BY symbol, timestamp
            ''')
            
            flat_records = cursor.fetchall()
            
            # Find records with identical consecutive prices (another duplicate indicator)
            cursor.execute('''
            SELECT t1.symbol, t1.timestamp, t1.close_price, t1.fr_movement, t1.id,
                   t2.timestamp as prev_timestamp, t2.close_price as prev_close
            FROM trading_data t1
            JOIN trading_data t2 ON t1.symbol = t2.symbol 
            WHERE t1.close_price = t2.close_price 
            AND t1.timestamp > t2.timestamp
            AND t1.id != t2.id
            ORDER BY t1.symbol, t1.timestamp
            ''')
            
            identical_records = cursor.fetchall()
            
            conn.close()
            
            # Combine and deduplicate
            duplicate_data = []
            
            # Add FLAT records
            for record in flat_records:
                duplicate_data.append({
                    'symbol': record[0],
                    'timestamp': record[1],
                    'close_price': record[2],
                    'fr_movement': record[3],
                    'id': record[4],
                    'issue': 'FLAT_MOVEMENT'
                })
            
            # Add identical price records
            for record in identical_records:
                duplicate_data.append({
                    'symbol': record[0],
                    'timestamp': record[1],
                    'close_price': record[2],
                    'fr_movement': record[3],
                    'id': record[4],
                    'issue': 'IDENTICAL_PRICE',
                    'prev_timestamp': record[5],
                    'prev_close': record[6]
                })
            
            self.logger.info(f"📊 Found {len(duplicate_data)} duplicate/problematic records")
            return duplicate_data
            
        except Exception as e:
            self.logger.error(f"❌ Error detecting duplicate data: {e}")
            return []
    
    def delete_duplicate_record(self, record_id: int) -> bool:
        """Delete a duplicate record from database"""
        try:
            conn = sqlite3.connect(self.DB_PATH, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('DELETE FROM trading_data WHERE id = ?', (record_id,))
            conn.commit()
            conn.close()
            
            self.logger.info(f"🗑️ Deleted duplicate record ID: {record_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error deleting record {record_id}: {e}")
            return False
    
    def fetch_fresh_data(self, symbol: str, token: str, timestamp: str) -> Dict:
        """Fetch fresh data for a specific symbol and timestamp with retry logic"""
        
        # Parse timestamp
        target_time = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')
        
        for attempt in range(self.MAX_RETRIES):
            try:
                self.logger.info(f"🔄 Attempt {attempt + 1}: Fetching FRESH data for {symbol} at {target_time.strftime('%H:%M')}")
                
                # Force fresh API connection for each retry
                if not self.connect_to_api():
                    self.logger.error(f"❌ API connection failed on attempt {attempt + 1}")
                    if attempt < self.MAX_RETRIES - 1:
                        time.sleep(self.RETRY_DELAYS[attempt])
                        continue
                    return None
                
                # Create time range for the specific interval
                from_time = target_time
                to_time = target_time + timedelta(minutes=15)
                
                from_date = from_time.strftime("%Y-%m-%d %H:%M")
                to_date = to_time.strftime("%Y-%m-%d %H:%M")
                
                # Add random delay to avoid cached data
                time.sleep(2 + attempt)  # Increasing delay per attempt
                
                # Fetch candle data with fresh session
                params = {
                    "exchange": DATA_FETCH_CONFIG["exchange"],
                    "symboltoken": token,
                    "interval": DATA_FETCH_CONFIG["interval"],
                    "fromdate": from_date,
                    "todate": to_date
                }
                
                response = self.smart_api.getCandleData(params)
                
                if response and response.get('status') and response.get('data'):
                    candles = response['data']
                    
                    # Find the candle that matches our target time
                    for candle in candles:
                        candle_time_str = candle[0].replace('Z', '+00:00')
                        candle_time = datetime.fromisoformat(candle_time_str)
                        candle_time = candle_time.replace(tzinfo=None)
                        
                        # Check if this candle matches our target time (within 1 minute tolerance)
                        time_diff = abs((candle_time - target_time).total_seconds())
                        if time_diff <= 60:  # Within 1 minute
                            
                            fresh_data = {
                                "symbol": symbol,
                                "token": token,
                                "exchange": DATA_FETCH_CONFIG["exchange"],
                                "timestamp": candle_time,
                                "open_price": float(candle[1]),
                                "high_price": float(candle[2]),
                                "low_price": float(candle[3]),
                                "close_price": float(candle[4]),
                                "volume": int(candle[5])
                            }
                            
                            self.logger.info(f"✅ FRESH DATA: {symbol} at {target_time.strftime('%H:%M')}: ₹{fresh_data['close_price']:.2f}")
                            return fresh_data
                
                self.logger.warning(f"⚠️ No fresh data found for {symbol} at {target_time.strftime('%H:%M')} on attempt {attempt + 1}")
                
                # Wait before retry
                if attempt < self.MAX_RETRIES - 1:
                    delay = self.RETRY_DELAYS[attempt]
                    self.logger.info(f"⏳ Waiting {delay}s before retry...")
                    time.sleep(delay)
                
            except Exception as e:
                self.logger.error(f"❌ Error fetching fresh data for {symbol} (attempt {attempt + 1}): {e}")
                if attempt < self.MAX_RETRIES - 1:
                    time.sleep(self.RETRY_DELAYS[attempt])
                    continue
        
        self.logger.error(f"❌ Failed to fetch fresh data for {symbol} after {self.MAX_RETRIES} attempts")
        return None
    
    def load_symbols_from_token_file(self) -> Dict[str, str]:
        """Load symbols and tokens from angelone_tokens.txt"""
        try:
            symbols_dict = {}
            with open('Data/angelone_tokens.txt', 'r') as f:
                for line in f:
                    line = line.strip()
                    if ':' in line:
                        symbol, token = line.split(':', 1)
                        symbols_dict[symbol] = token
            
            self.logger.info(f"📊 Loaded {len(symbols_dict)} symbols from token file")
            return symbols_dict
            
        except Exception as e:
            self.logger.error(f"❌ Error loading symbols: {e}")
            return {}
    
    def store_fresh_data_with_fr(self, candle_data: Dict) -> bool:
        """Store fresh data with proper F/R calculation"""
        try:
            conn = sqlite3.connect(self.DB_PATH, timeout=30.0)
            cursor = conn.cursor()
            
            symbol = candle_data['symbol']
            current_price = candle_data['close_price']
            timestamp = candle_data['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
            
            # Get previous close price for F/R calculation
            cursor.execute('''
            SELECT close_price FROM trading_data 
            WHERE symbol = ? AND timestamp < ? 
            ORDER BY timestamp DESC 
            LIMIT 1
            ''', (symbol, timestamp))
            
            result = cursor.fetchone()
            
            if result:
                previous_close = result[0]
                
                # Calculate F/R movement
                if current_price > previous_close:
                    fr_movement = 'R'  # Rise
                elif current_price < previous_close:
                    fr_movement = 'F'  # Fall
                else:
                    fr_movement = 'FLAT'  # This should be rare with fresh data
                
                self.logger.info(f"📊 {symbol}: ₹{previous_close:.2f} → ₹{current_price:.2f} = {fr_movement}")
            else:
                # First data point
                fr_movement = 'BASE'
                previous_close = current_price
            
            # Insert fresh data
            cursor.execute('''
            INSERT OR REPLACE INTO trading_data 
            (symbol, token, exchange, timestamp, open_price, high_price, 
             low_price, close_price, volume, fr_movement, previous_close)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                candle_data['symbol'],
                candle_data['token'],
                candle_data['exchange'],
                timestamp,
                candle_data['open_price'],
                candle_data['high_price'],
                candle_data['low_price'],
                candle_data['close_price'],
                candle_data['volume'],
                fr_movement,
                previous_close
            ))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"✅ Stored FRESH data: {symbol} at {timestamp} = ₹{current_price:.2f} [{fr_movement}]")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error storing fresh data: {e}")
            return False

    def clean_duplicate_data(self) -> Dict:
        """Main method to clean duplicate data and retry with fresh fetching"""
        try:
            self.logger.info("🚀 STARTING DUPLICATE DATA CLEANING")

            # Step 1: Detect duplicate data
            duplicate_records = self.detect_duplicate_data()

            if not duplicate_records:
                self.logger.info("✅ No duplicate data found")
                return {'success': 0, 'failed': 0, 'total': 0, 'message': 'No duplicates found'}

            # Step 2: Load symbols and tokens
            symbols_dict = self.load_symbols_from_token_file()

            # Step 3: Process each duplicate record
            cleaned_count = 0
            failed_count = 0

            for record in duplicate_records:
                symbol = record['symbol']
                timestamp = record['timestamp']
                record_id = record['id']
                issue = record['issue']

                self.logger.info(f"🔧 Cleaning {symbol} at {timestamp} (Issue: {issue})")

                # Get token for this symbol
                token = symbols_dict.get(symbol)
                if not token:
                    self.logger.error(f"❌ No token found for {symbol}")
                    failed_count += 1
                    continue

                # Step 3a: Delete the duplicate record
                if self.delete_duplicate_record(record_id):

                    # Step 3b: Fetch fresh data
                    fresh_data = self.fetch_fresh_data(symbol, token, timestamp)

                    if fresh_data:
                        # Step 3c: Store fresh data with F/R calculation
                        if self.store_fresh_data_with_fr(fresh_data):
                            cleaned_count += 1
                            self.logger.info(f"✅ {symbol}: Cleaned and replaced with fresh data")
                        else:
                            failed_count += 1
                            self.logger.error(f"❌ {symbol}: Failed to store fresh data")
                    else:
                        failed_count += 1
                        self.logger.error(f"❌ {symbol}: Failed to fetch fresh data")
                else:
                    failed_count += 1
                    self.logger.error(f"❌ {symbol}: Failed to delete duplicate record")

                # Rate limiting between symbols
                time.sleep(self.REQUEST_DELAY_SECONDS)

            self.logger.info(f"🎉 DUPLICATE DATA CLEANING COMPLETED")
            self.logger.info(f"📊 Results: {cleaned_count} cleaned, {failed_count} failed out of {len(duplicate_records)} duplicates")

            return {
                'success': cleaned_count,
                'failed': failed_count,
                'total': len(duplicate_records),
                'message': f'Cleaned {cleaned_count} duplicate records with fresh data'
            }

        except Exception as e:
            self.logger.error(f"❌ Error in clean_duplicate_data: {e}")
            return {'success': 0, 'failed': 0, 'total': 0, 'error': str(e)}

# Main execution
if __name__ == "__main__":
    cleaner = DuplicateDataCleaner()
    result = cleaner.clean_duplicate_data()
    print(f"📊 Final Result: {result}")
