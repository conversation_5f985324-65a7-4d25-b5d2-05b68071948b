#!/usr/bin/env python3
"""
DB1 ENGINE - COMPLETE DB1 SYSTEM IN ONE FILE

ROLE: Complete DB1 operations - 15-minute data processing
FUNCTIONS:
- Calculate F/R movements from existing data (PURE SQL) - FAST SQL QUERIES
- Detect 4F+1R patterns (PURE SQL) - FAST SQL QUERIES
- Generate BUY signals (PURE SQL) - FAST SQL QUERIES
- Transmit signals to DB2 (PURE SQL) - FAST SQL QUERIES
- GOLD/SILVER/BRONZE priority classification (PURE SQL) - FAST SQL QUERIES
- Handle hardcoded market timings (9:15-15:15, except weekends)
- Data integrity checking for missing data

DATABASE: trading_data.db
AUTO-TRIGGER: Called by realtime_data_fetcher.py every 15 minutes (9:15, 9:30, 9:45...15:15)
"""

import sqlite3
import logging
from datetime import datetime
from typing import List, Dict, Optional

class DB1Engine:
    """Complete DB1 system - Pattern detection + Signal generation"""
    
    def __init__(self):
        self.db1_path = 'Data/trading_data.db'
        self.db2_path = 'Data/trading_operations.db'
        self.logger = logging.getLogger(__name__)
        
    def run_complete_db1_cycle(self) -> Dict:
        """MAIN FUNCTION: Complete DB1 cycle - Called by realtime_data_fetcher.py"""
        try:
            self.logger.info("🔄 RUNNING COMPLETE DB1 CYCLE")
            
            # Step 1: Calculate F/R movements
            fr_updated = self.calculate_fr_movements_for_all_symbols()
            
            # Step 2: Detect 4F+1R patterns
            detected_patterns = self.scan_all_symbols_for_4f1r_patterns()
            
            # Step 3: Generate BUY signals
            generated_signals = self.generate_buy_signals_from_patterns(detected_patterns)
            
            # Step 4: Transmit signals to DB2
            transmitted_count = self.transmit_signals_to_db2()
            
            result = {
                'fr_updated': fr_updated,
                'patterns_detected': len(detected_patterns),
                'signals_generated': len(generated_signals),
                'signals_transmitted': transmitted_count,
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"✅ DB1 CYCLE COMPLETE: {len(generated_signals)} signals → {transmitted_count} transmitted")
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Error in DB1 cycle: {e}")
            return {'error': str(e)}
    
    def calculate_fr_movements_for_all_symbols(self) -> int:
        """Calculate F/R movements for all symbols - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db1_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Get all symbols
            cursor.execute('SELECT DISTINCT symbol FROM trading_data ORDER BY symbol')
            symbols = [row[0] for row in cursor.fetchall()]
            
            updated_count = 0
            for symbol in symbols:
                # Get last 2 records for F/R calculation
                cursor.execute('''
                SELECT id, close_price FROM trading_data 
                WHERE symbol = ? ORDER BY timestamp DESC LIMIT 2
                ''', (symbol,))
                
                records = cursor.fetchall()
                if len(records) >= 2:
                    current_id, current_close = records[0]
                    previous_id, previous_close = records[1]
                    
                    # Calculate F/R movement
                    if current_close > previous_close:
                        fr_movement = 'R'
                    elif current_close < previous_close:
                        fr_movement = 'F'
                    else:
                        fr_movement = 'N'
                    
                    # Update current record
                    cursor.execute('''
                    UPDATE trading_data SET fr_movement = ?, previous_close = ? WHERE id = ?
                    ''', (fr_movement, previous_close, current_id))
                    
                    updated_count += 1
            
            conn.commit()
            conn.close()
            
            if updated_count > 0:
                self.logger.info(f"📊 DB1: Updated F/R movements for {updated_count} symbols")
            
            return updated_count
            
        except Exception as e:
            self.logger.error(f"❌ Error calculating F/R movements: {e}")
            return 0
    
    def scan_all_symbols_for_4f1r_patterns(self) -> List[Dict]:
        """Detect 4F+1R patterns for all symbols - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db1_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Get symbols with at least 5 data points
            cursor.execute('''
            SELECT symbol, COUNT(*) as count FROM trading_data 
            GROUP BY symbol HAVING COUNT(*) >= 5
            ''')
            
            symbols = cursor.fetchall()
            detected_patterns = []
            
            for symbol, count in symbols:
                pattern_info = self.detect_4f1r_pattern_for_symbol(cursor, symbol)
                if pattern_info:
                    detected_patterns.append(pattern_info)
            
            conn.close()
            return detected_patterns
            
        except Exception as e:
            self.logger.error(f"❌ Error scanning for patterns: {e}")
            return []
    
    def detect_4f1r_pattern_for_symbol(self, cursor, symbol: str) -> Optional[Dict]:
        """Detect 4F+1R pattern for single symbol - PURE SQL"""
        try:
            # Get last 5 data points
            cursor.execute('''
            SELECT timestamp, close_price, fr_movement FROM trading_data 
            WHERE symbol = ? ORDER BY timestamp DESC LIMIT 5
            ''', (symbol,))
            
            results = cursor.fetchall()
            if len(results) < 5:
                return None
            
            # Reverse to chronological order
            data_points = list(reversed(results))
            
            # Extract movements (skip first point)
            movements = [row[2] for row in data_points[1:]]  # Skip first point
            prices = [row[1] for row in data_points]
            
            # Check for FFFR pattern (4F+1R)
            if len(movements) == 4 and ''.join(movements) == 'FFFR':
                # Validate 0.5% drop
                start_price = prices[0]
                lowest_price = min(prices[1:4])  # Lowest in the 3 falls
                drop_percentage = ((start_price - lowest_price) / start_price) * 100
                
                if drop_percentage >= 0.5:
                    signal_price = prices[-1]  # Price at R point
                    
                    self.logger.info(f"🎯 4F+1R DETECTED: {symbol} @ ₹{signal_price:.2f} (Drop: {drop_percentage:.2f}%)")
                    
                    return {
                        'symbol': symbol,
                        'pattern': 'FFFR',
                        'signal_price': signal_price,
                        'drop_percentage': drop_percentage,
                        'detected_at': datetime.now()
                    }
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ Error detecting pattern for {symbol}: {e}")
            return None
    
    def generate_buy_signals_from_patterns(self, patterns: List[Dict]) -> List[Dict]:
        """Generate BUY signals from detected patterns - PURE SQL"""
        try:
            if not patterns:
                return []
            
            conn = sqlite3.connect(self.db1_path, timeout=30.0)
            cursor = conn.cursor()
            
            generated_signals = []
            
            for pattern in patterns:
                # Check if signal already exists
                cursor.execute('''
                SELECT id FROM trading_signals 
                WHERE symbol = ? AND signal_type = 'BUY'
                ORDER BY created_at DESC LIMIT 1
                ''', (pattern['symbol'],))
                
                existing = cursor.fetchone()
                if existing:
                    # Check if recent (within 1 hour)
                    cursor.execute('''
                    SELECT created_at FROM trading_signals WHERE id = ?
                    ''', (existing[0],))
                    
                    created_at = cursor.fetchone()[0]
                    created_time = datetime.fromisoformat(created_at)
                    time_diff = datetime.now() - created_time
                    
                    if time_diff.total_seconds() < 3600:  # Less than 1 hour
                        continue
                
                # Insert new signal
                cursor.execute('''
                INSERT INTO trading_signals 
                (symbol, signal_type, price, pattern_sequence, created_at)
                VALUES (?, ?, ?, ?, ?)
                ''', (
                    pattern['symbol'], 'BUY', pattern['signal_price'], 
                    pattern['pattern'], datetime.now()
                ))
                
                signal_id = cursor.lastrowid
                generated_signals.append({**pattern, 'signal_id': signal_id})
                
                self.logger.info(f"💰 BUY SIGNAL GENERATED: {pattern['symbol']} @ ₹{pattern['signal_price']:.2f}")
            
            conn.commit()
            conn.close()
            
            return generated_signals
            
        except Exception as e:
            self.logger.error(f"❌ Error generating signals: {e}")
            return []
    
    def transmit_signals_to_db2(self) -> int:
        """Transmit BUY signals to DB2 - PURE SQL"""
        try:
            # Get untransmitted signals
            conn1 = sqlite3.connect(self.db1_path, timeout=30.0)
            cursor1 = conn1.cursor()
            
            cursor1.execute('''
            SELECT id, symbol, signal_type, price, pattern_sequence, created_at
            FROM trading_signals WHERE signal_type = 'BUY'
            ORDER BY created_at ASC
            ''')
            
            signals = cursor1.fetchall()
            conn1.close()
            
            if not signals:
                return 0
            
            # Transmit to DB2
            conn2 = sqlite3.connect(self.db2_path, timeout=30.0)
            cursor2 = conn2.cursor()
            
            transmitted_count = 0
            
            for signal in signals:
                db1_id, symbol, signal_type, price, pattern, created_at = signal
                
                # Check if already exists in DB2
                cursor2.execute('''
                SELECT id FROM db2_signals_received 
                WHERE symbol = ? AND signal_price = ? AND db1_signal_id = ?
                ''', (symbol, price, db1_id))
                
                if not cursor2.fetchone():
                    # Insert into DB2
                    cursor2.execute('''
                    INSERT INTO db2_signals_received 
                    (symbol, signal_type, signal_price, status, received_time, db1_signal_id)
                    VALUES (?, ?, ?, ?, ?, ?)
                    ''', (symbol, signal_type, price, 'PENDING', datetime.now(), db1_id))
                    
                    transmitted_count += 1
                    self.logger.info(f"📡 SIGNAL TRANSMITTED: {symbol} @ ₹{price:.2f}")
            
            conn2.commit()
            conn2.close()
            
            if transmitted_count > 0:
                self.logger.info(f"📤 TRANSMITTED {transmitted_count} SIGNALS TO DB2")
            
            return transmitted_count
            
        except Exception as e:
            self.logger.error(f"❌ Error transmitting signals: {e}")
            return 0
    
    def get_priority_queue_status(self) -> Dict:
        """Get GOLD/SILVER/BRONZE priority status - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db1_path, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('SELECT DISTINCT symbol FROM trading_data')
            symbols = [row[0] for row in cursor.fetchall()]
            
            priority_counts = {'GOLD': 0, 'SILVER': 0, 'BRONZE': 0, 'REMAINING': 0}
            priority_symbols = {'GOLD': [], 'SILVER': [], 'BRONZE': [], 'REMAINING': []}
            
            for symbol in symbols:
                priority = self.analyze_pattern_priority(cursor, symbol)
                if priority in priority_counts:
                    priority_counts[priority] += 1
                    priority_symbols[priority].append(symbol)
            
            conn.close()
            
            return {
                'counts': priority_counts,
                'symbols': priority_symbols,
                'total_symbols': len(symbols)
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error getting priority status: {e}")
            return {}
    
    def analyze_pattern_priority(self, cursor, symbol: str) -> str:
        """Analyze pattern priority for symbol"""
        try:
            cursor.execute('''
            SELECT fr_movement FROM trading_data 
            WHERE symbol = ? ORDER BY timestamp DESC LIMIT 5
            ''', (symbol,))
            
            results = cursor.fetchall()
            if len(results) < 5:
                return 'REMAINING'
            
            movements = [row[0] for row in reversed(results)][1:]  # Skip first, reverse to chronological
            
            if len(movements) == 4:
                pattern = ''.join(movements)
                if pattern == 'FFFR':
                    return 'GOLD'
                elif pattern.endswith('FFF'):
                    return 'SILVER'
                elif pattern.endswith('FF'):
                    return 'BRONZE'
            
            return 'REMAINING'
            
        except Exception as e:
            return 'REMAINING'

# Global instance
db1_engine = DB1Engine()
