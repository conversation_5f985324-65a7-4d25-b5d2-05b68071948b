#!/usr/bin/env python3
"""
DB2 Signal Receiver - PURE SQL OPERATIONS

This module receives and manages signals from DB1.
NO API CALLS - Only SQL operations on DB2 data.
FOLLOWS EXACT DOCUMENTATION SPECIFICATIONS.
"""

import sqlite3
import logging
from typing import List, Dict, Optional
from datetime import datetime

class DB2SignalReceiver:
    """Pure SQL signal receiver for DB2"""
    
    def __init__(self):
        self.db_path = 'Data/trading_operations.db'
        self.logger = logging.getLogger(__name__)
        
    def get_pending_signals(self) -> List[Dict]:
        """Get all signals pending Layer 2 confirmation - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT id, symbol, signal_type, signal_price, status, received_time, db1_signal_id
            FROM db2_signals_received 
            WHERE status = 'PENDING'
            ORDER BY received_time ASC
            ''')
            
            results = cursor.fetchall()
            conn.close()
            
            signals = []
            for row in results:
                signals.append({
                    'id': row[0],
                    'symbol': row[1],
                    'signal_type': row[2],
                    'signal_price': row[3],
                    'status': row[4],
                    'received_time': row[5],
                    'db1_signal_id': row[6]
                })
            
            return signals
            
        except Exception as e:
            self.logger.error(f"❌ Error getting pending signals: {e}")
            return []
    
    def mark_signal_confirmed(self, signal_id: int, confirmation_type: str) -> bool:
        """Mark signal as confirmed (RR or FF) - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('''
            UPDATE db2_signals_received 
            SET status = ?
            WHERE id = ?
            ''', (confirmation_type, signal_id))
            
            conn.commit()
            conn.close()
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error marking signal {signal_id} as confirmed: {e}")
            return False
    
    def get_confirmed_signals(self) -> List[Dict]:
        """Get all confirmed signals ready for execution - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT id, symbol, signal_type, signal_price, status, received_time, db1_signal_id
            FROM db2_signals_received 
            WHERE status IN ('RR', 'FF', 'CONFIRMED')
            ORDER BY received_time ASC
            ''')
            
            results = cursor.fetchall()
            conn.close()
            
            signals = []
            for row in results:
                signals.append({
                    'id': row[0],
                    'symbol': row[1],
                    'signal_type': row[2],
                    'signal_price': row[3],
                    'status': row[4],
                    'received_time': row[5],
                    'db1_signal_id': row[6]
                })
            
            return signals
            
        except Exception as e:
            self.logger.error(f"❌ Error getting confirmed signals: {e}")
            return []
    
    def get_signal_by_id(self, signal_id: int) -> Optional[Dict]:
        """Get signal details by ID - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT id, symbol, signal_type, signal_price, status, received_time, db1_signal_id
            FROM db2_signals_received 
            WHERE id = ?
            ''', (signal_id,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return {
                    'id': result[0],
                    'symbol': result[1],
                    'signal_type': result[2],
                    'signal_price': result[3],
                    'status': result[4],
                    'received_time': result[5],
                    'db1_signal_id': result[6]
                }
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Error getting signal {signal_id}: {e}")
            return None
    
    def get_signals_by_symbol(self, symbol: str) -> List[Dict]:
        """Get all signals for a specific symbol - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT id, symbol, signal_type, signal_price, status, received_time, db1_signal_id
            FROM db2_signals_received 
            WHERE symbol = ?
            ORDER BY received_time DESC
            ''', (symbol,))
            
            results = cursor.fetchall()
            conn.close()
            
            signals = []
            for row in results:
                signals.append({
                    'id': row[0],
                    'symbol': row[1],
                    'signal_type': row[2],
                    'signal_price': row[3],
                    'status': row[4],
                    'received_time': row[5],
                    'db1_signal_id': row[6]
                })
            
            return signals
            
        except Exception as e:
            self.logger.error(f"❌ Error getting signals for {symbol}: {e}")
            return []
    
    def get_signal_statistics(self) -> Dict:
        """Get signal reception and processing statistics - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Total signals received
            cursor.execute('SELECT COUNT(*) FROM db2_signals_received')
            total_received = cursor.fetchone()[0]
            
            # Pending confirmation
            cursor.execute('SELECT COUNT(*) FROM db2_signals_received WHERE status = "PENDING"')
            pending_confirmation = cursor.fetchone()[0]
            
            # Confirmed signals
            cursor.execute('SELECT COUNT(*) FROM db2_signals_received WHERE status IN ("RR", "FF", "CONFIRMED")')
            confirmed_signals = cursor.fetchone()[0]
            
            # Signals by symbol
            cursor.execute('''
            SELECT symbol, COUNT(*) as count 
            FROM db2_signals_received 
            GROUP BY symbol 
            ORDER BY count DESC
            ''')
            
            signals_by_symbol = cursor.fetchall()
            conn.close()
            
            return {
                'total_received': total_received,
                'pending_confirmation': pending_confirmation,
                'confirmed_signals': confirmed_signals,
                'confirmation_rate': (confirmed_signals / max(1, total_received)) * 100,
                'signals_by_symbol': dict(signals_by_symbol)
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error getting signal statistics: {e}")
            return {}

# Global instance
db2_signal_receiver = DB2SignalReceiver()
