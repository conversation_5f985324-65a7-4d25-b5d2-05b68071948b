#!/usr/bin/env python3
"""
DB2 Data Collector - PURE SQL OPERATIONS

This module handles 2-minute data operations for DB2 with perfect F/R calculation.
NO API CALLS - Only SQL operations on existing data.
Uses SAME STRUCTURE as DB1 but with 2-minute intervals.
FOLLOWS EXACT DOCUMENTATION SPECIFICATIONS.
"""

import sqlite3
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional

class DB2DataCollector:
    """Pure SQL DB2 data operations with 2-minute intervals"""
    
    def __init__(self):
        self.db_path = 'Data/trading_operations.db'
        self.logger = logging.getLogger(__name__)
        
    def calculate_fr_movements_for_pending_symbols(self) -> int:
        """Calculate F/R movements for symbols with pending signals - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Get symbols with pending signals
            cursor.execute('''
            SELECT DISTINCT symbol FROM db2_signals_received 
            WHERE status = 'PENDING'
            ''')
            
            symbols = [row[0] for row in cursor.fetchall()]
            
            updated_count = 0
            
            for symbol in symbols:
                # Calculate F/R for this symbol
                success = self._calculate_and_update_fr_movement(cursor, symbol)
                if success:
                    updated_count += 1
            
            conn.commit()
            conn.close()
            
            if updated_count > 0:
                self.logger.info(f"📊 DB2: Updated F/R movements for {updated_count} symbols")
            
            return updated_count
                
        except Exception as e:
            self.logger.error(f"❌ Error calculating DB2 F/R movements: {e}")
            return 0
    
    def _calculate_and_update_fr_movement(self, cursor, symbol: str) -> bool:
        """Calculate and update F/R movement for a symbol - PURE SQL"""
        try:
            # Get last 2 records for this symbol to calculate F/R
            cursor.execute('''
            SELECT id, close_price, timestamp 
            FROM db2_trading_data 
            WHERE symbol = ? 
            ORDER BY timestamp DESC 
            LIMIT 2
            ''', (symbol,))
            
            records = cursor.fetchall()
            
            if len(records) < 2:
                # Not enough data for F/R calculation
                return False
            
            # Current record (most recent)
            current_id, current_close, current_timestamp = records[0]
            # Previous record
            previous_id, previous_close, previous_timestamp = records[1]
            
            # Calculate F/R movement (SAME LOGIC AS DB1)
            if current_close > previous_close:
                fr_movement = 'R'  # RISE
            elif current_close < previous_close:
                fr_movement = 'F'  # FALL
            else:
                fr_movement = 'N'  # NO CHANGE
            
            # Update the current record with F/R movement and previous close
            cursor.execute('''
            UPDATE db2_trading_data 
            SET fr_movement = ?, previous_close = ?
            WHERE id = ?
            ''', (fr_movement, previous_close, current_id))
            
            self.logger.debug(f"📊 DB2 {symbol}: ₹{previous_close:.2f} → ₹{current_close:.2f} = {fr_movement}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error calculating DB2 F/R for {symbol}: {e}")
            return False
    
    def get_recent_2min_data(self, symbol: str, limit: int = 3) -> List[Dict]:
        """Get recent 2-minute trading data for confirmation analysis - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT timestamp, close_price, fr_movement, previous_close
            FROM db2_trading_data 
            WHERE symbol = ? 
            ORDER BY timestamp DESC 
            LIMIT ?
            ''', (symbol, limit))
            
            results = cursor.fetchall()
            conn.close()
            
            # Convert to list of dictionaries
            data = []
            for row in results:
                data.append({
                    'timestamp': row[0],
                    'close_price': row[1],
                    'fr_movement': row[2],
                    'previous_close': row[3]
                })
            
            # Reverse to get chronological order
            return list(reversed(data))
            
        except Exception as e:
            self.logger.error(f"❌ Error getting recent 2-min data for {symbol}: {e}")
            return []
    
    def get_symbols_with_pending_signals(self) -> List[str]:
        """Get symbols that have pending signals and need 2-minute data - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT DISTINCT symbol FROM db2_signals_received 
            WHERE status = 'PENDING'
            ''')
            
            results = cursor.fetchall()
            conn.close()
            
            return [row[0] for row in results]
            
        except Exception as e:
            self.logger.error(f"❌ Error getting symbols with pending signals: {e}")
            return []
    
    def store_2min_data_from_api(self, symbol: str, candle_data: Dict) -> bool:
        """Store 2-minute data from API in DB2 - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Store data in db2_trading_data table (SAME STRUCTURE AS DB1)
            cursor.execute('''
            INSERT OR REPLACE INTO db2_trading_data 
            (symbol, close_price, timestamp, fr_movement, previous_close, is_800_base_point)
            VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                symbol,
                candle_data['close_price'],
                candle_data['timestamp'],
                'START',  # Will be calculated later
                candle_data['close_price'],  # Initial previous_close
                False  # Not a ₹800 base point
            ))
            
            conn.commit()
            conn.close()
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error storing 2-min data for {symbol}: {e}")
            return False
    
    def mark_800_base_point(self, symbol: str, price: float, timestamp: datetime) -> bool:
        """Mark a data point as ₹800 profit base point - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Insert or update the ₹800 base point
            cursor.execute('''
            INSERT OR REPLACE INTO db2_trading_data 
            (symbol, close_price, timestamp, fr_movement, previous_close, is_800_base_point)
            VALUES (?, ?, ?, ?, ?, ?)
            ''', (symbol, price, timestamp, 'BASE', price, True))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"📍 DB2: Marked ₹800 base point for {symbol} @ ₹{price:.2f}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error marking ₹800 base point for {symbol}: {e}")
            return False
    
    def get_data_statistics(self) -> Dict:
        """Get DB2 data statistics - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Total data points
            cursor.execute('SELECT COUNT(*) FROM db2_trading_data')
            total_data_points = cursor.fetchone()[0]
            
            # Unique symbols
            cursor.execute('SELECT COUNT(DISTINCT symbol) FROM db2_trading_data')
            unique_symbols = cursor.fetchone()[0]
            
            # ₹800 base points
            cursor.execute('SELECT COUNT(*) FROM db2_trading_data WHERE is_800_base_point = TRUE')
            base_points_800 = cursor.fetchone()[0]
            
            # Data by symbol
            cursor.execute('''
            SELECT symbol, COUNT(*) as count 
            FROM db2_trading_data 
            GROUP BY symbol 
            ORDER BY count DESC
            ''')
            
            data_by_symbol = cursor.fetchall()
            conn.close()
            
            return {
                'total_data_points': total_data_points,
                'unique_symbols': unique_symbols,
                'base_points_800': base_points_800,
                'data_by_symbol': dict(data_by_symbol)
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error getting DB2 data statistics: {e}")
            return {}

# Global instance
db2_data_collector = DB2DataCollector()
