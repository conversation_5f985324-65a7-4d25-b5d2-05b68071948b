#!/usr/bin/env python3
"""
Test All Endpoints - Verify all Flask API endpoints work
"""
import sys
import os

# Add current directory to path
sys.path.insert(0, os.getcwd())

def test_all_endpoints():
    """Test all Flask API endpoints"""
    print("🔄 Testing ALL Flask API endpoints...")
    
    try:
        # Import Flask app
        import flask_app
        app = flask_app.app
        
        # Create test client
        with app.test_client() as client:
            
            # All endpoints to test
            endpoints = [
                # Legacy compatibility
                '/api/sql-dashboard/summary',
                '/api/portfolio-status',
                '/api/active-positions',
                '/api/priority-queue/status',
                
                # DB1 endpoints
                '/api/db1/signals',
                '/api/db1/priority-queue',
                
                # DB2 endpoints
                '/api/db2/pending-confirmations',
                '/api/db2/trading-positions',
                '/api/db2/statistics',
                '/api/db2-signals',
                '/api/layer2-confirmations',
                
                # System endpoints
                '/api/data-integrity',
                '/api/symbols',
                
                # Data endpoints
                '/api/trading-data/RELIANCE',  # Test with a symbol
            ]
            
            # POST endpoints to test
            post_endpoints = [
                '/api/start-trading',
                '/api/stop-trading',
                '/api/trigger-system-cycle',
                '/api/comprehensive-check',
                '/api/start-data-fetch',
                '/api/stop-data-fetch'
            ]
            
            print("=" * 60)
            print("🔄 Testing GET endpoints...")
            
            success_count = 0
            total_count = 0
            
            for endpoint in endpoints:
                total_count += 1
                try:
                    print(f"🔄 GET {endpoint}")
                    response = client.get(endpoint)
                    
                    if response.status_code == 200:
                        data = response.get_json()
                        if data and data.get('success'):
                            print(f"✅ {endpoint}: SUCCESS")
                            success_count += 1
                            
                            # Show key data
                            if 'count' in data:
                                print(f"   📊 Count: {data['count']}")
                            elif 'summary' in data:
                                summary = data['summary']
                                print(f"   📊 System: {summary.get('system_status', 'UNKNOWN')}")
                        else:
                            print(f"⚠️ {endpoint}: API returned success=False")
                            if data:
                                print(f"   Error: {data.get('error', 'Unknown error')}")
                    else:
                        print(f"❌ {endpoint}: HTTP {response.status_code}")
                        
                except Exception as e:
                    print(f"❌ {endpoint}: {e}")
            
            print("\n" + "=" * 60)
            print("🔄 Testing POST endpoints...")
            
            for endpoint in post_endpoints:
                total_count += 1
                try:
                    print(f"🔄 POST {endpoint}")
                    response = client.post(endpoint)
                    
                    if response.status_code == 200:
                        data = response.get_json()
                        if data and data.get('success'):
                            print(f"✅ {endpoint}: SUCCESS")
                            success_count += 1
                        else:
                            print(f"⚠️ {endpoint}: API returned success=False")
                            if data:
                                print(f"   Error: {data.get('error', 'Unknown error')}")
                    else:
                        print(f"❌ {endpoint}: HTTP {response.status_code}")
                        
                except Exception as e:
                    print(f"❌ {endpoint}: {e}")
            
            print("\n" + "=" * 60)
            print(f"📊 RESULTS: {success_count}/{total_count} endpoints working")
            
            if success_count == total_count:
                print("🎉 ALL ENDPOINTS WORKING!")
            elif success_count > total_count * 0.8:
                print("✅ Most endpoints working - system ready!")
            else:
                print("⚠️ Some endpoints need attention")
            
    except Exception as e:
        print(f"❌ Error testing endpoints: {e}")

def main():
    """Main function"""
    print("🚀 COMPREHENSIVE FLASK API TEST")
    print("Testing all endpoints without starting HTTP server...")
    
    test_all_endpoints()
    
    print("\n💡 To start the full Flask server:")
    print("   python flask_app.py")
    print("   Then visit: http://localhost:5000")

if __name__ == "__main__":
    main()
