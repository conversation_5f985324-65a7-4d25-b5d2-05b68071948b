#!/usr/bin/env python3
"""
Perfect DB1-DB2 Integration - EXACT DOCUMENTATION SPECIFICATIONS

This module provides integration points for realtime_data_fetcher.py to trigger:
1. DB1 analysis after 15-minute data collection
2. DB2 trade execution and profit monitoring

AUTO-TRIGGER: Called by realtime_data_fetcher.py every 15 minutes
FOLLOWS EXACT DOCUMENTATION SPECIFICATIONS
"""

import logging
from datetime import datetime
from typing import Dict, List

# Import all perfect components
from db1_data_collector import db1_data_collector
from db1_pattern_detector import db1_pattern_detector
from perfect_db1_signal_generator import perfect_db1_signal_generator
from db1_signal_transmitter import db1_signal_transmitter
from db2_trade_executor import db2_trade_executor

class PerfectDB1DB2Integration:
    """Perfect integration between DB1 and DB2 systems"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.logger.info("🚀 PERFECT DB1-DB2 INTEGRATION INITIALIZED")
    
    def trigger_db1_analysis_after_data_fetch(self) -> Dict:
        """
        MAIN INTEGRATION POINT: Called by realtime_data_fetcher.py after 15-minute data collection
        EXACT DOCUMENTATION: AUTO-TRIGGER every 15 minutes
        """
        try:
            self.logger.info("🔄 TRIGGERING DB1 ANALYSIS AFTER 15-MINUTE DATA FETCH")
            
            # Step 1: Calculate F/R movements for all symbols (PURE SQL)
            fr_updated = db1_data_collector.calculate_fr_movements_for_all_symbols()
            self.logger.info(f"📊 DB1: Updated F/R movements for {fr_updated} symbols")
            
            # Step 2: Detect 4F+1R patterns (PURE SQL)
            detected_patterns = db1_pattern_detector.scan_all_symbols_for_patterns()
            self.logger.info(f"🎯 DB1: Detected {len(detected_patterns)} 4F+1R patterns")
            
            # Step 3: Generate BUY signals (PURE SQL)
            generated_signals = perfect_db1_signal_generator.scan_and_generate_signals()
            self.logger.info(f"💰 DB1: Generated {len(generated_signals)} BUY signals")
            
            # Step 4: Transmit signals to DB2 (PURE SQL)
            transmitted_count = db1_signal_transmitter.transmit_signals_to_db2()
            self.logger.info(f"📡 DB1→DB2: Transmitted {transmitted_count} signals")
            
            # Step 5: Trigger DB2 processing
            db2_result = self.trigger_db2_processing()
            
            result = {
                'fr_updated': fr_updated,
                'patterns_detected': len(detected_patterns),
                'signals_generated': len(generated_signals),
                'signals_transmitted': transmitted_count,
                'db2_processing': db2_result,
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"✅ DB1 ANALYSIS COMPLETE: {len(generated_signals)} signals → {transmitted_count} transmitted")
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Error in DB1 analysis: {e}")
            return {'error': str(e)}
    
    def trigger_db2_processing(self) -> Dict:
        """
        MAIN INTEGRATION POINT: Trigger DB2 trade execution and profit monitoring
        EXACT DOCUMENTATION: AUTO-TRIGGER every 15 minutes
        """
        try:
            self.logger.info("🔄 TRIGGERING DB2 TRADE EXECUTION AND PROFIT MONITORING")
            
            # Step 1: Run complete DB2 cycle
            db2_result = db2_trade_executor.run_db2_cycle()
            
            result = {
                'db2_cycle_result': db2_result,
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"✅ DB2 PROCESSING COMPLETE")
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Error in DB2 processing: {e}")
            return {'error': str(e)}
    
    def get_priority_queue_for_data_fetch(self) -> Dict:
        """
        INTEGRATION POINT: Get GOLD/SILVER/BRONZE priority queue for realtime_data_fetcher.py
        EXACT DOCUMENTATION: GOLD/SILVER/BRONZE priority logic
        """
        try:
            # Get priority status from DB1 pattern detector
            priority_status = db1_pattern_detector.get_priority_queue_status()
            
            # Format for realtime_data_fetcher compatibility
            priority_queue = {
                'GOLD': priority_status.get('symbols', {}).get('GOLD', []),
                'SILVER': priority_status.get('symbols', {}).get('SILVER', []),
                'BRONZE': priority_status.get('symbols', {}).get('BRONZE', []),
                'REMAINING': priority_status.get('symbols', {}).get('REMAINING', [])
            }
            
            self.logger.info(f"📊 PRIORITY QUEUE: GOLD={len(priority_queue['GOLD'])}, SILVER={len(priority_queue['SILVER'])}, BRONZE={len(priority_queue['BRONZE'])}, REMAINING={len(priority_queue['REMAINING'])}")
            
            return {
                'priority_queue': priority_queue,
                'counts': priority_status.get('counts', {}),
                'total_symbols': priority_status.get('total_symbols', 0)
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error getting priority queue: {e}")
            return {}
    
    def get_system_status_for_frontend(self) -> Dict:
        """
        INTEGRATION POINT: Get complete system status for flask_app.py APIs
        EXACT DOCUMENTATION: Frontend display requirements
        """
        try:
            # DB1 statistics
            db1_stats = {
                'signal_stats': perfect_db1_signal_generator.get_signal_statistics(),
                'transmission_stats': db1_signal_transmitter.get_transmission_statistics(),
                'priority_queue': db1_pattern_detector.get_priority_queue_status()
            }
            
            # DB2 statistics
            from db2_signal_receiver import db2_signal_receiver
            from db2_confirmation_engine import db2_confirmation_engine
            from db2_data_collector import db2_data_collector
            
            db2_stats = {
                'signal_stats': db2_signal_receiver.get_signal_statistics(),
                'confirmation_stats': db2_confirmation_engine.get_confirmation_statistics(),
                'data_stats': db2_data_collector.get_data_statistics()
            }
            
            # Recent signals for display
            recent_signals = perfect_db1_signal_generator.get_recent_signals(10)
            pending_confirmations = db2_signal_receiver.get_pending_signals()
            confirmed_signals = db2_signal_receiver.get_confirmed_signals()
            
            return {
                'db1_statistics': db1_stats,
                'db2_statistics': db2_stats,
                'recent_signals': recent_signals,
                'pending_confirmations': pending_confirmations,
                'confirmed_signals': confirmed_signals,
                'system_time': datetime.now().isoformat(),
                'integration_status': 'ACTIVE'
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error getting system status: {e}")
            return {'error': str(e)}
    
    def get_db1_signals_for_display(self) -> List[Dict]:
        """Get DB1 signals for 'Active Patterns' display in frontend"""
        try:
            return perfect_db1_signal_generator.get_recent_signals(20)
        except Exception as e:
            self.logger.error(f"❌ Error getting DB1 signals: {e}")
            return []
    
    def get_db2_confirmations_for_display(self) -> Dict:
        """Get DB2 confirmations for 'Paper Trading Brain' display in frontend"""
        try:
            from db2_signal_receiver import db2_signal_receiver
            from rolling_window_manager import get_rolling_window_manager
            
            rolling_manager = get_rolling_window_manager()
            
            return {
                'pending_signals': db2_signal_receiver.get_pending_signals(),
                'confirmed_signals': db2_signal_receiver.get_confirmed_signals(),
                'active_monitors': rolling_manager.get_active_monitors()
            }
        except Exception as e:
            self.logger.error(f"❌ Error getting DB2 confirmations: {e}")
            return {}
    
    def get_trading_positions_for_display(self) -> List[Dict]:
        """Get trading positions for 'Paper Trading Records' display in frontend"""
        try:
            import sqlite3
            
            conn = sqlite3.connect('Data/trading_operations.db', timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT symbol, buy_price, sell_price, shares_quantity, investment, 
                   current_profit, status, buy_timestamp, sell_timestamp
            FROM trading_positions 
            ORDER BY buy_timestamp DESC
            ''')
            
            positions = []
            for row in cursor.fetchall():
                positions.append({
                    'symbol': row[0],
                    'buy_price': row[1],
                    'sell_price': row[2],
                    'shares_quantity': row[3],
                    'investment': row[4],
                    'current_profit': row[5],
                    'status': row[6],
                    'buy_timestamp': row[7],
                    'sell_timestamp': row[8]
                })
            
            conn.close()
            return positions
            
        except Exception as e:
            self.logger.error(f"❌ Error getting trading positions: {e}")
            return []

# Global instance for integration
perfect_db1_db2_integration = PerfectDB1DB2Integration()
