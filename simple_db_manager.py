#!/usr/bin/env python3
"""
Simple Database Manager - Basic database operations for data storage
Replaces the old simple_database_manager.py with minimal functionality
"""
import sqlite3
import logging
from datetime import datetime
from typing import List, Dict, Optional

class SimpleDBManager:
    """Simple database manager for basic operations"""
    
    def __init__(self, db_path: str = 'Data/trading_data.db'):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self.connection = None
        
    def connect(self) -> bool:
        """Connect to database"""
        try:
            self.connection = sqlite3.connect(self.db_path, timeout=30.0)
            return True
        except Exception as e:
            self.logger.error(f"Error connecting to database: {e}")
            return False
    
    def create_tables(self) -> bool:
        """Create required tables"""
        try:
            if not self.connection:
                self.connect()
                
            cursor = self.connection.cursor()
            
            # Create trading_data table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS trading_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                token TEXT NOT NULL,
                exchange TEXT DEFAULT 'NSE',
                timestamp DATETIME NOT NULL,
                open_price REAL NOT NULL,
                high_price REAL NOT NULL,
                low_price REAL NOT NULL,
                close_price REAL NOT NULL,
                volume INTEGER NOT NULL,
                interval_type TEXT DEFAULT 'FIFTEEN_MINUTE',
                fr_movement TEXT,
                previous_close REAL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, timestamp)
            )
            ''')
            
            # Create indexes
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_trading_data_symbol ON trading_data(symbol)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_trading_data_timestamp ON trading_data(timestamp)')
            
            self.connection.commit()
            return True
            
        except Exception as e:
            self.logger.error(f"Error creating tables: {e}")
            return False
    
    def insert_trading_data(self, data_list: List[Dict]) -> bool:
        """Insert trading data into database"""
        try:
            if not self.connection:
                self.connect()
                
            cursor = self.connection.cursor()
            
            for data in data_list:
                cursor.execute('''
                INSERT OR REPLACE INTO trading_data 
                (symbol, token, exchange, timestamp, open_price, high_price, 
                 low_price, close_price, volume, interval_type)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    data['symbol'],
                    data['token'],
                    data['exchange'],
                    data['timestamp'],
                    data['open_price'],
                    data['high_price'],
                    data['low_price'],
                    data['close_price'],
                    data['volume'],
                    data['interval_type']
                ))
            
            self.connection.commit()
            return True
            
        except Exception as e:
            self.logger.error(f"Error inserting trading data: {e}")
            return False
    
    def get_latest_data(self, symbol: str, limit: int = 10) -> List[Dict]:
        """Get latest data for a symbol"""
        try:
            if not self.connection:
                self.connect()
                
            cursor = self.connection.cursor()
            
            cursor.execute('''
            SELECT timestamp, open_price, high_price, low_price, close_price, volume
            FROM trading_data 
            WHERE symbol = ?
            ORDER BY timestamp DESC
            LIMIT ?
            ''', (symbol, limit))
            
            rows = cursor.fetchall()
            
            data = []
            for row in rows:
                data.append({
                    'timestamp': row[0],
                    'open_price': row[1],
                    'high_price': row[2],
                    'low_price': row[3],
                    'close_price': row[4],
                    'volume': row[5]
                })
            
            return data
            
        except Exception as e:
            self.logger.error(f"Error getting latest data for {symbol}: {e}")
            return []
    
    def close(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            self.connection = None

# Global instance
_simple_db_manager = None

def get_simple_db_manager() -> SimpleDBManager:
    """Get global simple database manager instance"""
    global _simple_db_manager
    if _simple_db_manager is None:
        _simple_db_manager = SimpleDBManager()
    return _simple_db_manager
