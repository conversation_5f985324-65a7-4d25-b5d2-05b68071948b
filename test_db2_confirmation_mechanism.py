#!/usr/bin/env python3
"""
Test DB2 Confirmation Mechanism

This script tests the critical issues:
1. Does DB2 actually wait for RR confirmation before executing BUY?
2. Is there a proper ₹800 profit tracking mechanism?
3. How does the confirmation process work?
"""

import logging
import sqlite3
import time
from datetime import datetime
from db2_trade_executor import get_db2_trade_executor
from db1_db2_communicator import TradingSignal, get_communicator

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_buy_confirmation_mechanism():
    """Test if DB2 actually waits for RR confirmation"""
    logger.info("🔍 TESTING BUY CONFIRMATION MECHANISM")
    
    try:
        communicator = get_communicator()
        db2_executor = get_db2_trade_executor()
        
        # Clear any existing data
        conn = sqlite3.connect(db2_executor.db_path)
        cursor = conn.cursor()
        cursor.execute("DELETE FROM db2_signals_received")
        cursor.execute("DELETE FROM trading_positions")
        conn.commit()
        conn.close()
        
        # Step 1: Send BUY signal
        test_signal = TradingSignal(
            symbol='TEST_CONFIRM',
            signal_type='BUY',
            price=1000.0,
            timestamp_ns=time.time_ns(),
            pattern_info={'pattern_type': '4F+1R'},
            source='TEST'
        )
        
        logger.info("📤 Sending BUY signal to DB2...")
        success = communicator.send_signal_to_db2(test_signal)
        
        if not success:
            logger.error("❌ Failed to send signal")
            return False
        
        # Step 2: Process signal (should NOT execute BUY immediately)
        logger.info("🔄 Processing signal in DB2...")
        db2_executor.run_periodic_check()
        
        # Step 3: Check if BUY was executed immediately (WRONG!)
        conn = sqlite3.connect(db2_executor.db_path)
        cursor = conn.cursor()
        
        # Check signals table
        cursor.execute("SELECT symbol, status FROM db2_signals_received WHERE symbol = 'TEST_CONFIRM'")
        signal_result = cursor.fetchone()
        
        # Check positions table
        cursor.execute("SELECT symbol FROM trading_positions WHERE symbol = 'TEST_CONFIRM'")
        position_result = cursor.fetchone()
        
        conn.close()
        
        logger.info("📊 CONFIRMATION TEST RESULTS:")
        
        if signal_result:
            symbol, status = signal_result
            logger.info(f"   📥 Signal Status: {status}")
            
            if status == 'EXECUTED':
                logger.error("❌ CRITICAL ISSUE: BUY executed WITHOUT proper RR confirmation!")
                logger.error("   This means DB2 is not waiting for 2-minute RR pattern")
                return False
            elif status == 'CONFIRMING':
                logger.info("✅ GOOD: Signal is waiting for RR confirmation")
            else:
                logger.warning(f"⚠️ Unexpected status: {status}")
        
        if position_result:
            logger.error("❌ CRITICAL ISSUE: Position created WITHOUT RR confirmation!")
            return False
        else:
            logger.info("✅ GOOD: No position created yet (waiting for confirmation)")
        
        # Step 4: Check active monitors
        monitors = db2_executor.rolling_window_manager.get_active_monitors()
        logger.info(f"📊 Active Monitors: {len(monitors)}")
        
        for symbol, monitor_info in monitors.items():
            logger.info(f"   🔄 {symbol}: {monitor_info['signal_type']} monitor active")
            logger.info(f"      Data points: {monitor_info['data_points']}/3")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing confirmation mechanism: {e}")
        return False

def test_profit_tracking_mechanism():
    """Test ₹800 profit tracking mechanism"""
    logger.info("🔍 TESTING ₹800 PROFIT TRACKING MECHANISM")
    
    try:
        db2_executor = get_db2_trade_executor()
        
        # Create a test position manually
        from db1_db2_communicator import ActivePosition
        
        test_position = ActivePosition(
            symbol='TEST_PROFIT',
            buy_price=1000.0,
            shares_quantity=100,  # 100 shares @ ₹1000 = ₹100,000
            investment=100000.0,
            target_price=1008.0,  # ₹8 per share profit = ₹800 total
            buy_time=datetime.now(),
            current_profit=0.0,
            status='ACTIVE',
            timestamp_ns=time.time_ns()
        )
        
        # Add to active positions
        db2_executor.active_positions['TEST_PROFIT'] = test_position
        
        # Store in database
        db2_executor._store_position_in_db2(test_position)
        
        logger.info("📊 Created test position:")
        logger.info(f"   Symbol: TEST_PROFIT")
        logger.info(f"   Shares: 100 @ ₹1000 = ₹100,000")
        logger.info(f"   Target: ₹800 profit (₹1008 per share)")
        
        # Test profit monitoring
        logger.info("🔄 Testing profit monitoring...")
        
        # Simulate price increase to ₹1008 (₹800 profit)
        # This should trigger SELL when FF pattern confirmed
        
        # Check if profit monitoring method exists
        if hasattr(db2_executor, '_monitor_profit_targets_and_execute_sells'):
            logger.info("✅ Profit monitoring method exists")
            
            # Test the method
            db2_executor._monitor_profit_targets_and_execute_sells()
            logger.info("✅ Profit monitoring executed")
            
            return True
        else:
            logger.error("❌ Profit monitoring method missing!")
            return False
        
    except Exception as e:
        logger.error(f"❌ Error testing profit tracking: {e}")
        return False

def test_rr_pattern_detection():
    """Test RR pattern detection logic"""
    logger.info("🔍 TESTING RR PATTERN DETECTION")
    
    try:
        from rolling_window_manager import RollingWindowMonitor
        
        # Create a test monitor
        def test_callback(symbol, confirmation_price, confirmation_time, data_points):
            logger.info(f"🎯 CALLBACK TRIGGERED: {symbol} @ ₹{confirmation_price:.2f}")
        
        monitor = RollingWindowMonitor(
            symbol='TEST_RR',
            signal_type='BUY',
            base_price=1000.0,
            callback=test_callback,
            timeout_minutes=10
        )
        
        logger.info("📊 Testing RR pattern detection:")
        
        # Test data points that should trigger RR pattern
        test_prices = [1000.0, 1005.0, 1010.0]  # Rise-Rise pattern
        
        for i, price in enumerate(test_prices):
            logger.info(f"   Adding data point {i+1}: ₹{price:.2f}")
            confirmed = monitor.add_data_point(price, datetime.now())
            
            if confirmed:
                logger.info(f"✅ RR pattern confirmed at ₹{price:.2f}")
                return True
            else:
                logger.info(f"   ⏳ Waiting for more data points...")
        
        logger.error("❌ RR pattern not detected with Rise-Rise data")
        return False
        
    except Exception as e:
        logger.error(f"❌ Error testing RR pattern: {e}")
        return False

def test_db2_tables_structure():
    """Test DB2 tables for tracking"""
    logger.info("🔍 TESTING DB2 TABLES STRUCTURE")
    
    try:
        db2_executor = get_db2_trade_executor()
        conn = sqlite3.connect(db2_executor.db_path)
        cursor = conn.cursor()
        
        # Check required tables
        required_tables = [
            'db2_signals_received',
            'trading_positions', 
            'db2_trading_data',
            'db2_portfolio_summary'
        ]
        
        for table in required_tables:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            result = cursor.fetchone()
            
            if result:
                logger.info(f"✅ Table exists: {table}")
                
                # Show table structure
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                logger.info(f"   Columns: {[col[1] for col in columns]}")
            else:
                logger.error(f"❌ Missing table: {table}")
                return False
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing tables: {e}")
        return False

def main():
    """Main test function"""
    logger.info("🚀 TESTING DB2 CONFIRMATION MECHANISM")
    logger.info("=" * 60)
    
    tests = [
        ("DB2 Tables Structure", test_db2_tables_structure),
        ("BUY Confirmation Mechanism", test_buy_confirmation_mechanism),
        ("₹800 Profit Tracking", test_profit_tracking_mechanism),
        ("RR Pattern Detection", test_rr_pattern_detection)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 TEST: {test_name}")
        logger.info("-" * 40)
        
        try:
            if test_func():
                logger.info(f"✅ PASSED: {test_name}")
                passed_tests += 1
            else:
                logger.error(f"❌ FAILED: {test_name}")
        except Exception as e:
            logger.error(f"❌ ERROR in {test_name}: {e}")
    
    logger.info("\n" + "=" * 60)
    logger.info(f"📊 TEST RESULTS: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        logger.info("🎉 ALL CONFIRMATION TESTS PASSED!")
    else:
        logger.error("❌ CRITICAL ISSUES FOUND IN CONFIRMATION MECHANISM!")
        logger.error("🚨 DB2 may be executing trades without proper confirmation!")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
