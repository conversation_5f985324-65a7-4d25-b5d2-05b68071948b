#!/usr/bin/env python3
"""
Fresh Restart System - Complete Clean Setup
1. Clean DB1 and DB2 completely
2. Fetch fresh data with F/R calculations
3. Detect patterns and generate BUY signals
4. Transfer signals from DB1 to DB2
5. Ensure complete end-to-end flow works
"""

import sqlite3
import logging
import os
from typing import Dict
from missing_data_fetcher import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from fix_existing_fr_data import ExistingDataFixer

class FreshRestartSystem:
    def __init__(self):
        self.DB1_PATH = 'Data/trading_data.db'
        self.DB2_PATH = 'Data/trading_operations.db'
        self.logger = self._setup_logging()
        
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)
    
    def clean_all_databases(self) -> bool:
        """Step 1: Clean DB1 and DB2 completely"""
        try:
            self.logger.info("🧹 STEP 1: CLEANING ALL DATABASES")
            
            # Clean DB1 (trading_data.db)
            if os.path.exists(self.DB1_PATH):
                conn1 = sqlite3.connect(self.DB1_PATH, timeout=30.0)
                cursor1 = conn1.cursor()
                
                # Drop all tables (except sqlite_sequence which is system table)
                cursor1.execute("SELECT name FROM sqlite_master WHERE type='table' AND name != 'sqlite_sequence'")
                tables = cursor1.fetchall()

                for table in tables:
                    table_name = table[0]
                    cursor1.execute(f"DROP TABLE IF EXISTS {table_name}")
                    self.logger.info(f"🗑️ Dropped DB1 table: {table_name}")
                
                conn1.commit()
                conn1.close()
                self.logger.info("✅ DB1 cleaned completely")
            
            # Clean DB2 (trading_operations.db)
            if os.path.exists(self.DB2_PATH):
                conn2 = sqlite3.connect(self.DB2_PATH, timeout=30.0)
                cursor2 = conn2.cursor()
                
                # Drop all tables (except sqlite_sequence which is system table)
                cursor2.execute("SELECT name FROM sqlite_master WHERE type='table' AND name != 'sqlite_sequence'")
                tables = cursor2.fetchall()

                for table in tables:
                    table_name = table[0]
                    cursor2.execute(f"DROP TABLE IF EXISTS {table_name}")
                    self.logger.info(f"🗑️ Dropped DB2 table: {table_name}")
                
                conn2.commit()
                conn2.close()
                self.logger.info("✅ DB2 cleaned completely")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error cleaning databases: {e}")
            return False
    
    def create_fresh_db_schemas(self) -> bool:
        """Step 2: Create fresh DB1 and DB2 schemas"""
        try:
            self.logger.info("🏗️ STEP 2: CREATING FRESH DATABASE SCHEMAS")
            
            # Create DB1 schema
            conn1 = sqlite3.connect(self.DB1_PATH, timeout=30.0)
            cursor1 = conn1.cursor()
            
            # Trading data table
            cursor1.execute('''
            CREATE TABLE IF NOT EXISTS trading_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                token TEXT,
                exchange TEXT,
                timestamp DATETIME NOT NULL,
                open_price REAL,
                high_price REAL,
                low_price REAL,
                close_price REAL,
                volume INTEGER,
                fr_movement TEXT,
                previous_close REAL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, timestamp)
            )
            ''')
            
            # BUY signals table
            cursor1.execute('''
            CREATE TABLE IF NOT EXISTS signal_buy (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                signal_price REAL NOT NULL,
                signal_time DATETIME NOT NULL,
                pattern TEXT NOT NULL,
                priority TEXT NOT NULL,
                status TEXT DEFAULT 'ACTIVE',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, signal_time)
            )
            ''')
            
            conn1.commit()
            conn1.close()
            self.logger.info("✅ DB1 schema created")
            
            # Create DB2 schema
            conn2 = sqlite3.connect(self.DB2_PATH, timeout=30.0)
            cursor2 = conn2.cursor()
            
            # DB2 signals received from DB1
            cursor2.execute('''
            CREATE TABLE IF NOT EXISTS db2_signals_received (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                signal_type TEXT NOT NULL,
                signal_price REAL NOT NULL,
                signal_time DATETIME NOT NULL,
                pattern TEXT NOT NULL,
                priority TEXT NOT NULL,
                confirmation_status TEXT DEFAULT 'PENDING',
                received_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, signal_time)
            )
            ''')
            
            # Trading positions
            cursor2.execute('''
            CREATE TABLE IF NOT EXISTS trading_positions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                buy_price REAL NOT NULL,
                sell_price REAL,
                buy_quantity INTEGER NOT NULL,
                buy_investment REAL NOT NULL,
                profit_amount REAL DEFAULT 0,
                status TEXT DEFAULT 'ACTIVE',
                buy_time DATETIME NOT NULL,
                sell_time DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            conn2.commit()
            conn2.close()
            self.logger.info("✅ DB2 schema created")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error creating schemas: {e}")
            return False
    
    def fetch_fresh_data_with_patterns(self) -> Dict:
        """Step 3: Fetch fresh data and detect patterns"""
        try:
            self.logger.info("📊 STEP 3: FETCHING FRESH DATA WITH PATTERN DETECTION")
            
            # Use the missing data fetcher to get fresh data
            fetcher = MissingDataFetcher()
            result = fetcher.fetch_missing_data_for_all_symbols()
            
            self.logger.info(f"📊 Data fetch result: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Error fetching fresh data: {e}")
            return {'success': 0, 'failed': 0, 'total': 0, 'error': str(e)}
    
    def transfer_signals_to_db2(self) -> Dict:
        """Step 4: Transfer BUY signals from DB1 to DB2"""
        try:
            self.logger.info("🔄 STEP 4: TRANSFERRING SIGNALS FROM DB1 TO DB2")
            
            # Get BUY signals from DB1
            conn1 = sqlite3.connect(self.DB1_PATH, timeout=30.0)
            cursor1 = conn1.cursor()
            
            cursor1.execute('''
            SELECT symbol, signal_price, signal_time, pattern, priority
            FROM signal_buy 
            WHERE status = 'ACTIVE'
            ORDER BY created_at DESC
            ''')
            
            signals = cursor1.fetchall()
            conn1.close()
            
            if not signals:
                self.logger.info("📊 No BUY signals found in DB1")
                return {'transferred': 0, 'total': 0}
            
            # Transfer signals to DB2
            conn2 = sqlite3.connect(self.DB2_PATH, timeout=30.0)
            cursor2 = conn2.cursor()
            
            transferred_count = 0
            
            for signal in signals:
                symbol, signal_price, signal_time, pattern, priority = signal
                
                try:
                    cursor2.execute('''
                    INSERT OR REPLACE INTO db2_signals_received 
                    (symbol, signal_type, signal_price, signal_time, pattern, priority)
                    VALUES (?, ?, ?, ?, ?, ?)
                    ''', (symbol, 'BUY', signal_price, signal_time, pattern, priority))
                    
                    transferred_count += 1
                    self.logger.info(f"🎯 Transferred: {symbol} {pattern} {priority} at ₹{signal_price:.2f}")
                    
                except Exception as e:
                    self.logger.error(f"❌ Error transferring {symbol}: {e}")
                    continue
            
            conn2.commit()
            conn2.close()
            
            self.logger.info(f"✅ Transferred {transferred_count}/{len(signals)} signals to DB2")
            
            return {'transferred': transferred_count, 'total': len(signals)}
            
        except Exception as e:
            self.logger.error(f"❌ Error transferring signals: {e}")
            return {'transferred': 0, 'total': 0, 'error': str(e)}
    
    def verify_complete_flow(self) -> Dict:
        """Step 5: Verify complete DB1→DB2 flow"""
        try:
            self.logger.info("✅ STEP 5: VERIFYING COMPLETE FLOW")
            
            # Check DB1 statistics
            conn1 = sqlite3.connect(self.DB1_PATH, timeout=30.0)
            cursor1 = conn1.cursor()
            
            cursor1.execute('SELECT COUNT(*) FROM trading_data')
            db1_records = cursor1.fetchone()[0]
            
            cursor1.execute('SELECT COUNT(DISTINCT symbol) FROM trading_data')
            db1_symbols = cursor1.fetchone()[0]
            
            cursor1.execute('SELECT COUNT(*) FROM signal_buy')
            db1_signals = cursor1.fetchone()[0]
            
            cursor1.execute('''
            SELECT priority, COUNT(*) 
            FROM signal_buy 
            GROUP BY priority
            ''')
            priority_counts = dict(cursor1.fetchall())
            
            conn1.close()
            
            # Check DB2 statistics
            conn2 = sqlite3.connect(self.DB2_PATH, timeout=30.0)
            cursor2 = conn2.cursor()
            
            cursor2.execute('SELECT COUNT(*) FROM db2_signals_received')
            db2_signals = cursor2.fetchone()[0]
            
            cursor2.execute('SELECT COUNT(*) FROM trading_positions')
            db2_positions = cursor2.fetchone()[0]
            
            conn2.close()
            
            verification_result = {
                'db1_records': db1_records,
                'db1_symbols': db1_symbols,
                'db1_signals': db1_signals,
                'db2_signals': db2_signals,
                'db2_positions': db2_positions,
                'priority_counts': priority_counts,
                'flow_working': db1_signals > 0 and db2_signals > 0
            }
            
            self.logger.info(f"📊 VERIFICATION RESULTS:")
            self.logger.info(f"   DB1: {db1_records} records, {db1_symbols} symbols, {db1_signals} signals")
            self.logger.info(f"   DB2: {db2_signals} signals, {db2_positions} positions")
            self.logger.info(f"   Priority distribution: {priority_counts}")
            self.logger.info(f"   Flow working: {verification_result['flow_working']}")
            
            return verification_result
            
        except Exception as e:
            self.logger.error(f"❌ Error verifying flow: {e}")
            return {'error': str(e)}
    
    def fresh_restart_complete_system(self) -> Dict:
        """Main method: Complete fresh restart"""
        try:
            self.logger.info("🚀 STARTING FRESH RESTART OF COMPLETE SYSTEM")
            self.logger.info("=" * 80)
            
            # Step 1: Clean databases
            if not self.clean_all_databases():
                return {'success': False, 'error': 'Failed to clean databases'}
            
            # Step 2: Create fresh schemas
            if not self.create_fresh_db_schemas():
                return {'success': False, 'error': 'Failed to create schemas'}
            
            # Step 3: Fetch fresh data with patterns
            fetch_result = self.fetch_fresh_data_with_patterns()
            
            # Step 4: Transfer signals to DB2
            transfer_result = self.transfer_signals_to_db2()
            
            # Step 5: Verify complete flow
            verification = self.verify_complete_flow()
            
            self.logger.info("🎉 FRESH RESTART COMPLETED")
            self.logger.info("=" * 80)
            
            return {
                'success': True,
                'fetch_result': fetch_result,
                'transfer_result': transfer_result,
                'verification': verification,
                'message': 'Fresh restart completed successfully'
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error in fresh restart: {e}")
            return {'success': False, 'error': str(e)}

# Main execution
if __name__ == "__main__":
    restart_system = FreshRestartSystem()
    result = restart_system.fresh_restart_complete_system()
    print(f"📊 Final Result: {result}")
