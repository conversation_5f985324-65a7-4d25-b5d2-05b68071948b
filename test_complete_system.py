#!/usr/bin/env python3
"""
Complete System Test - End-to-End Validation

This script tests the complete system:
1. DB1 generates 4F+1R signals
2. DB2 receives and tracks signals
3. DB2 waits for RR confirmation
4. DB2 executes BUY with ₹100,000
5. DB2 monitors ₹800 profit target
6. DB2 executes SELL with FF confirmation
7. Frontend displays all data correctly
"""

import logging
import sqlite3
import time
from datetime import datetime
from db1_signal_generator import get_db1_signal_generator
from db2_trade_executor import get_db2_trade_executor
from db1_db2_communicator import TradingSignal, get_communicator

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_complete_trading_flow():
    """Test complete trading flow from DB1 to DB2"""
    logger.info("🚀 TESTING COMPLETE TRADING FLOW")
    logger.info("=" * 60)
    
    try:
        # Initialize components
        communicator = get_communicator()
        db2_executor = get_db2_trade_executor()
        
        # Clear existing data
        conn = sqlite3.connect(db2_executor.db_path)
        cursor = conn.cursor()
        cursor.execute("DELETE FROM db2_signals_received WHERE symbol LIKE 'TEST_%'")
        cursor.execute("DELETE FROM trading_positions WHERE symbol LIKE 'TEST_%'")
        conn.commit()
        conn.close()
        
        logger.info("✅ Cleared test data")
        
        # Step 1: Simulate DB1 generating 4F+1R signal
        logger.info("\n📊 STEP 1: DB1 Generates 4F+1R Signal")
        
        test_signal = TradingSignal(
            symbol='TEST_COMPLETE',
            signal_type='BUY',
            price=500.0,  # ₹500 per share
            timestamp_ns=time.time_ns(),
            pattern_info={
                'pattern_type': '4F+1R',
                'detection_method': 'complete_test',
                'drop_percentage': 2.0
            },
            source='DB1_COMPLETE_TEST'
        )
        
        logger.info(f"📤 DB1 Signal: BUY {test_signal.symbol} @ ₹{test_signal.price:.2f}")
        logger.info(f"   📊 Expected Investment: ₹{100000:,}")
        logger.info(f"   📈 Expected Shares: {int(100000 / test_signal.price):,} shares")
        
        # Step 2: Send signal to DB2
        logger.info("\n📥 STEP 2: DB1→DB2 Signal Transmission")
        
        success = communicator.send_signal_to_db2(test_signal)
        if not success:
            logger.error("❌ Failed to send signal to DB2")
            return False
        
        logger.info("✅ Signal sent to DB2 successfully")
        
        # Step 3: DB2 processes signal
        logger.info("\n🔄 STEP 3: DB2 Signal Processing")
        
        db2_executor.run_periodic_check()
        
        # Check signal tracking
        conn = sqlite3.connect(db2_executor.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT symbol, status, signal_price FROM db2_signals_received WHERE symbol = 'TEST_COMPLETE'")
        signal_result = cursor.fetchone()
        
        if signal_result:
            symbol, status, signal_price = signal_result
            logger.info(f"✅ Signal tracked in DB2: {symbol} - {status}")
            logger.info(f"   💰 Signal Price: ₹{signal_price:.2f}")
            
            if status == 'EXECUTED':
                logger.error("❌ CRITICAL: Signal executed without proper RR confirmation!")
                return False
            elif status in ['RECEIVED', 'CONFIRMING']:
                logger.info("✅ GOOD: Signal waiting for RR confirmation")
            else:
                logger.warning(f"⚠️ Unexpected status: {status}")
        else:
            logger.error("❌ Signal not found in DB2 tracking table")
            return False
        
        # Step 4: Check profit monitoring capability
        logger.info("\n💰 STEP 4: Profit Monitoring Test")
        
        # Create a test position to verify profit monitoring
        cursor.execute('''
        INSERT INTO trading_positions
        (symbol, buy_price, shares_quantity, investment, target_price, target_value, current_profit, status, buy_time)
        VALUES ('TEST_PROFIT', 1000.0, 100, 100000.0, 1008.0, 100800.0, 500.0, 'ACTIVE', ?)
        ''', (datetime.now().isoformat(),))
        conn.commit()
        
        # Test profit monitoring
        db2_executor._monitor_profit_targets_and_execute_sells()
        
        logger.info("✅ Profit monitoring system functional")
        
        # Step 5: Check portfolio summary
        logger.info("\n📊 STEP 5: Portfolio Summary Test")
        
        db2_executor._update_portfolio_summary()
        portfolio = db2_executor.get_portfolio_summary()
        
        logger.info(f"✅ Portfolio Summary:")
        logger.info(f"   💰 Total Investment: ₹{portfolio.get('total_investment', 0):,.2f}")
        logger.info(f"   📈 Current Value: ₹{portfolio.get('total_current_value', 0):,.2f}")
        logger.info(f"   💵 Current Profit: ₹{portfolio.get('total_profit', 0):,.2f}")
        logger.info(f"   📊 Active Positions: {portfolio.get('active_positions', 0)}")
        
        # Cleanup test data
        cursor.execute("DELETE FROM trading_positions WHERE symbol LIKE 'TEST_%'")
        conn.commit()
        conn.close()
        
        logger.info("\n🎉 COMPLETE TRADING FLOW TEST PASSED!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error in complete trading flow test: {e}")
        return False

def test_investment_calculation():
    """Test ₹100,000 investment calculation"""
    logger.info("\n💰 TESTING ₹100,000 INVESTMENT CALCULATION")
    
    test_cases = [
        {'price': 100.0, 'expected_shares': 1000, 'expected_investment': 100000.0},
        {'price': 250.0, 'expected_shares': 400, 'expected_investment': 100000.0},
        {'price': 500.0, 'expected_shares': 200, 'expected_investment': 100000.0},
        {'price': 1000.0, 'expected_shares': 100, 'expected_investment': 100000.0},
        {'price': 2000.0, 'expected_shares': 50, 'expected_investment': 100000.0}
    ]
    
    for case in test_cases:
        price = case['price']
        expected_shares = case['expected_shares']
        expected_investment = case['expected_investment']
        
        calculated_shares = int(100000 / price)
        actual_investment = calculated_shares * price
        
        logger.info(f"📊 ₹{price:.2f}/share:")
        logger.info(f"   📈 Shares: {calculated_shares:,} (expected: {expected_shares:,})")
        logger.info(f"   💰 Investment: ₹{actual_investment:,.2f}")
        
        if calculated_shares == expected_shares:
            logger.info(f"   ✅ Calculation correct")
        else:
            logger.error(f"   ❌ Calculation wrong: got {calculated_shares}, expected {expected_shares}")
            return False
    
    return True

def test_profit_target_calculation():
    """Test ₹800 profit target calculation"""
    logger.info("\n🎯 TESTING ₹800 PROFIT TARGET")
    
    test_cases = [
        {'shares': 1000, 'buy_price': 100.0, 'target_price': 100.8},
        {'shares': 400, 'buy_price': 250.0, 'target_price': 252.0},
        {'shares': 200, 'buy_price': 500.0, 'target_price': 504.0},
        {'shares': 100, 'buy_price': 1000.0, 'target_price': 1008.0},
        {'shares': 50, 'buy_price': 2000.0, 'target_price': 2016.0}
    ]
    
    for case in test_cases:
        shares = case['shares']
        buy_price = case['buy_price']
        expected_target = case['target_price']
        
        investment = shares * buy_price
        target_value = investment + 800  # ₹800 profit
        calculated_target = target_value / shares
        
        logger.info(f"📊 {shares:,} shares @ ₹{buy_price:.2f}:")
        logger.info(f"   💰 Investment: ₹{investment:,.2f}")
        logger.info(f"   🎯 Target Value: ₹{target_value:,.2f} (₹800 profit)")
        logger.info(f"   📈 Target Price: ₹{calculated_target:.2f} (expected: ₹{expected_target:.2f})")
        
        if abs(calculated_target - expected_target) < 0.01:
            logger.info(f"   ✅ Target calculation correct")
        else:
            logger.error(f"   ❌ Target calculation wrong")
            return False
    
    return True

def test_frontend_api_endpoints():
    """Test frontend API endpoints"""
    logger.info("\n🌐 TESTING FRONTEND API ENDPOINTS")
    
    try:
        import requests
        
        # Test DB2 signals API
        try:
            response = requests.get('http://localhost:5000/api/db2-signals', timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    logger.info("✅ DB2 signals API working")
                    logger.info(f"   📊 Total Signals: {data.get('total_signals', 0)}")
                    logger.info(f"   📊 Active Positions: {data.get('active_positions', 0)}")
                else:
                    logger.warning("⚠️ DB2 signals API returned error")
            else:
                logger.warning(f"⚠️ DB2 signals API returned status {response.status_code}")
        except requests.exceptions.RequestException:
            logger.info("ℹ️ Flask server not running - API test skipped")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing frontend APIs: {e}")
        return False

def main():
    """Main test function"""
    logger.info("🚀 COMPLETE SYSTEM VALIDATION")
    logger.info("=" * 80)
    
    tests = [
        ("₹100,000 Investment Calculation", test_investment_calculation),
        ("₹800 Profit Target Calculation", test_profit_target_calculation),
        ("Complete Trading Flow", test_complete_trading_flow),
        ("Frontend API Endpoints", test_frontend_api_endpoints)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 TEST: {test_name}")
        logger.info("-" * 50)
        
        try:
            if test_func():
                logger.info(f"✅ PASSED: {test_name}")
                passed_tests += 1
            else:
                logger.error(f"❌ FAILED: {test_name}")
        except Exception as e:
            logger.error(f"❌ ERROR in {test_name}: {e}")
    
    logger.info("\n" + "=" * 80)
    logger.info(f"📊 FINAL RESULTS: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        logger.info("🎉 ALL SYSTEM TESTS PASSED!")
        logger.info("✅ Complete trading system is functional")
        logger.info("✅ DB1→DB2 signal flow working")
        logger.info("✅ ₹100,000 investment calculation correct")
        logger.info("✅ ₹800 profit target system ready")
        logger.info("✅ Frontend integration ready")
        logger.info("🚀 SYSTEM READY FOR PRODUCTION!")
        return True
    else:
        logger.error("❌ SOME SYSTEM TESTS FAILED!")
        logger.error("🚨 System needs fixes before production")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
