#!/usr/bin/env python3
"""
DB1 Signal Transmitter - PURE SQL OPERATIONS

This module transmits BUY signals from DB1 to DB2.
NO API CALLS - Only SQL operations between databases.
FOLLOWS EXACT DOCUMENTATION SPECIFICATIONS.
"""

import sqlite3
import logging
from typing import List, Dict, Optional
from datetime import datetime
from perfect_db1_signal_generator import perfect_db1_signal_generator

class DB1SignalTransmitter:
    """Pure SQL signal transmitter from DB1 to DB2"""
    
    def __init__(self):
        self.db1_path = 'Data/trading_data.db'
        self.db2_path = 'Data/trading_operations.db'
        self.logger = logging.getLogger(__name__)
        
    def transmit_signals_to_db2(self) -> int:
        """Transmit all untransmitted signals from DB1 to DB2 - PURE SQL"""
        try:
            # Get untransmitted signals from DB1
            untransmitted_signals = perfect_db1_signal_generator.get_untransmitted_signals()
            
            if not untransmitted_signals:
                return 0
            
            transmitted_count = 0
            
            for signal in untransmitted_signals:
                # Check if signal already exists in DB2
                if not self._signal_exists_in_db2(signal):
                    success = self._send_signal_to_db2(signal)
                    if success:
                        transmitted_count += 1
                        
                        self.logger.info(f"📡 SIGNAL TRANSMITTED: {signal['symbol']} @ ₹{signal['signal_price']:.2f}")
                        self.logger.info(f"   DB1 Signal ID: {signal['id']}")
                        self.logger.info(f"   Pattern: {signal['pattern_sequence']}")
            
            if transmitted_count > 0:
                self.logger.info(f"📤 TRANSMITTED {transmitted_count} SIGNALS TO DB2")
            
            return transmitted_count
            
        except Exception as e:
            self.logger.error(f"❌ Error transmitting signals to DB2: {e}")
            return 0
    
    def _signal_exists_in_db2(self, signal: Dict) -> bool:
        """Check if signal already exists in DB2 - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db2_path, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT id FROM db2_signals_received 
            WHERE symbol = ? AND signal_price = ? AND db1_signal_id = ?
            ''', (signal['symbol'], signal['signal_price'], signal['id']))
            
            result = cursor.fetchone()
            conn.close()
            
            return result is not None
            
        except Exception as e:
            self.logger.error(f"❌ Error checking signal existence in DB2: {e}")
            return False
    
    def _send_signal_to_db2(self, signal: Dict) -> bool:
        """Send individual signal to DB2 - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db2_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Insert signal into DB2 signals received table
            cursor.execute('''
            INSERT INTO db2_signals_received 
            (symbol, signal_type, signal_price, status, received_time, db1_signal_id)
            VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                signal['symbol'],
                signal['signal_type'],
                signal['signal_price'],
                'PENDING',  # Waiting for Layer 2 confirmation
                datetime.now(),
                signal['id']  # DB1 signal ID
            ))
            
            conn.commit()
            conn.close()
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error sending signal to DB2 for {signal['symbol']}: {e}")
            return False
    
    def get_transmission_statistics(self) -> Dict:
        """Get signal transmission statistics - PURE SQL"""
        try:
            # DB1 statistics
            conn1 = sqlite3.connect(self.db1_path, timeout=30.0)
            cursor1 = conn1.cursor()
            
            cursor1.execute('SELECT COUNT(*) FROM trading_signals WHERE signal_type = "BUY"')
            db1_total_signals = cursor1.fetchone()[0]
            
            conn1.close()
            
            # DB2 statistics
            conn2 = sqlite3.connect(self.db2_path, timeout=30.0)
            cursor2 = conn2.cursor()
            
            cursor2.execute('SELECT COUNT(*) FROM db2_signals_received')
            db2_received = cursor2.fetchone()[0]
            
            cursor2.execute('SELECT COUNT(*) FROM db2_signals_received WHERE status = "PENDING"')
            db2_pending_confirmation = cursor2.fetchone()[0]
            
            cursor2.execute('SELECT COUNT(*) FROM db2_signals_received WHERE status = "CONFIRMED"')
            db2_confirmed = cursor2.fetchone()[0]
            
            conn2.close()
            
            return {
                'db1_total_signals': db1_total_signals,
                'db2_received': db2_received,
                'db2_pending_confirmation': db2_pending_confirmation,
                'db2_confirmed': db2_confirmed,
                'transmission_rate': (db2_received / max(1, db1_total_signals)) * 100
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error getting transmission statistics: {e}")
            return {}
    
    def verify_signal_integrity(self) -> bool:
        """Verify that all signals are properly transmitted - PURE SQL"""
        try:
            # Get signals from DB1
            conn1 = sqlite3.connect(self.db1_path, timeout=30.0)
            cursor1 = conn1.cursor()
            
            cursor1.execute('''
            SELECT id, symbol, price, pattern_sequence 
            FROM trading_signals 
            WHERE signal_type = "BUY"
            ''')
            
            db1_signals = cursor1.fetchall()
            conn1.close()
            
            # Check if they exist in DB2
            conn2 = sqlite3.connect(self.db2_path, timeout=30.0)
            cursor2 = conn2.cursor()
            
            missing_signals = []
            
            for signal in db1_signals:
                db1_id, symbol, price, pattern = signal
                
                cursor2.execute('''
                SELECT id FROM db2_signals_received 
                WHERE db1_signal_id = ? AND symbol = ? AND signal_price = ?
                ''', (db1_id, symbol, price))
                
                if not cursor2.fetchone():
                    missing_signals.append(signal)
            
            conn2.close()
            
            if missing_signals:
                self.logger.error(f"❌ SIGNAL INTEGRITY ISSUE: {len(missing_signals)} signals missing in DB2")
                for signal in missing_signals:
                    self.logger.error(f"   Missing: {signal[1]} @ ₹{signal[2]:.2f}")
                return False
            else:
                self.logger.info("✅ SIGNAL INTEGRITY VERIFIED: All signals found in DB2")
                return True
                
        except Exception as e:
            self.logger.error(f"❌ Error verifying signal integrity: {e}")
            return False

# Global instance
db1_signal_transmitter = DB1SignalTransmitter()
