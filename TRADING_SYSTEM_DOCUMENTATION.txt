================================================================================
                    AUTOMATED TRADING SYSTEM - COMPLETE DOCUMENTATION
================================================================================

OVERVIEW:
---------
This is a complete automated trading system with two independent databases:
- DB1: 15-minute data analysis and 4F+1R signal generation
- DB2: 2-minute data confirmation and trade execution
- Frontend: Real-time dashboard for monitoring

SYSTEM ARCHITECTURE:
-------------------
DB1 (Signal Generation) ←→ DB2 (Trade Execution) → Frontend Dashboard
     ↓                        ↓                      ↓
15-min intervals         2-min confirmations    Real-time display
4F+1R patterns          RR/FF patterns         Portfolio tracking

================================================================================
                                CORE FILES
================================================================================

1. MAIN EXECUTION FILES:
------------------------

realtime_data_fetcher.py
- PURPOSE: Main coordinator that runs every 15 minutes
- TIMING: Hardcoded 9:15, 9:30, 9:45... till 15:15 (Monday-Friday)
- FUNCTIONS:
  * Fetches 15-minute data for all symbols (GOLD → SILVER → BRONZE priority)
  * Triggers DB1 signal generation
  * Triggers DB2 periodic checks
  * Manages market hours and weekend exclusions
- AUTO-TRIGGER: Yes, every 15 minutes during market hours

flask_app.py
- PURPOSE: Web dashboard for monitoring and display
- FUNCTIONS:
  * Serves HTML dashboard
  * Provides APIs for real-time data display
  * Shows DB1 signals, DB2 trades, portfolio status
- EXECUTION: Run manually: python flask_app.py

2. DB1 FILES (15-minute Signal Generation):
-------------------------------------------

db1_signal_generator.py
- PURPOSE: Detects 4F+1R patterns and generates BUY signals
- BASE POINT: Each 15-min close vs previous 15-min close
- PATTERN: 4 consecutive FALL + 1 RISE (5 data points = 4 comparisons)
- VALIDATION: 0.5% minimum drop across 4 FALL movements
- DATABASE: trading_data.db (trading_data, trading_signals tables)
- AUTO-TRIGGER: Yes, called by realtime_data_fetcher.py every 15 minutes

db1_db2_communicator.py
- PURPOSE: Handles signal communication between DB1 and DB2
- FUNCTIONS:
  * Sends BUY signals from DB1 to DB2
  * One-way communication (DB1 → DB2 only)
  * Queue-based signal transmission
- EXECUTION: Automatic when signals are generated

3. DB2 FILES (2-minute Trade Execution):
----------------------------------------

db2_trade_executor.py
- PURPOSE: Main DB2 engine for trade execution and profit monitoring
- FUNCTIONS:
  * Receives BUY signals from DB1
  * Executes trades with ₹100,000 investment per symbol
  * Monitors ₹800 profit target every 15 minutes
  * Executes SELL when ₹800 + FF pattern confirmed
- BASE POINTS:
  * BUY confirmation: DB1 signal price for first 2-min comparison
  * SELL confirmation: ₹800 confirmation price for FF pattern
- DATABASE: trading_operations.db (multiple tables)
- AUTO-TRIGGER: Yes, called by realtime_data_fetcher.py every 15 minutes

rolling_window_manager.py
- PURPOSE: Manages 2-minute data threads for RR/FF pattern confirmation
- FUNCTIONS:
  * Starts separate 2-minute threads for each symbol
  * Confirms RR pattern (Rise-Rise) for BUY execution
  * Confirms FF pattern (Fall-Fall) for SELL execution
  * Independent threads run every 2 minutes until pattern confirmed
- AUTO-TRIGGER: Yes, started when signals received or ₹800 reached

4. SUPPORTING FILES:
-------------------

symbol_manager.py
- PURPOSE: Manages symbol lists and API tokens
- FUNCTIONS:
  * Loads symbols from angelone_tokens.txt
  * Categorizes symbols by priority (GOLD, SILVER, BRONZE)
  * Provides symbol-token mapping for API calls

config.py
- PURPOSE: Configuration settings
- CONTAINS:
  * API credentials and endpoints
  * Database paths
  * Trading parameters (investment amounts, profit targets)

data_integrity_checker.py
- PURPOSE: Database validation and integrity checks
- FUNCTIONS:
  * Validates data consistency between DB1 and DB2
  * Checks for missing or corrupted data
  * Provides data quality reports

database_setup.py
- PURPOSE: Initial database schema creation
- FUNCTIONS:
  * Creates all required tables in both databases
  * Sets up proper indexes and constraints
  * Initializes default values

================================================================================
                            COMPLETE EXECUTION FLOW
================================================================================

STARTUP SEQUENCE:
-----------------
1. Run: python realtime_data_fetcher.py (Main system)
2. Run: python flask_app.py (Dashboard - separate terminal)
3. System automatically starts 15-minute cycles

EVERY 15-MINUTE CYCLE (9:15, 9:30, 9:45... 15:15):
--------------------------------------------------

STEP 1: DATA FETCHING (realtime_data_fetcher.py)
├── Check if trading hours (9:15-15:15, Monday-Friday)
├── Fetch 15-minute data for all symbols
│   ├── GOLD symbols first (highest priority)
│   ├── SILVER symbols second
│   └── BRONZE symbols last
├── Store data in DB1 with F/R calculation
│   └── F/R = current_close vs previous_close
└── Trigger DB1 and DB2 processing

STEP 2: DB1 SIGNAL GENERATION (db1_signal_generator.py)
├── Analyze last 5 data points for each symbol
├── Calculate 4F+1R pattern:
│   ├── Point 1 vs Point 2 = F (Fall)
│   ├── Point 2 vs Point 3 = F (Fall)
│   ├── Point 3 vs Point 4 = F (Fall)
│   └── Point 4 vs Point 5 = R (Rise)
├── Validate 0.5% minimum drop across 4 FALL movements
├── Generate BUY signal if pattern + drop confirmed
└── Send signal to DB2 via communicator

STEP 3: DB2 SIGNAL PROCESSING (db2_trade_executor.py)
├── Receive BUY signals from DB1
├── Start 2-minute RR confirmation (rolling_window_manager.py)
│   ├── Fetch 2-minute data independently
│   ├── First 2-min: Compare with DB1 signal price
│   ├── Subsequent 2-min: Compare with previous 2-min price
│   └── Execute BUY when R-R pattern confirmed
├── Monitor existing positions for ₹800 profit
│   ├── Fetch latest 2-minute price for each position
│   ├── Calculate: (current_price - buy_price) × shares
│   ├── If profit ≥ ₹800: Set ₹800 base point
│   └── Start FF confirmation from ₹800 base point
└── Execute SELL when FF pattern confirmed

CONTINUOUS 2-MINUTE THREADS:
----------------------------
Each symbol with active confirmation gets independent 2-minute thread:
├── Symbol A: RR confirmation thread (every 2 minutes)
├── Symbol B: FF confirmation thread (every 2 minutes)
├── Symbol C: RR confirmation thread (every 2 minutes)
└── All threads run independently until pattern confirmed or timeout

================================================================================
                              DATABASE STRUCTURE
================================================================================

DB1 DATABASE (trading_data.db):
-------------------------------
trading_data:
- symbol, timestamp, close_price, fr_movement, previous_close
- Stores 15-minute data with F/R calculations

trading_signals:
- symbol, signal_type, price, pattern_sequence, created_at
- Stores generated 4F+1R BUY signals

DB2 DATABASE (trading_operations.db):
------------------------------------
db2_signals_received:
- symbol, signal_type, signal_price, status, received_time
- Tracks signals received from DB1

db2_trading_data:
- symbol, close_price, timestamp, fr_movement, previous_close, is_800_base_point
- Stores 2-minute data with F/R calculations

trading_positions:
- symbol, buy_price, shares_quantity, investment, current_profit, status
- profit_800_base_point, profit_800_timestamp
- Tracks all active and completed positions

db2_portfolio_summary:
- total_investment, total_current_value, total_profit, active_positions
- Portfolio summary for dashboard display

================================================================================
                                BASE POINTS
================================================================================

DB1 BASE POINTS (15-minute):
----------------------------
- F/R Calculation: Each 15-min close vs previous 15-min close
- Pattern Detection: Last 5 data points for 4F+1R
- Drop Validation: 0.5% minimum from highest to lowest in 4 FALL movements

DB2 BASE POINTS (2-minute):
---------------------------
BUY Confirmation:
- First 2-min data: Compare with DB1 signal price (base point)
- Subsequent 2-min: Compare with previous 2-min price
- RR Pattern: Two consecutive RISE movements

SELL Confirmation:
- Base Point: Exact price when ₹800 profit reached
- FF Pattern: Two consecutive FALL movements from ₹800 base point only
- SELL Execution: Only after FF confirmed from ₹800 base point

================================================================================
                            TRADING PARAMETERS
================================================================================

INVESTMENT: ₹100,000 per symbol
PROFIT TARGET: ₹800 per position
TRADING HOURS: 9:15 AM to 3:15 PM (Monday to Friday)
DATA INTERVALS: 15-minute (DB1), 2-minute (DB2)
PATTERN REQUIREMENTS: 4F+1R (DB1), RR/FF (DB2)
MINIMUM DROP: 0.5% across 4 FALL movements

================================================================================
                              DASHBOARD DISPLAY
================================================================================

FRONTEND TABS:
--------------
1. Active Signals (DB1): Shows generated 4F+1R signals
2. DB2 - Paper Trading Brain: Shows signal confirmations and 2-minute tracking
3. Paper Trading Records (DB2): Shows active and completed positions

REAL-TIME UPDATES:
-----------------
- Portfolio value and profit tracking
- Signal generation status
- Trade execution confirmations
- Pattern confirmation progress

================================================================================
                                EXECUTION GUIDE
================================================================================

TO START THE SYSTEM:
-------------------
1. Open Terminal 1: python realtime_data_fetcher.py
2. Open Terminal 2: python flask_app.py
3. Open browser: http://localhost:5000
4. System runs automatically every 15 minutes during market hours

TO MONITOR:
----------
- Check dashboard for real-time updates
- Monitor logs for detailed execution information
- Verify database tables for data integrity

SYSTEM STATUS:
-------------
- All files are production-ready
- Databases are clean (no test data)
- Auto-triggering mechanisms are active
- SQL-only architecture (no API dependencies in core logic)

================================================================================
                                    END
================================================================================
