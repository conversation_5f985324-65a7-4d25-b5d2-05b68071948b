#!/usr/bin/env python3
"""
DB2 ENGINE - COMPLETE DB2 SYSTEM IN ONE FILE

ROLE: Complete DB2 operations - 2-minute confirmations + Trade execution + Profit monitoring
FUNCTIONS:
- Receive BUY signals from DB1
- Store 2-minute data from API (PURE SQL)
- Detect RR patterns for BUY confirmation (PURE SQL) - FAST SQL QUERIES
- Execute trades with ₹100,000 investment ONLY AFTER RR confirmation
- Store 15-minute data after BUY execution for profit monitoring (PURE SQL)
- Monitor ₹800 profit target using 15-minute data (PURE SQL)
- Detect FF patterns for SELL confirmation (PURE SQL) - FAST SQL QUERIES
- Execute SELL ONLY AFTER ₹800 profit + FF confirmation
- NO DEPENDENCY on rolling_window_manager.py - DB2 handles its own 2-minute data

DATABASE: trading_operations.db
AUTO-TRIGGER: Called by realtime_data_fetcher.py every 15 minutes
"""

import sqlite3
import logging
from datetime import datetime
from typing import List, Dict, Optional

class DB2Engine:
    """Complete DB2 system - 2-minute confirmations + Trade execution + Profit monitoring"""

    def __init__(self):
        self.db_path = 'Data/trading_operations.db'
        self.logger = logging.getLogger(__name__)

        # Trading parameters
        self.investment_per_symbol = 100000.0  # ₹100,000 per symbol
        self.profit_target = 800.0  # ₹800 profit target
        
    def run_complete_db2_cycle(self) -> Dict:
        """MAIN FUNCTION: Complete DB2 cycle - Called by realtime_data_fetcher.py"""
        try:
            self.logger.info("🔄 RUNNING COMPLETE DB2 CYCLE")

            # Step 1: Receive BUY signals from DB1
            new_signals = self.receive_buy_signals_from_db1()

            # Step 2: Store 2-minute data for pending signals (for RR confirmation)
            self.store_2min_data_for_pending_signals()

            # Step 3: Check RR confirmations and execute BUY trades
            rr_confirmed = self.check_rr_confirmations_and_execute_buy()

            # Step 4: Store 15-minute data for active positions (for profit monitoring)
            self.store_15min_data_for_active_positions()

            # Step 5: Monitor ₹800 profit target
            profit_800_reached = self.monitor_800_profit_target()

            # Step 6: Check FF confirmations and execute SELL trades
            ff_confirmed = self.check_ff_confirmations_and_execute_sell()

            result = {
                'new_signals_received': len(new_signals),
                'rr_confirmations': rr_confirmed,
                'profit_800_reached': profit_800_reached,
                'ff_confirmations': ff_confirmed,
                'timestamp': datetime.now().isoformat()
            }

            self.logger.info(f"✅ DB2 CYCLE COMPLETE: {len(new_signals)} signals, {rr_confirmed} RR confirmed, {ff_confirmed} FF confirmed")
            return result

        except Exception as e:
            self.logger.error(f"❌ Error in DB2 cycle: {e}")
            return {'error': str(e)}
    
    def receive_buy_signals_from_db1(self) -> List[Dict]:
        """Receive BUY signals from DB1 and start RR confirmation - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Get new PENDING signals
            cursor.execute('''
            SELECT id, symbol, signal_type, signal_price, status, received_time, db1_signal_id
            FROM db2_signals_received 
            WHERE status = 'PENDING' AND signal_type = 'BUY'
            ORDER BY received_time ASC
            ''')
            
            signals = []
            for row in cursor.fetchall():
                signals.append({
                    'id': row[0],
                    'symbol': row[1],
                    'signal_type': row[2],
                    'signal_price': row[3],
                    'status': row[4],
                    'received_time': row[5],
                    'db1_signal_id': row[6]
                })
            
            conn.close()
            
            if signals:
                self.logger.info(f"📨 RECEIVED {len(signals)} BUY SIGNALS FROM DB1")
                
                # Start RR confirmation for each signal
                for signal in signals:
                    self.start_rr_confirmation_thread(signal)
            
            return signals
            
        except Exception as e:
            self.logger.error(f"❌ Error receiving BUY signals: {e}")
            return []
    
    def start_rr_confirmation_thread(self, signal: Dict) -> bool:
        """Start RR pattern confirmation thread"""
        try:
            # Start 2-minute thread for RR confirmation
            success = self.rolling_window_manager.start_monitor(
                symbol=signal['symbol'],
                signal_type='BUY',  # RR confirmation for BUY
                base_price=signal['signal_price'],
                callback=self.handle_rr_confirmation,
                timeout_minutes=10
            )
            
            if success:
                self.logger.info(f"🔄 STARTED RR CONFIRMATION: {signal['symbol']} @ ₹{signal['signal_price']:.2f}")
                return True
            else:
                self.logger.error(f"❌ Failed to start RR thread for {signal['symbol']}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error starting RR confirmation: {e}")
            return False
    
    def handle_rr_confirmation(self, symbol: str, confirmation_price: float, 
                              confirmation_time: datetime, data_points: List[Dict]) -> None:
        """Handle RR confirmation callback - Execute BUY trade"""
        try:
            self.logger.info(f"🎯 RR CONFIRMATION RECEIVED: {symbol} @ ₹{confirmation_price:.2f}")
            
            # Get the original signal
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT id, db1_signal_id, signal_price FROM db2_signals_received 
            WHERE symbol = ? AND status = 'PENDING'
            ORDER BY received_time DESC LIMIT 1
            ''', (symbol,))
            
            result = cursor.fetchone()
            if result:
                signal_id, db1_signal_id, original_signal_price = result
                
                # Execute BUY trade
                success = self.execute_buy_trade(symbol, confirmation_price, db1_signal_id)
                
                if success:
                    # Update signal status
                    cursor.execute('''
                    UPDATE db2_signals_received SET status = 'EXECUTED' 
                    WHERE id = ?
                    ''', (signal_id,))
                    
                    conn.commit()
                    self.logger.info(f"✅ BUY EXECUTED after RR confirmation: {symbol}")
                else:
                    self.logger.error(f"❌ Failed to execute BUY for {symbol}")
            else:
                self.logger.error(f"❌ No pending signal found for {symbol}")
            
            conn.close()
                
        except Exception as e:
            self.logger.error(f"❌ Error handling RR confirmation: {e}")
    
    def execute_buy_trade(self, symbol: str, buy_price: float, db1_signal_id: int) -> bool:
        """Execute BUY trade with ₹100,000 investment"""
        try:
            # Calculate shares
            shares_quantity = int(self.investment_per_symbol / buy_price)
            actual_investment = shares_quantity * buy_price
            
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Insert into trading_positions
            cursor.execute('''
            INSERT INTO trading_positions 
            (symbol, buy_price, shares_quantity, investment, current_profit, status, 
             profit_800_base_point, profit_800_timestamp, buy_timestamp, db1_signal_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                symbol, buy_price, shares_quantity, actual_investment, 0.0, 'ACTIVE',
                None, None, datetime.now(), db1_signal_id
            ))
            
            position_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            self.logger.info(f"💰 BUY EXECUTED: {symbol}")
            self.logger.info(f"   Price: ₹{buy_price:.2f}")
            self.logger.info(f"   Shares: {shares_quantity}")
            self.logger.info(f"   Investment: ₹{actual_investment:.2f}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error executing BUY trade: {e}")
            return False
    
    def monitor_800_profit_target(self) -> List[Dict]:
        """Monitor ₹800 profit target every 15 minutes"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Get active positions
            cursor.execute('''
            SELECT id, symbol, buy_price, shares_quantity, investment, current_profit, 
                   profit_800_base_point, profit_800_timestamp
            FROM trading_positions 
            WHERE status = 'ACTIVE'
            ''')
            
            positions = []
            profit_800_reached = []
            
            for row in cursor.fetchall():
                position = {
                    'id': row[0],
                    'symbol': row[1],
                    'buy_price': row[2],
                    'shares_quantity': row[3],
                    'investment': row[4],
                    'current_profit': row[5],
                    'profit_800_base_point': row[6],
                    'profit_800_timestamp': row[7]
                }
                positions.append(position)
                
                # Check if profit reached ₹800
                if position['current_profit'] >= self.profit_target and not position['profit_800_base_point']:
                    # Calculate ₹800 base price
                    base_price = position['buy_price'] + (self.profit_target / position['shares_quantity'])
                    
                    # Mark ₹800 base point
                    cursor.execute('''
                    UPDATE trading_positions 
                    SET profit_800_base_point = ?, profit_800_timestamp = ?
                    WHERE id = ?
                    ''', (base_price, datetime.now(), position['id']))
                    
                    profit_800_reached.append(position)
                    
                    # Start FF confirmation thread
                    self.start_ff_confirmation_thread(position, base_price)
            
            conn.commit()
            conn.close()
            
            if profit_800_reached:
                self.logger.info(f"📈 ₹800 PROFIT REACHED: {len(profit_800_reached)} positions")
            
            return positions
            
        except Exception as e:
            self.logger.error(f"❌ Error monitoring ₹800 profit: {e}")
            return []
    
    def start_ff_confirmation_thread(self, position: Dict, base_price: float) -> bool:
        """Start FF pattern confirmation thread for SELL"""
        try:
            # Start 2-minute thread for FF confirmation
            success = self.rolling_window_manager.start_monitor(
                symbol=position['symbol'],
                signal_type='SELL',  # FF confirmation for SELL
                base_price=base_price,
                callback=self.handle_ff_confirmation,
                timeout_minutes=10
            )
            
            if success:
                self.logger.info(f"🔄 STARTED FF CONFIRMATION: {position['symbol']} @ ₹{base_price:.2f}")
                return True
            else:
                self.logger.error(f"❌ Failed to start FF thread for {position['symbol']}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error starting FF confirmation: {e}")
            return False
    
    def handle_ff_confirmation(self, symbol: str, confirmation_price: float, 
                              confirmation_time: datetime, data_points: List[Dict]) -> None:
        """Handle FF confirmation callback - Execute SELL trade"""
        try:
            self.logger.info(f"🎯 FF CONFIRMATION RECEIVED: {symbol} @ ₹{confirmation_price:.2f}")
            
            # Get active position
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT id FROM trading_positions 
            WHERE symbol = ? AND status = 'ACTIVE' AND profit_800_base_point IS NOT NULL
            ORDER BY buy_timestamp DESC LIMIT 1
            ''', (symbol,))
            
            result = cursor.fetchone()
            if result:
                position_id = result[0]
                
                # Execute SELL trade
                success = self.execute_sell_trade(symbol, confirmation_price, position_id)
                
                if success:
                    self.logger.info(f"✅ SELL EXECUTED after FF confirmation: {symbol}")
                else:
                    self.logger.error(f"❌ Failed to execute SELL for {symbol}")
            else:
                self.logger.error(f"❌ No active position found for {symbol}")
            
            conn.close()
                
        except Exception as e:
            self.logger.error(f"❌ Error handling FF confirmation: {e}")
    
    def execute_sell_trade(self, symbol: str, sell_price: float, position_id: int) -> bool:
        """Execute SELL when ₹800 + FF pattern confirmed"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Get position details
            cursor.execute('''
            SELECT buy_price, shares_quantity, investment 
            FROM trading_positions WHERE id = ?
            ''', (position_id,))
            
            result = cursor.fetchone()
            if not result:
                return False
            
            buy_price, shares_quantity, investment = result
            
            # Calculate final profit
            sell_value = shares_quantity * sell_price
            final_profit = sell_value - investment
            
            # Update position
            cursor.execute('''
            UPDATE trading_positions 
            SET status = 'COMPLETED', sell_price = ?, sell_value = ?, 
                final_profit = ?, sell_timestamp = ?
            WHERE id = ?
            ''', (sell_price, sell_value, final_profit, datetime.now(), position_id))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"💸 SELL EXECUTED: {symbol}")
            self.logger.info(f"   Buy: ₹{buy_price:.2f} → Sell: ₹{sell_price:.2f}")
            self.logger.info(f"   Final Profit: ₹{final_profit:.2f}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error executing SELL trade: {e}")
            return False

# Global instance
db2_engine = DB2Engine()
