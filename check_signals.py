import sqlite3
conn = sqlite3.connect('Data/trading_operations.db')
cursor = conn.cursor()

cursor.execute('SELECT symbol, status FROM db2_signals_received')
signals = cursor.fetchall()
print('All signals:')
for signal in signals:
    print(f'  {signal[0]}: {signal[1]}')

cursor.execute('SELECT COUNT(*) FROM db2_signals_received WHERE status IN ("RECEIVED", "CONFIRMING")')
pending = cursor.fetchone()[0]
print(f'Pending signals: {pending}')

cursor.execute('SELECT COUNT(*) FROM db2_signals_received WHERE status = "EXECUTED"')
executed = cursor.fetchone()[0]
print(f'Executed signals: {executed}')

conn.close()
