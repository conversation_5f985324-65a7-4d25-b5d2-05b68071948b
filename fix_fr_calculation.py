#!/usr/bin/env python3
"""
Fix F/R (Fall/Rise) Calculation Issue

This script fixes the incorrect F/R movement calculations where all movements
show as 'R' because previous_close is using old data instead of the immediately
previous interval's close price.

Issue: All 360ONE records on 2025-06-13 show fr_movement='R' with previous_close=1047.45
Fix: Recalculate F/R movements using correct interval-to-interval comparisons
"""

import sqlite3
import logging
from datetime import datetime
from typing import List, Dict, Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FRCalculationFixer:
    def __init__(self, db_path: str = "Data/trading_data.db"):
        self.db_path = db_path
        self.logger = logger
    
    def fix_fr_calculations_for_symbol(self, symbol: str, start_date: str = None) -> bool:
        """
        Fix F/R calculations for a specific symbol
        
        Args:
            symbol: Symbol to fix (e.g., '360ONE')
            start_date: Start date for fixing (e.g., '2025-06-13')
        """
        try:
            self.logger.info(f"🔧 Starting F/R calculation fix for {symbol}")
            
            # Get all records for the symbol in chronological order
            records = self._get_symbol_records(symbol, start_date)
            
            if not records:
                self.logger.warning(f"⚠️ No records found for {symbol}")
                return False
            
            self.logger.info(f"📊 Found {len(records)} records for {symbol}")
            
            # Recalculate F/R movements
            fixed_records = self._recalculate_fr_movements(records)
            
            # Update database with corrected values
            updated_count = self._update_database_records(fixed_records)
            
            self.logger.info(f"✅ Fixed {updated_count} F/R calculations for {symbol}")
            
            # Verify the fix
            self._verify_fix(symbol, start_date)
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error fixing F/R calculations for {symbol}: {e}")
            return False
    
    def _get_symbol_records(self, symbol: str, start_date: str = None) -> List[Dict]:
        """Get all records for a symbol in chronological order"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Build query with optional date filter
            query = '''
            SELECT id, symbol, timestamp, close_price, fr_movement, previous_close, fr_calculated
            FROM trading_data 
            WHERE symbol = ?
            '''
            params = [symbol]
            
            if start_date:
                query += ' AND timestamp >= ?'
                params.append(start_date)
            
            query += ' ORDER BY timestamp ASC'
            
            cursor.execute(query, params)
            
            # Convert to list of dictionaries
            columns = [desc[0] for desc in cursor.description]
            records = [dict(zip(columns, row)) for row in cursor.fetchall()]
            
            conn.close()
            return records
            
        except Exception as e:
            self.logger.error(f"❌ Error getting records for {symbol}: {e}")
            return []
    
    def _recalculate_fr_movements(self, records: List[Dict]) -> List[Dict]:
        """Recalculate F/R movements using correct interval-to-interval logic"""
        try:
            fixed_records = []
            previous_close = None
            
            for i, record in enumerate(records):
                current_close = record['close_price']
                
                if i == 0 or previous_close is None:
                    # First record or no previous data
                    record['fr_movement'] = 'START'
                    record['previous_close'] = None
                    record['fr_calculated'] = True
                    self.logger.debug(f"📊 {record['symbol']} {record['timestamp']}: START")
                else:
                    # Calculate F/R based on previous interval
                    if current_close > previous_close:
                        fr_movement = 'R'
                    elif current_close < previous_close:
                        fr_movement = 'F'
                    else:
                        fr_movement = 'N'
                    
                    record['fr_movement'] = fr_movement
                    record['previous_close'] = previous_close
                    record['fr_calculated'] = True
                    
                    self.logger.debug(f"📊 {record['symbol']} {record['timestamp']}: "
                                    f"₹{previous_close:.2f} → ₹{current_close:.2f} = {fr_movement}")
                
                fixed_records.append(record)
                previous_close = current_close
            
            return fixed_records
            
        except Exception as e:
            self.logger.error(f"❌ Error recalculating F/R movements: {e}")
            return []
    
    def _update_database_records(self, records: List[Dict]) -> int:
        """Update database with corrected F/R values"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            updated_count = 0
            
            for record in records:
                cursor.execute('''
                UPDATE trading_data 
                SET fr_movement = ?, previous_close = ?, fr_calculated = ?
                WHERE id = ?
                ''', (
                    record['fr_movement'],
                    record['previous_close'],
                    record['fr_calculated'],
                    record['id']
                ))
                
                if cursor.rowcount > 0:
                    updated_count += 1
            
            conn.commit()
            conn.close()
            
            return updated_count
            
        except Exception as e:
            self.logger.error(f"❌ Error updating database records: {e}")
            return 0
    
    def _verify_fix(self, symbol: str, start_date: str = None):
        """Verify that the F/R calculations are now correct"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get F/R distribution
            query = '''
            SELECT fr_movement, COUNT(*) as count
            FROM trading_data 
            WHERE symbol = ?
            '''
            params = [symbol]
            
            if start_date:
                query += ' AND timestamp >= ?'
                params.append(start_date)
            
            query += ' GROUP BY fr_movement ORDER BY count DESC'
            
            cursor.execute(query, params)
            results = cursor.fetchall()
            
            self.logger.info(f"📊 F/R Distribution for {symbol}:")
            for fr_movement, count in results:
                self.logger.info(f"   {fr_movement}: {count} records")
            
            # Check for consecutive intervals
            query2 = '''
            SELECT timestamp, close_price, fr_movement, previous_close
            FROM trading_data 
            WHERE symbol = ?
            '''
            if start_date:
                query2 += ' AND timestamp >= ?'
            
            query2 += ' ORDER BY timestamp ASC LIMIT 10'
            
            cursor.execute(query2, params)
            sample_records = cursor.fetchall()
            
            self.logger.info(f"📊 Sample corrected records for {symbol}:")
            for timestamp, close_price, fr_movement, previous_close in sample_records:
                prev_str = f"₹{previous_close:.2f}" if previous_close else "None"
                self.logger.info(f"   {timestamp}: {prev_str} → ₹{close_price:.2f} = {fr_movement}")
            
            conn.close()
            
        except Exception as e:
            self.logger.error(f"❌ Error verifying fix: {e}")

    def fix_all_symbols_fr_calculations(self, start_date: str = None) -> bool:
        """Fix F/R calculations for all symbols in the database"""
        try:
            # Get all unique symbols
            symbols = self._get_all_symbols()

            if not symbols:
                self.logger.warning("⚠️ No symbols found in database")
                return False

            self.logger.info(f"🔧 Fixing F/R calculations for {len(symbols)} symbols")

            success_count = 0
            for symbol in symbols:
                self.logger.info(f"🔄 Processing {symbol}...")
                if self.fix_fr_calculations_for_symbol(symbol, start_date):
                    success_count += 1
                else:
                    self.logger.warning(f"⚠️ Failed to fix {symbol}")

            self.logger.info(f"✅ Successfully fixed {success_count}/{len(symbols)} symbols")
            return success_count == len(symbols)

        except Exception as e:
            self.logger.error(f"❌ Error fixing all symbols: {e}")
            return False

    def _get_all_symbols(self) -> List[str]:
        """Get all unique symbols from the database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('SELECT DISTINCT symbol FROM trading_data ORDER BY symbol')
            symbols = [row[0] for row in cursor.fetchall()]

            conn.close()
            return symbols

        except Exception as e:
            self.logger.error(f"❌ Error getting symbols: {e}")
            return []

def main():
    """Main function to fix F/R calculations"""
    fixer = FRCalculationFixer()

    logger.info("🚀 Starting F/R Calculation Fix")
    logger.info("=" * 50)

    # Option 1: Fix specific symbol
    # success = fixer.fix_fr_calculations_for_symbol('360ONE', '2025-06-13')

    # Option 2: Fix all symbols (recommended)
    success = fixer.fix_all_symbols_fr_calculations('2025-06-13')

    if success:
        logger.info("✅ F/R calculation fix completed successfully!")
        logger.info("🎯 Pattern detection should now work correctly")
    else:
        logger.error("❌ F/R calculation fix failed!")

if __name__ == "__main__":
    main()
