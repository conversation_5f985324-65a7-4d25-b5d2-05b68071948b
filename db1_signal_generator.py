#!/usr/bin/env python3
"""
DB1 Signal Generator - Real-time Pattern Detection and Signal Generation
Works alongside data_integrity_checker.py and realtime_data_fetcher.py
Triggers immediately when data is fetched - NO STANDALONE LOOPS
"""

import sqlite3
import pandas as pd
import logging
import time
import threading
from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Dict, Optional, Tuple
from db1_db2_communicator import TradingSignal, ActivePosition, get_communicator

class DB1_SignalGenerator:
    """
    🎯 DB1 Signal Generator - PURE SIGNAL GENERATION ONLY

    🔄 SIMPLIFIED RESPONSIBILITIES (NO EXECUTION/MONITORING):
    1. Work alongside data_integrity_checker.py and realtime_data_fetcher.py
    2. Trigger immediately when data is fetched (no standalone loops)
    3. Implement rolling window pattern detection (4F+1R)
    4. Handle GOLD/SILVER/BRONZE priority categories
    5. Generate BUY signals ONLY when 4F+1R patterns detected
    6. Send BUY signals to DB2 (ONE-WAY: DB1→DB2)

    ❌ REMOVED RESPONSIBILITIES (NOW HANDLED BY DB2):
    - NO profit monitoring (DB2 handles ₹800 profit tracking)
    - NO SELL signal generation (DB2 handles SELL with FF confirmation)
    - NO position management (DB2 handles complete trade lifecycle)
    - NO back communication from DB2

    🔄 DOES NOT EXECUTE TRADES - Only generates BUY signals
    📤 Sends signals to: DB2 Trade Executor (ONE-WAY ONLY)
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.db_path = 'Data/trading_data.db'
        self.trading_db_path = 'Data/trading_operations.db'
        self.communicator = get_communicator()

        # Portfolio Management (moved from trading_engine.py)
        self.total_pot = 22200000.0  # ₹2.22 Cr
        self.investment_per_symbol = 100000.0  # ₹1 Lakh per symbol
        self.vault_amount = 0.0
        self.active_positions = {}
        self.completed_trades = []

        # Pattern detection parameters
        self.min_drop_percentage = 0.5  # Minimum 0.5% drop required
        self.profit_target = 800  # ₹800 profit target

        # ROLLING WINDOW tracking for real-time F/R pattern detection
        self.symbol_patterns = {}  # Track F/R patterns for each symbol
        self.rolling_window_data = {}  # Store rolling window data for each symbol

        # Priority categories
        self.priority_categories = {
            'GOLD': [],    # Active positions - highest priority
            'SILVER': [],  # Symbols close to patterns
            'BRONZE': [],  # Symbols with recent activity
            'REMAINING': []  # All other symbols
        }

        # Initialize database tables
        self._initialize_database_tables()
        self._initialize_rolling_window_tables()

        # Load existing positions and rolling window data
        self._load_active_positions_from_database()
        self._load_rolling_window_data_from_database()

        self.logger.info("✅ DB1 Signal Generator initialized - Real-time pattern detection & position management")
        self.logger.info(f"🔄 Loaded rolling window data for {len(self.rolling_window_data)} symbols")
    
    def trigger_analysis_after_data_fetch(self, source: str = "data_fetch", symbols_updated: List[str] = None):
        """
        🎯 MAIN TRIGGER METHOD - Called immediately after data is fetched
        Works alongside data_integrity_checker.py and realtime_data_fetcher.py

        Args:
            source: "data_integrity" or "realtime_fetch"
            symbols_updated: List of symbols that got new data
        """
        try:
            start_time = time.time()

            self.logger.info("=" * 80)
            self.logger.info(f"🎯 DB1 ANALYSIS TRIGGERED by {source.upper()} - {datetime.now().strftime('%H:%M:%S')}")
            self.logger.info("=" * 80)

            # Step 1: Update priority categories (GOLD/SILVER/BRONZE)
            self._update_priority_categories()

            # Step 2: REMOVED - No position management in DB1 (handled by DB2)
            # DB1 only generates BUY signals, DB2 handles complete trade lifecycle

            # Step 3: Use simple trading system for pattern analysis
            target_symbols = symbols_updated if symbols_updated else self._get_all_symbols()
            buy_signals_generated = self._trigger_simple_trading_system(target_symbols)

            # Step 4: REMOVED - No profit monitoring in DB1 (handled by DB2)
            sell_signals_generated = 0  # DB1 no longer generates SELL signals

            # Step 5: Simple system already triggered in step 3

            # Step 6: Log cycle summary
            cycle_time = time.time() - start_time
            self.logger.info(f"📊 DB1 ANALYSIS COMPLETE: {buy_signals_generated} BUY signals, "
                           f"{sell_signals_generated} SELL signals generated in {cycle_time:.2f}s")
            self.logger.info(f"🎯 Analyzed {len(target_symbols)} symbols triggered by {source}")

            return {
                'success': True,
                'buy_signals': buy_signals_generated,
                'sell_signals': sell_signals_generated,
                'symbols_analyzed': len(target_symbols),
                'cycle_time': cycle_time
            }

        except Exception as e:
            self.logger.error(f"❌ Error in DB1 analysis trigger: {e}")
            return {'success': False, 'error': str(e)}

    def _initialize_database_tables(self):
        """Initialize database tables for position management"""
        try:
            conn = sqlite3.connect(self.trading_db_path)
            cursor = conn.cursor()

            # Create trading positions table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS trading_positions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                buy_price REAL NOT NULL,
                shares_quantity INTEGER NOT NULL,
                investment REAL NOT NULL,
                target_value REAL NOT NULL,
                target_price REAL NOT NULL,
                buy_time DATETIME NOT NULL,
                sell_time DATETIME,
                sell_price REAL,
                actual_profit REAL,
                status TEXT NOT NULL DEFAULT 'ACTIVE',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            # Create portfolio status table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS portfolio_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                total_pot REAL NOT NULL DEFAULT 22200000.0,
                vault_amount REAL NOT NULL DEFAULT 0.0,
                active_positions INTEGER NOT NULL DEFAULT 0,
                completed_trades INTEGER NOT NULL DEFAULT 0,
                total_profit REAL NOT NULL DEFAULT 0.0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            conn.commit()
            conn.close()
            self.logger.info("✅ Database tables initialized for position management")

        except Exception as e:
            self.logger.error(f"❌ Error initializing database tables: {e}")

    def _initialize_rolling_window_tables(self):
        """Initialize database tables for persistent rolling window storage"""
        try:
            conn = sqlite3.connect(self.trading_db_path)
            cursor = conn.cursor()

            # Create rolling window patterns table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS rolling_window_patterns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL UNIQUE,
                fr_pattern TEXT,  -- JSON string of F/R pattern array
                price_history TEXT,  -- JSON string of price history array
                last_update DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            conn.commit()
            conn.close()
            self.logger.info("✅ Rolling window tables initialized for persistent storage")

        except Exception as e:
            self.logger.error(f"❌ Error initializing rolling window tables: {e}")

    def _load_rolling_window_data_from_database(self):
        """Load existing rolling window data from database for continuity"""
        try:
            import json

            conn = sqlite3.connect(self.trading_db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT symbol, fr_pattern, price_history, last_update
                FROM rolling_window_patterns
            ''')

            patterns = cursor.fetchall()
            conn.close()

            for pattern in patterns:
                symbol, fr_pattern_json, price_history_json, last_update = pattern

                try:
                    # Parse JSON data
                    fr_pattern = json.loads(fr_pattern_json) if fr_pattern_json else []
                    price_history = json.loads(price_history_json) if price_history_json else []

                    # Restore rolling window data
                    self.rolling_window_data[symbol] = {
                        'fr_pattern': fr_pattern,
                        'price_history': price_history,
                        'last_update': last_update
                    }

                    self.logger.debug(f"🔄 Restored {symbol}: Pattern {fr_pattern} ({len(fr_pattern)}/5)")

                except json.JSONDecodeError as e:
                    self.logger.error(f"❌ Error parsing rolling window data for {symbol}: {e}")

            self.logger.info(f"✅ Loaded rolling window data for {len(self.rolling_window_data)} symbols from database")

        except Exception as e:
            self.logger.error(f"❌ Error loading rolling window data: {e}")

    def _save_rolling_window_data_to_database(self, symbol: str):
        """Save rolling window data for a symbol to database"""
        try:
            import json

            if symbol not in self.rolling_window_data:
                return

            window_data = self.rolling_window_data[symbol]

            conn = sqlite3.connect(self.trading_db_path)
            cursor = conn.cursor()

            # Convert arrays to JSON strings
            fr_pattern_json = json.dumps(window_data.get('fr_pattern', []))
            price_history_json = json.dumps(window_data.get('price_history', []))
            last_update = window_data.get('last_update', datetime.now().isoformat())

            # Insert or update rolling window data
            cursor.execute('''
            INSERT OR REPLACE INTO rolling_window_patterns
            (symbol, fr_pattern, price_history, last_update, updated_at)
            VALUES (?, ?, ?, ?, ?)
            ''', (
                symbol,
                fr_pattern_json,
                price_history_json,
                last_update,
                datetime.now().isoformat()
            ))

            conn.commit()
            conn.close()

            self.logger.debug(f"💾 Saved rolling window data for {symbol}")

        except Exception as e:
            self.logger.error(f"❌ Error saving rolling window data for {symbol}: {e}")

    def _load_active_positions_from_database(self):
        """Load existing active positions from database"""
        try:
            conn = sqlite3.connect(self.trading_db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT symbol, buy_price, shares_quantity, investment, target_price, buy_time
                FROM trading_positions
                WHERE status = 'ACTIVE'
            ''')

            positions = cursor.fetchall()
            conn.close()

            for position in positions:
                symbol, buy_price, quantity, investment, target_price, buy_time = position

                # Create ActivePosition object instead of dictionary
                position_obj = ActivePosition(
                    symbol=symbol,
                    buy_price=buy_price,
                    shares_quantity=quantity,
                    investment=investment,
                    target_price=target_price,
                    buy_time=datetime.fromisoformat(buy_time) if isinstance(buy_time, str) else buy_time,
                    current_profit=0.0,  # Will be calculated later
                    status='ACTIVE',
                    timestamp_ns=time.time_ns()
                )

                self.active_positions[symbol] = position_obj

            self.logger.info(f"✅ Loaded {len(self.active_positions)} active positions from database")

        except Exception as e:
            self.logger.error(f"❌ Error loading active positions: {e}")

    def _update_active_positions_from_db2(self):
        """Update active positions by reading directly from DB2 database"""
        try:
            # 🎯 READ DIRECTLY FROM DB2 DATABASE (no queue dependency)
            db2_positions = self._read_active_positions_from_db2_database()

            if not db2_positions:
                return 0

            updated_count = 0

            for db2_position in db2_positions:
                symbol = db2_position['symbol']

                # Create ActivePosition object from DB2 data
                position = ActivePosition(
                    symbol=symbol,
                    buy_price=db2_position['buy_price'],
                    shares_quantity=db2_position['shares_quantity'],
                    investment=db2_position['investment'],
                    target_price=db2_position['target_price'],
                    buy_time=datetime.fromisoformat(db2_position['buy_time']) if isinstance(db2_position['buy_time'], str) else db2_position['buy_time'],
                    current_profit=self._calculate_current_profit(symbol, db2_position),
                    status='ACTIVE',
                    timestamp_ns=time.time_ns()
                )

                # Store position in DB1 active positions for monitoring
                self.active_positions[symbol] = position
                updated_count += 1

                # Store position in DB1 database for persistence
                self._store_active_position_in_database(
                    position.symbol,
                    position.buy_price,
                    position.shares_quantity,
                    position.investment,
                    position.target_price,
                    position.buy_time.isoformat() if hasattr(position.buy_time, 'isoformat') else str(position.buy_time)
                )

                self.logger.info(f"📥 POSITION SYNCED FROM DB2: {position.symbol}")
                self.logger.info(f"   💰 Investment: ₹{position.investment:.0f}")
                self.logger.info(f"   🎯 Target: ₹{position.investment + self.profit_target:.0f} (₹{self.profit_target} profit)")
                self.logger.info(f"   📊 Current Profit: ₹{position.current_profit:.0f}")
                self.logger.info(f"   ✅ Now monitoring for ₹{self.profit_target} profit target")

            if updated_count > 0:
                self.logger.info(f"📊 Synced {updated_count} active positions from DB2 database")

            self.logger.info(f"📈 ACTIVE POSITIONS: {len(self.active_positions)} symbols being monitored")
            return updated_count

        except Exception as e:
            self.logger.error(f"❌ Error syncing active positions from DB2: {e}")
            return 0

    def _read_active_positions_from_db2_database(self):
        """Read active positions directly from DB2 database"""
        try:
            # Connect to DB2 database
            db2_path = 'Data/db2_trading_data.db'
            conn = sqlite3.connect(db2_path)
            cursor = conn.cursor()

            # Check if table exists, if not return empty list
            cursor.execute('''
            SELECT name FROM sqlite_master WHERE type='table' AND name='trading_positions'
            ''')

            if not cursor.fetchone():
                self.logger.info("📊 DB2 trading_positions table doesn't exist yet - no active positions")
                conn.close()
                return []

            cursor.execute('''
            SELECT symbol, buy_price, shares_quantity, investment, target_price, buy_time
            FROM trading_positions
            WHERE status = 'ACTIVE'
            ''')

            rows = cursor.fetchall()
            conn.close()

            positions = []
            for row in rows:
                positions.append({
                    'symbol': row[0],
                    'buy_price': row[1],
                    'shares_quantity': row[2],
                    'investment': row[3],
                    'target_price': row[4],
                    'buy_time': row[5]
                })

            self.logger.info(f"📊 Read {len(positions)} active positions from DB2 database")
            return positions

        except Exception as e:
            self.logger.error(f"❌ Error reading from DB2 database: {e}")
            return []

    def _calculate_current_profit(self, symbol: str, position_data: dict) -> float:
        """Calculate current profit for a position"""
        try:
            # Get current price
            current_price = self._get_current_price(symbol)

            if current_price:
                current_value = position_data['shares_quantity'] * current_price
                current_profit = current_value - position_data['investment']
                return current_profit
            else:
                return 0.0

        except Exception as e:
            self.logger.error(f"❌ Error calculating current profit for {symbol}: {e}")
            return 0.0

    def _update_priority_categories(self):
        """Update GOLD/SILVER/BRONZE priority categories"""
        try:
            # Reset categories
            self.priority_categories = {'GOLD': [], 'SILVER': [], 'BRONZE': [], 'REMAINING': []}

            # Get all symbols
            all_symbols = self._get_all_symbols()

            # GOLD: Active positions (highest priority)
            for symbol in self.active_positions.keys():
                if symbol in all_symbols:
                    self.priority_categories['GOLD'].append(symbol)
                    all_symbols.remove(symbol)

            # SILVER: Symbols close to 4F+1R pattern (3F or 2F already detected)
            silver_symbols = []
            for symbol in all_symbols[:]:
                pattern_data = self.symbol_patterns.get(symbol, {})
                consecutive_falls = pattern_data.get('consecutive_falls', 0)
                if consecutive_falls >= 2:  # 2 or 3 falls already detected
                    silver_symbols.append(symbol)
                    all_symbols.remove(symbol)

            self.priority_categories['SILVER'] = silver_symbols[:11]  # Limit to 11 symbols

            # BRONZE: Symbols with recent activity or volatility
            bronze_symbols = all_symbols[:5]  # Take first 5 remaining symbols
            self.priority_categories['BRONZE'] = bronze_symbols
            for symbol in bronze_symbols:
                all_symbols.remove(symbol)

            # REMAINING: All other symbols
            self.priority_categories['REMAINING'] = all_symbols

            self.logger.info(f"📊 PRIORITY CATEGORIES UPDATED:")
            self.logger.info(f"🥇 GOLD: {len(self.priority_categories['GOLD'])} symbols (Active positions)")
            self.logger.info(f"🥈 SILVER: {len(self.priority_categories['SILVER'])} symbols (Close to pattern)")
            self.logger.info(f"🥉 BRONZE: {len(self.priority_categories['BRONZE'])} symbols (Recent activity)")
            self.logger.info(f"⚪ REMAINING: {len(self.priority_categories['REMAINING'])} symbols")

        except Exception as e:
            self.logger.error(f"❌ Error updating priority categories: {e}")

    def _get_all_symbols(self) -> List[str]:
        """Get all symbols from database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT DISTINCT symbol FROM trading_data ORDER BY symbol")
            symbols = [row[0] for row in cursor.fetchall()]
            conn.close()

            return symbols

        except Exception as e:
            self.logger.error(f"❌ Error getting symbols: {e}")
            return []

    def _trigger_simple_trading_system(self, symbols: List[str]):
        """🚀 TRIGGER SIMPLE TRADING SYSTEM instead of complex rolling window"""
        try:
            self.logger.info(f"🚀 TRIGGERING SIMPLE TRADING SYSTEM for {len(symbols)} symbols")

            # Import and use simple trading system
            from simple_trading_system import get_simple_trading_system

            simple_system = get_simple_trading_system()

            # Use simple ultra-fast signal generation
            signals_generated = simple_system.ultra_fast_signal_generation(symbols)

            if signals_generated > 0:
                self.logger.info(f"🎉 SIMPLE SYSTEM: {signals_generated} signals generated")
            else:
                self.logger.info(f"📊 SIMPLE SYSTEM: No signals generated")

            return signals_generated

        except Exception as e:
            self.logger.error(f"❌ Error in simple trading system: {e}")
            return 0

    def _analyze_patterns_and_generate_buy_signals(self, target_symbols: List[str] = None) -> int:
        """
        Analyze 15-minute data for 4F+1R patterns and generate BUY signals
        Returns: Number of BUY signals generated
        """
        try:
            signals_generated = 0
            analyzed_count = 0

            # Determine symbols to analyze based on priority
            if target_symbols:
                symbols_to_analyze = target_symbols
                self.logger.info(f"📊 ANALYZING {len(symbols_to_analyze)} TARGET SYMBOLS for 4F+1R patterns...")
            else:
                # Use priority order: GOLD -> SILVER -> BRONZE -> REMAINING
                symbols_to_analyze = []
                symbols_to_analyze.extend(self.priority_categories['GOLD'])
                symbols_to_analyze.extend(self.priority_categories['SILVER'])
                symbols_to_analyze.extend(self.priority_categories['BRONZE'])
                symbols_to_analyze.extend(self.priority_categories['REMAINING'])

                self.logger.info(f"📊 ANALYZING ALL SYMBOLS IN PRIORITY ORDER:")
                self.logger.info(f"🥇 GOLD: {len(self.priority_categories['GOLD'])} symbols first")
                self.logger.info(f"🥈 SILVER: {len(self.priority_categories['SILVER'])} symbols second")
                self.logger.info(f"🥉 BRONZE: {len(self.priority_categories['BRONZE'])} symbols third")
                self.logger.info(f"⚪ REMAINING: {len(self.priority_categories['REMAINING'])} symbols last")

            for symbol in symbols_to_analyze:
                try:
                    analyzed_count += 1

                    # Log every 10th symbol to show progress
                    if analyzed_count % 10 == 0 or analyzed_count <= 5:
                        self.logger.info(f"🔍 Analyzing symbol {analyzed_count}/{len(symbols_to_analyze)}: {symbol}")

                    # Skip symbols with active positions (except GOLD category for monitoring)
                    if symbol in self.active_positions and symbol not in self.priority_categories['GOLD']:
                        self.logger.info(f"⏭️ SKIP {symbol}: Already has active position")
                        continue
                    
                    # Get latest 6 data points for 4F+1R pattern
                    # (6 points = 5 comparisons = 4F+1R)
                    latest_data = self._get_latest_data_points(symbol, count=6)

                    # Log data availability for debugging
                    if len(latest_data) < 6:
                        self.logger.info(f"📊 {symbol}: Only {len(latest_data)}/6 intervals available")

                    if len(latest_data) >= 6:  # Require 6 data points for 4F+1R
                        # Check for TRUE 4F+1R pattern with 6 data points
                        pattern_result = self._detect_4fall_1rise_pattern_with_logging(symbol, latest_data)
                        if pattern_result:
                            current_price = latest_data[0]['close_price']

                            # Generate BUY signal
                            signal = self._create_buy_signal(symbol, current_price, latest_data)

                            # 💾 SAVE SIGNAL TO DATABASE FIRST
                            signal_saved = self._save_signal_to_database(signal)

                            if signal_saved:
                                self.logger.info(f"💾 BUY SIGNAL SAVED TO DB1: {symbol} @ ₹{current_price:.2f}")

                                # Send signal to DB2
                                if self.communicator.send_signal_to_db2(signal):
                                    signals_generated += 1
                                    self.logger.info(f"🎯 BUY SIGNAL GENERATED: {symbol} @ ₹{current_price:.2f}")
                                    self.logger.info(f"📤 Signal sent to DB2 for R+R confirmation")

                                    # Create pending position (will be activated by DB2 confirmation)
                                    self._create_pending_position(symbol, current_price)
                                else:
                                    self.logger.error(f"❌ Failed to send BUY signal to DB2 for {symbol}")
                            else:
                                self.logger.error(f"❌ Failed to save BUY signal to database for {symbol}")
                        else:
                            # Log why pattern was rejected (every 10th symbol to avoid spam)
                            if hash(symbol) % 10 == 0:
                                self.logger.info(f"⏭️ {symbol}: No 4F+1R pattern (latest: ₹{latest_data[0]['close_price']:.2f})")
                
                except Exception as e:
                    self.logger.error(f"❌ Error analyzing {symbol}: {e}")
                    continue
            
            self.logger.info(f"📊 BUY SIGNAL ANALYSIS COMPLETE: {analyzed_count} symbols analyzed, {signals_generated} signals generated")
            return signals_generated
            
        except Exception as e:
            self.logger.error(f"❌ Error in pattern analysis: {e}")
            return 0
    
    def _monitor_positions_and_generate_sell_signals(self) -> int:
        """
        Monitor active positions for ₹800 profit target and generate SELL signals
        Returns: Number of SELL signals generated
        """
        try:
            if not self.active_positions:
                self.logger.info("📊 No active positions to monitor for SELL signals")
                return 0
            
            self.logger.info(f"💰 MONITORING {len(self.active_positions)} POSITIONS FOR PROFIT TARGET...")
            
            signals_generated = 0
            
            for symbol, position in self.active_positions.items():
                try:
                    # Get current price
                    current_price = self._get_current_price(symbol)
                    
                    if current_price:
                        # Calculate current profit
                        current_profit = (current_price * position.shares_quantity) - position.investment
                        
                        self.logger.info(f"📊 {symbol}: Current ₹{current_price:.2f}, "
                                       f"Profit ₹{current_profit:.0f} (Target: ₹{self.profit_target})")
                        
                        # 🎯 CHECK IF ₹800 PROFIT TARGET REACHED
                        if current_profit >= self.profit_target:
                            self.logger.info(f"🎉 PROFIT TARGET REACHED! {symbol}: ₹{current_profit:.0f} ≥ ₹{self.profit_target}")
                            self.logger.info(f"📤 SENDING SELL SIGNAL TO DB2 for execution...")

                            # Generate SELL signal
                            signal = self._create_sell_signal(symbol, current_price, position, current_profit)

                            # 💾 SAVE SIGNAL TO DATABASE FIRST
                            signal_saved = self._save_signal_to_database(signal)

                            if signal_saved:
                                self.logger.info(f"💾 SELL SIGNAL SAVED TO DB1: {symbol} @ ₹{current_price:.2f}")

                                # 📤 SEND SELL SIGNAL TO DB2 FOR EXECUTION
                                if self.communicator.send_signal_to_db2(signal):
                                    signals_generated += 1
                                    self.logger.info(f"🎯 SELL SIGNAL SENT TO DB2: {symbol} @ ₹{current_price:.2f}")
                                    self.logger.info(f"💰 Profit achieved: ₹{current_profit:.0f} (Target: ₹{self.profit_target})")
                                    self.logger.info(f"🔄 DB2 will now execute SELL with F+F confirmation")

                                    # Remove from active monitoring (DB2 will handle execution)
                                    del self.active_positions[symbol]
                                    self.logger.info(f"✅ {symbol} removed from DB1 monitoring - DB2 handling SELL execution")

                                else:
                                    self.logger.error(f"❌ Failed to send SELL signal to DB2 for {symbol}")
                            else:
                                self.logger.error(f"❌ Failed to save SELL signal to database for {symbol}")
                
                except Exception as e:
                    self.logger.error(f"❌ Error monitoring {symbol}: {e}")
                    continue
            
            self.logger.info(f"📊 SELL SIGNAL MONITORING COMPLETE: {signals_generated} signals generated")
            return signals_generated
            
        except Exception as e:
            self.logger.error(f"❌ Error in position monitoring: {e}")
            return 0

    def _store_active_position_in_database(self, symbol: str, price: float, quantity: int, investment: float, target_price: float, buy_time: str):
        """Store active position in database for persistence (moved from trading_engine.py)"""
        try:
            conn = sqlite3.connect(self.trading_db_path)
            cursor = conn.cursor()

            # Insert active position into trading_positions table
            cursor.execute('''
            INSERT INTO trading_positions
            (symbol, buy_price, shares_quantity, investment, target_value, target_price, buy_time, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                symbol,
                price,
                quantity,
                investment,
                quantity * target_price,  # target_value
                target_price,
                buy_time,
                'ACTIVE'
            ))

            conn.commit()
            conn.close()

            self.logger.info(f"💾 Stored active position in database: {symbol}")

        except Exception as e:
            self.logger.error(f"❌ Error storing active position in database: {e}")

    def _remove_active_position_from_database(self, symbol: str, sell_price: float, sell_time: str, profit: float):
        """Remove active position from database and mark as sold (moved from trading_engine.py)"""
        try:
            conn = sqlite3.connect(self.trading_db_path)
            cursor = conn.cursor()

            # Update position status to SOLD
            cursor.execute('''
            UPDATE trading_positions
            SET status = 'SOLD', sell_price = ?, sell_time = ?, actual_profit = ?
            WHERE symbol = ? AND status = 'ACTIVE'
            ''', (sell_price, sell_time, profit, symbol))

            conn.commit()
            conn.close()

            self.logger.info(f"💾 Updated position in database: {symbol} -> SOLD")

        except Exception as e:
            self.logger.error(f"❌ Error updating position in database: {e}")

    def _update_portfolio_status_in_database(self):
        """Update portfolio status in database (moved from trading_engine.py)"""
        try:
            conn = sqlite3.connect(self.trading_db_path)
            cursor = conn.cursor()

            # Calculate current portfolio metrics
            total_profit = sum(pos.get('current_profit', 0) for pos in self.active_positions.values())
            active_count = len(self.active_positions)
            completed_count = len(self.completed_trades)

            # Insert current portfolio status
            cursor.execute('''
            INSERT INTO portfolio_status
            (total_pot, vault_amount, active_positions, completed_trades, total_profit, updated_at)
            VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                self.total_pot,
                self.vault_amount,
                active_count,
                completed_count,
                total_profit,
                datetime.now().isoformat()
            ))

            conn.commit()
            conn.close()

            self.logger.debug(f"💾 Updated portfolio status in database")

        except Exception as e:
            self.logger.error(f"❌ Error updating portfolio status: {e}")
    
    def _detect_4fall_1rise_pattern(self, data_points: List[Dict]) -> bool:
        """
        Detect 4-FALL + 1-RISE pattern with 0.5% drop condition
        Args: data_points - List of 5 data points (most recent first)
        Returns: True if pattern detected, False otherwise
        """
        try:
            if len(data_points) < 6:
                return False

            # Extract prices in chronological order (reverse the list)
            prices = [point['close_price'] for point in reversed(data_points)]

            # Calculate trends from the 6 data points (5 trends)
            trends = []
            for i in range(1, len(prices)):
                if prices[i] > prices[i-1]:
                    trends.append("RISE")
                elif prices[i] < prices[i-1]:
                    trends.append("FALL")
                else:
                    trends.append("FLAT")

            # Check for TRUE 4-FALL + 1-RISE pattern (exactly 5 trends from 6 data points)
            # 6 data points = 5 movements = 4F+1R pattern (1 hidden + 5 visible)
            if len(trends) == 5:
                expected_pattern = ["FALL", "FALL", "FALL", "FALL", "RISE"]

                # Check if pattern is EXACTLY 4 FALLS followed by 1 RISE
                if trends == expected_pattern:
                    # Pattern confirmed: 4F+1R detected
                        
                        # Check 0.5% drop condition
                        start_price = prices[0]  # First price
                        lowest_price = min(prices[:-1])  # Lowest price before the final rise
                        
                        # Calculate percentage drop
                        percentage_drop = ((start_price - lowest_price) / start_price) * 100
                        
                        if percentage_drop >= self.min_drop_percentage:
                            self.logger.info(f"✅ 4F+1R Pattern + {percentage_drop:.2f}% Drop DETECTED!")
                            return True
                        else:
                            self.logger.debug(f"❌ 4F+1R Pattern found but insufficient drop: "
                                            f"{percentage_drop:.2f}% (need ≥{self.min_drop_percentage}%)")
            
            return False

        except Exception as e:
            self.logger.error(f"❌ Error in pattern detection: {e}")
            return False

    def _detect_4fall_1rise_pattern_with_logging(self, symbol: str, data_points: List[Dict]) -> bool:
        """
        Detect 4-FALL + 1-RISE pattern with detailed logging for debugging
        Args: symbol, data_points - List of 5 data points (most recent first) for 4F+1R
        Returns: True if pattern detected, False otherwise
        """
        try:
            if len(data_points) < 6:
                self.logger.info(f"⏭️ {symbol}: Insufficient data ({len(data_points)}/6 intervals minimum for 4F+1R)")
                return False

            # We now require exactly 6 data points for 4F+1R pattern
            if len(data_points) == 6:
                self.logger.info(f"📊 {symbol}: Analyzing 4F+1R pattern with {len(data_points)} intervals")

            # 🎯 FORWARD-ONLY TIME COMPARISON: Extract prices in chronological order (reverse the list)
            # Database returns newest first, we need oldest first for forward-only analysis
            prices = [point['close_price'] for point in reversed(data_points)]
            timestamps = [point['timestamp'] for point in reversed(data_points)]

            # Log price sequence for analysis with timestamps
            price_str = " → ".join([f"₹{p:.2f}" for p in prices])
            time_str = " → ".join([ts.split(' ')[1][:5] if ' ' in ts else ts[:5] for ts in timestamps])
            self.logger.info(f"🔍 {symbol}: FORWARD-ONLY Analysis: {price_str}")
            self.logger.info(f"🕐 {symbol}: Time sequence: {time_str}")

            # 🎯 FORWARD-ONLY: Calculate trends from the 6 data points (5 movements)
            # Each movement compares current point with PREVIOUS point (forward in time)
            trends = []
            for i in range(1, len(prices)):
                previous_price = prices[i-1]  # Earlier in time
                current_price = prices[i]     # Later in time

                if current_price > previous_price:
                    trends.append("RISE")
                elif current_price < previous_price:
                    trends.append("FALL")
                else:
                    trends.append("FLAT")

            trend_str = " → ".join(trends)
            self.logger.info(f"📊 {symbol}: Trend pattern: {trend_str}")

            # Store rolling window pattern for this symbol
            if symbol not in self.symbol_patterns:
                self.symbol_patterns[symbol] = {}

            # Update rolling pattern tracking
            current_trend = trends[-1]  # Latest trend (F or R)
            self.symbol_patterns[symbol]['last_trend'] = current_trend
            self.symbol_patterns[symbol]['trend_history'] = trends
            self.symbol_patterns[symbol]['last_update'] = datetime.now().isoformat()

            # Check for 4F+1R pattern ONLY (6 data points = 5 movements)
            if len(data_points) == 6 and len(trends) == 5:
                # 4F+1R pattern (6 data points = 5 movements = 4F+1R)
                expected_pattern = ["FALL", "FALL", "FALL", "FALL", "RISE"]
                self.logger.info(f"🔍 {symbol}: Checking 4F+1R pattern (6 points = 5 movements)")
            else:
                self.logger.info(f"❌ {symbol}: Invalid data length: {len(data_points)} points, {len(trends)} trends (need 6 points, 5 trends)")
                return False

            # 🎯 DETAILED PATTERN MATCHING FOR DEBUGGING
            self.logger.info(f"🔍 {symbol}: Expected pattern: {expected_pattern}")
            self.logger.info(f"🔍 {symbol}: Actual pattern:   {trends}")

            # Check if pattern matches EXACTLY
            if trends == expected_pattern:
                self.logger.info(f"✅ {symbol}: PATTERN MATCH CONFIRMED!")

                # Check 0.5% drop condition
                start_price = prices[0]  # First price (earliest in time)
                lowest_price = min(prices[:-1])  # Lowest price before the final rise
                final_price = prices[-1]  # Final price (latest in time)

                # Calculate percentage drop
                percentage_drop = ((start_price - lowest_price) / start_price) * 100

                self.logger.info(f"📉 {symbol}: FORWARD-ONLY Drop analysis:")
                self.logger.info(f"📉 {symbol}: Start: ₹{start_price:.2f} → Lowest: ₹{lowest_price:.2f} → Final: ₹{final_price:.2f}")
                self.logger.info(f"📉 {symbol}: Drop: {percentage_drop:.2f}% (Required: ≥{self.min_drop_percentage}%)")

                # Use standard 0.5% drop requirement for TRUE 4F+1R pattern
                min_drop = self.min_drop_percentage  # Always 0.5% for TRUE pattern

                if percentage_drop >= min_drop:
                    # Calculate correct pattern name: 5 trends = 4F+1R, 4 trends = 3F+1R, etc.
                    falls_count = len(expected_pattern) - 1  # Subtract 1 for the RISE
                    pattern_name = f"{falls_count}F+1R"
                    self.logger.info(f"✅ {symbol}: {pattern_name} PATTERN CONFIRMED! Drop: {percentage_drop:.2f}% ≥ {min_drop}%")

                    # Log the TRUE 4F+1R detection
                    if falls_count == 4:
                        self.logger.info(f"🎯 {symbol}: TRUE 4F+1R PATTERN DETECTED - GENERATING BUY SIGNAL!")

                    return True
                else:
                    self.logger.info(f"❌ {symbol}: Pattern found but insufficient drop: {percentage_drop:.2f}% < {min_drop}%")
                    return False
            else:
                # 🎯 DETAILED MISMATCH ANALYSIS
                self.logger.info(f"❌ {symbol}: PATTERN MISMATCH ANALYSIS:")
                for i, (expected, actual) in enumerate(zip(expected_pattern, trends)):
                    match_status = "✅" if expected == actual else "❌"
                    self.logger.info(f"   {match_status} Movement {i+1}: Expected {expected}, Got {actual}")

                # Special check for HBLENGINE-like patterns
                if symbol == "HBLENGINE" or "HBL" in symbol:
                    self.logger.info(f"🔍 {symbol}: SPECIAL HBLENGINE ANALYSIS:")
                    self.logger.info(f"   📊 Prices: {prices}")
                    self.logger.info(f"   📊 Trends: {trends}")
                    self.logger.info(f"   📊 Expected: {expected_pattern}")

                return False

            return False

        except Exception as e:
            self.logger.error(f"❌ Error in pattern detection for {symbol}: {e}")
            return False

    def _create_buy_signal(self, symbol: str, price: float, data_points: List[Dict]) -> TradingSignal:
        """Create BUY signal with pattern information"""
        pattern_info = {
            'pattern_type': '4F+1R',
            'drop_percentage': self._calculate_drop_percentage(data_points),
            'data_points': len(data_points),
            'detection_time': datetime.now().isoformat()
        }

        return TradingSignal(
            symbol=symbol,
            signal_type='BUY',
            price=price,
            timestamp_ns=time.time_ns(),
            pattern_info=pattern_info,
            source='DB1'
        )

    def _save_signal_to_database(self, signal: TradingSignal) -> bool:
        """Save trading signal to database"""
        try:
            conn = sqlite3.connect(self.trading_db_path)
            cursor = conn.cursor()

            # Ensure trading_signals table exists with correct structure
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS trading_signals (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                signal_type TEXT NOT NULL,
                price REAL NOT NULL,
                timestamp DATETIME NOT NULL,
                pattern_sequence TEXT,
                source TEXT DEFAULT 'DB1',
                executed BOOLEAN DEFAULT FALSE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            # Convert pattern_info to JSON string
            pattern_sequence = str(signal.pattern_info) if signal.pattern_info else ""

            # Insert signal into trading_signals table
            cursor.execute('''
            INSERT INTO trading_signals
            (symbol, signal_type, price, timestamp, pattern_sequence, source)
            VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                signal.symbol,
                signal.signal_type,
                signal.price,
                datetime.now().isoformat(),
                pattern_sequence,
                signal.source
            ))

            conn.commit()
            conn.close()

            self.logger.info(f"💾 Signal saved to database: {signal.signal_type} {signal.symbol} @ ₹{signal.price:.2f}")
            return True

        except Exception as e:
            self.logger.error(f"❌ Error saving signal to database: {e}")
            return False
    
    def _create_sell_signal(self, symbol: str, price: float, position: ActivePosition, profit: float) -> TradingSignal:
        """Create SELL signal with position information"""
        pattern_info = {
            'pattern_type': 'PROFIT_TARGET',
            'current_profit': profit,
            'target_profit': self.profit_target,
            'buy_price': position.buy_price,
            'profit_percentage': (profit / position.investment) * 100,
            'detection_time': datetime.now().isoformat()
        }
        
        return TradingSignal(
            symbol=symbol,
            signal_type='SELL',
            price=price,
            timestamp_ns=time.time_ns(),
            pattern_info=pattern_info,
            source='DB1'
        )
    
    def _calculate_drop_percentage(self, data_points: List[Dict]) -> float:
        """Calculate percentage drop from start to lowest point"""
        prices = [point['close_price'] for point in reversed(data_points)]
        start_price = prices[0]
        lowest_price = min(prices[:-1])
        return ((start_price - lowest_price) / start_price) * 100
    
    def _get_all_symbols(self) -> List[str]:
        """Get all symbols for analysis"""
        try:
            from symbol_manager import SymbolManager
            symbol_manager = SymbolManager()
            return symbol_manager.get_all_symbols()
        except Exception as e:
            self.logger.error(f"❌ Error getting symbols: {e}")
            return []
    
    def _get_latest_data_points(self, symbol: str, count: int = 6) -> List[Dict]:
        """Get latest N data points for a symbol"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            query = '''
            SELECT close_price, timestamp, open_price, high_price, low_price, volume
            FROM trading_data 
            WHERE symbol = ? 
            ORDER BY timestamp DESC 
            LIMIT ?
            '''
            
            df = pd.read_sql_query(query, conn, params=(symbol, count))
            conn.close()
            
            if df.empty:
                return []
            
            # Convert to list of dictionaries
            return df.to_dict('records')
            
        except Exception as e:
            self.logger.error(f"❌ Error getting data for {symbol}: {e}")
            return []
    
    def _get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price for a symbol"""
        try:
            latest_data = self._get_latest_data_points(symbol, count=1)
            if latest_data:
                return latest_data[0]['close_price']
            return None
        except Exception as e:
            self.logger.error(f"❌ Error getting current price for {symbol}: {e}")
            return None
    
    def _create_pending_position(self, symbol: str, price: float):
        """Create pending position entry for BUY signal"""
        try:
            # Calculate position details
            quantity = int(self.investment_per_symbol / price)
            actual_investment = quantity * price
            target_price = price + (self.profit_target / quantity)

            # Store in database as PENDING (will be activated by DB2)
            conn = sqlite3.connect(self.trading_db_path)
            cursor = conn.cursor()

            cursor.execute('''
            INSERT INTO trading_positions
            (symbol, buy_price, shares_quantity, investment, target_value, target_price, buy_time, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (symbol, price, quantity, actual_investment,
                  actual_investment + self.profit_target, target_price,
                  datetime.now().isoformat(), 'PENDING'))

            conn.commit()
            conn.close()

            self.logger.info(f"💾 Created pending position: {symbol} @ ₹{price:.2f} (Qty: {quantity})")

        except Exception as e:
            self.logger.error(f"❌ Error creating pending position for {symbol}: {e}")

    def detect_rolling_window_pattern_realtime(self, symbol: str, new_price: float, timestamp: str) -> bool:
        """
        SIMPLE ROLLING WINDOW 4F+1R Pattern Detection
        Tracks last 4 F/R states + new F/R = 5 total
        If pattern = [F, F, F, F, R] → BUY SIGNAL
        If not → IGNORE, NEXT SYMBOL
        """
        try:
            # Initialize rolling window for symbol if not exists
            if symbol not in self.rolling_window_data:
                self.rolling_window_data[symbol] = {
                    'previous_price': None,
                    'fr_pattern': [],  # Store last 4 F/R states
                    'last_update': None
                }

            window_data = self.rolling_window_data[symbol]

            # If no previous price, store current and return
            if window_data['previous_price'] is None:
                window_data['previous_price'] = new_price
                window_data['last_update'] = timestamp
                self.logger.info(f"🔄 {symbol}: Initialized @ ₹{new_price:.2f}")
                return False

            # Calculate F or R: Previous → Current
            previous_price = window_data['previous_price']

            if new_price > previous_price:
                current_fr = 'R'
            elif new_price < previous_price:
                current_fr = 'F'
            else:
                current_fr = 'FLAT'
                # Skip FLAT movements
                window_data['previous_price'] = new_price
                window_data['last_update'] = timestamp
                return False

            # Update rolling window: Keep last 4 + add new = 5 total
            window_data['fr_pattern'].append(current_fr)
            if len(window_data['fr_pattern']) > 4:
                window_data['fr_pattern'].pop(0)  # Remove oldest

            # Log current state
            pattern_str = " → ".join(window_data['fr_pattern'])
            self.logger.info(f"🔄 {symbol}: ₹{previous_price:.2f} → ₹{new_price:.2f} = {current_fr}")
            self.logger.info(f"📊 {symbol}: Pattern: [{pattern_str}]")

            # Check for 4F+1R pattern ONLY when we have 4 states + new R
            if len(window_data['fr_pattern']) == 4 and current_fr == 'R':
                # Check if last 3 + current = [F, F, F, R] (3F+1R for now, will extend to 4F+1R)
                if window_data['fr_pattern'] == ['F', 'F', 'F', 'R']:
                    # Calculate price drop for validation
                    # We need to get the start price from 4 data points ago
                    start_price = self._get_price_n_intervals_ago(symbol, 3)  # 3 intervals ago
                    if start_price:
                        percentage_drop = ((start_price - new_price) / start_price) * 100

                        self.logger.info(f"🎯 {symbol}: 3F+1R PATTERN DETECTED!")
                        self.logger.info(f"📉 {symbol}: Drop: ₹{start_price:.2f} → ₹{new_price:.2f} = {percentage_drop:.2f}%")

                        # Check 0.5% drop condition
                        if percentage_drop >= self.min_drop_percentage:
                            self.logger.info(f"✅ {symbol}: 3F+1R PATTERN CONFIRMED! Drop: {percentage_drop:.2f}% ≥ {self.min_drop_percentage}%")

                            # Reset pattern after signal
                            window_data['fr_pattern'] = []
                            window_data['previous_price'] = new_price
                            window_data['last_update'] = timestamp

                            return True
                        else:
                            self.logger.info(f"❌ {symbol}: Pattern found but insufficient drop: {percentage_drop:.2f}% < {self.min_drop_percentage}%")

                # If not the right pattern, just log and continue
                self.logger.info(f"❌ {symbol}: Pattern mismatch. Expected: [F, F, F, R], Got: {window_data['fr_pattern']}")

            # Update previous price for next iteration
            window_data['previous_price'] = new_price
            window_data['last_update'] = timestamp

            return False

        except Exception as e:
            self.logger.error(f"❌ Error in rolling window pattern detection for {symbol}: {e}")
            return False

    def _get_price_n_intervals_ago(self, symbol: str, n: int) -> float:
        """Get price from n intervals ago for drop calculation"""
        try:
            import sqlite3

            conn = sqlite3.connect('Data/trading_data.db')
            cursor = conn.cursor()

            cursor.execute('''
            SELECT close_price FROM trading_data
            WHERE symbol = ?
            ORDER BY timestamp DESC
            LIMIT 1 OFFSET ?
            ''', (symbol, n))

            row = cursor.fetchone()
            conn.close()

            return row[0] if row else None

        except Exception as e:
            self.logger.error(f"❌ Error getting price {n} intervals ago for {symbol}: {e}")
            return None

    def _get_historical_data_for_rolling_window(self, symbol: str, count: int = 5) -> List[Dict]:
        """Get REAL historical data for rolling window pattern detection"""
        try:
            import sqlite3

            conn = sqlite3.connect('Data/trading_data.db')
            cursor = conn.cursor()

            cursor.execute('''
            SELECT close_price, timestamp, open_price, high_price, low_price, volume
            FROM trading_data
            WHERE symbol = ?
            ORDER BY timestamp DESC
            LIMIT ?
            ''', (symbol, count))

            rows = cursor.fetchall()
            conn.close()

            # Convert to list of dictionaries (most recent first)
            historical_data = []
            for row in rows:
                historical_data.append({
                    'close_price': row[0],
                    'timestamp': row[1],
                    'open_price': row[2],
                    'high_price': row[3],
                    'low_price': row[4],
                    'volume': row[5]
                })

            return historical_data

        except Exception as e:
            self.logger.error(f"❌ Error getting historical data for {symbol}: {e}")
            return []

    def _build_rolling_window_from_real_data(self, symbol: str, historical_data: List[Dict], current_price: float, timestamp: str) -> bool:
        """
        Build rolling window pattern from REAL historical data
        This is the CORE TRADING LOGIC you want!
        """
        try:
            # Extract prices in chronological order (oldest first)
            prices = [point['close_price'] for point in reversed(historical_data)]

            # Add current price to the end
            prices.append(current_price)

            # Calculate F/R pattern from price movements
            fr_pattern = []
            for i in range(1, len(prices)):
                if prices[i] > prices[i-1]:
                    fr_pattern.append('R')
                elif prices[i] < prices[i-1]:
                    fr_pattern.append('F')
                else:
                    fr_pattern.append('FLAT')

            # Log the real data analysis
            price_str = " → ".join([f"₹{p:.2f}" for p in prices])
            pattern_str = " → ".join(fr_pattern)

            self.logger.info(f"📊 {symbol}: REAL DATA: {price_str}")
            self.logger.info(f"📊 {symbol}: PATTERN: {pattern_str}")

            # Check for 4F+1R pattern (need at least 5 movements)
            if len(fr_pattern) >= 5:
                # Check last 5 movements for 4F+1R
                last_5_movements = fr_pattern[-5:]

                if last_5_movements == ['F', 'F', 'F', 'F', 'R']:
                    # Calculate price drop
                    start_price = prices[-6]  # 6 prices ago (start of 4 falls)
                    lowest_price = min(prices[-5:-1])  # Lowest during the 4 falls
                    percentage_drop = ((start_price - lowest_price) / start_price) * 100

                    self.logger.info(f"🎯 {symbol}: TRUE 4F+1R PATTERN DETECTED!")
                    self.logger.info(f"📉 {symbol}: Drop: ₹{start_price:.2f} → ₹{lowest_price:.2f} = {percentage_drop:.2f}%")

                    # Check 0.5% drop condition
                    if percentage_drop >= self.min_drop_percentage:
                        self.logger.info(f"✅ {symbol}: 4F+1R PATTERN CONFIRMED! Drop: {percentage_drop:.2f}% ≥ {self.min_drop_percentage}%")
                        return True
                    else:
                        self.logger.info(f"❌ {symbol}: Pattern found but insufficient drop: {percentage_drop:.2f}% < {self.min_drop_percentage}%")

                elif len(fr_pattern) >= 4:
                    # Check for 3F+1R pattern as fallback
                    last_4_movements = fr_pattern[-4:]
                    if last_4_movements == ['F', 'F', 'F', 'R']:
                        start_price = prices[-5]
                        lowest_price = min(prices[-4:-1])
                        percentage_drop = ((start_price - lowest_price) / start_price) * 100

                        self.logger.info(f"🎯 {symbol}: 3F+1R PATTERN DETECTED!")
                        self.logger.info(f"📉 {symbol}: Drop: ₹{start_price:.2f} → ₹{lowest_price:.2f} = {percentage_drop:.2f}%")

                        if percentage_drop >= self.min_drop_percentage:
                            self.logger.info(f"✅ {symbol}: 3F+1R PATTERN CONFIRMED! Drop: {percentage_drop:.2f}% ≥ {self.min_drop_percentage}%")
                            return True
                        else:
                            self.logger.info(f"❌ {symbol}: Pattern found but insufficient drop: {percentage_drop:.2f}% < {self.min_drop_percentage}%")

                # Log pattern mismatch
                self.logger.info(f"❌ {symbol}: Pattern mismatch. Expected: [F, F, F, F, R] or [F, F, F, R], Got: {last_5_movements if len(fr_pattern) >= 5 else fr_pattern}")

            else:
                self.logger.info(f"📊 {symbol}: Insufficient movements ({len(fr_pattern)}/5) for pattern detection")

            return False

        except Exception as e:
            self.logger.error(f"❌ Error building rolling window for {symbol}: {e}")
            return False

    def _get_latest_data_for_symbol(self, symbol: str, limit: int = 5) -> List[Dict]:
        """Get latest data for symbol (alias for compatibility)"""
        return self._get_latest_data_points(symbol, count=limit)

    def process_new_data_point_for_trading(self, symbol: str, latest_data_points: List[Dict]) -> bool:
        """
        PROCESS EVERY NEW DATA POINT FOR TRADING (FROM POINT 1)
        Called EVERY TIME new data arrives for a symbol
        Builds 4F+1R pattern incrementally
        """
        try:
            # Get current and previous data points
            current_data = latest_data_points[0]  # Most recent
            previous_data = latest_data_points[1]  # Previous

            current_price = current_data['close_price']
            previous_price = previous_data['close_price']
            timestamp = current_data.get('timestamp', datetime.now().isoformat())

            # Calculate F or R movement
            if current_price > previous_price:
                movement = 'R'
            elif current_price < previous_price:
                movement = 'F'
            else:
                movement = 'FLAT'
                # Skip FLAT movements
                return False

            # Initialize rolling window for symbol if not exists
            if symbol not in self.rolling_window_data:
                self.rolling_window_data[symbol] = {
                    'fr_pattern': [],  # Store F/R movements
                    'price_history': [],  # Store prices for drop calculation
                    'last_update': None
                }

            window_data = self.rolling_window_data[symbol]

            # Add new movement to rolling window
            window_data['fr_pattern'].append(movement)
            window_data['price_history'].append(current_price)
            window_data['last_update'] = timestamp

            # Keep only last 5 movements (for 4F+1R pattern)
            if len(window_data['fr_pattern']) > 5:
                window_data['fr_pattern'].pop(0)
                window_data['price_history'].pop(0)

            # 💾 SAVE TO DATABASE FOR PERSISTENCE
            self._save_rolling_window_data_to_database(symbol)

            # Log the movement and current pattern
            pattern_str = " → ".join(window_data['fr_pattern'])
            self.logger.info(f"🔄 {symbol}: ₹{previous_price:.2f} → ₹{current_price:.2f} = {movement}")
            self.logger.info(f"📊 {symbol}: Pattern: [{pattern_str}] ({len(window_data['fr_pattern'])}/5)")

            # Check for 4F+1R pattern when we have 5 movements
            if len(window_data['fr_pattern']) == 5:
                if window_data['fr_pattern'] == ['F', 'F', 'F', 'F', 'R']:
                    # Calculate price drop from start to lowest point
                    prices = window_data['price_history']
                    start_price = prices[0]  # Price at start of pattern
                    lowest_price = min(prices[:-1])  # Lowest during the 4 falls (exclude final rise)
                    percentage_drop = ((start_price - lowest_price) / start_price) * 100

                    self.logger.info(f"🎯 {symbol}: TRUE 4F+1R PATTERN DETECTED!")
                    self.logger.info(f"📉 {symbol}: Drop: ₹{start_price:.2f} → ₹{lowest_price:.2f} = {percentage_drop:.2f}%")

                    # Check 0.5% drop condition
                    if percentage_drop >= self.min_drop_percentage:
                        self.logger.info(f"✅ {symbol}: 4F+1R PATTERN CONFIRMED! Drop: {percentage_drop:.2f}% ≥ {self.min_drop_percentage}%")

                        # Generate BUY signal
                        return self._generate_buy_signal(symbol, current_price, timestamp, percentage_drop)
                    else:
                        self.logger.info(f"❌ {symbol}: Pattern found but insufficient drop: {percentage_drop:.2f}% < {self.min_drop_percentage}%")
                else:
                    # Pattern mismatch
                    expected = ['F', 'F', 'F', 'F', 'R']
                    self.logger.info(f"❌ {symbol}: Pattern mismatch. Expected: {expected}, Got: {window_data['fr_pattern']}")

            return False

        except Exception as e:
            self.logger.error(f"❌ Error processing new data point for {symbol}: {e}")
            return False

    def initialize_symbol_for_trading(self, symbol: str, first_data_point: Dict) -> None:
        """Initialize symbol for trading with first data point"""
        try:
            price = first_data_point['close_price']
            timestamp = first_data_point.get('timestamp', datetime.now().isoformat())

            # Initialize rolling window
            self.rolling_window_data[symbol] = {
                'fr_pattern': [],
                'price_history': [price],
                'last_update': timestamp
            }

            self.logger.info(f"🔄 {symbol}: Initialized for trading @ ₹{price:.2f}")

        except Exception as e:
            self.logger.error(f"❌ Error initializing {symbol} for trading: {e}")

    def _generate_buy_signal(self, symbol: str, price: float, timestamp: str, drop_percentage: float) -> bool:
        """Generate BUY signal and send to DB2"""
        try:
            self.logger.info(f"🎯 GENERATING BUY SIGNAL for {symbol} @ ₹{price:.2f}")

            # Create signal data
            signal_data = {
                'symbol': symbol,
                'signal_type': 'BUY',
                'price': price,
                'timestamp_ns': time.time_ns(),
                'pattern_info': {
                    'pattern_type': '4F+1R',
                    'detection_method': 'incremental_realtime',
                    'detection_time': timestamp,
                    'drop_percentage': drop_percentage
                },
                'source': 'DB1_REALTIME'
            }

            # Send signal to DB2
            signal = TradingSignal(**signal_data)
            if self.communicator.send_signal_to_db2(signal):
                self.logger.info(f"✅ BUY SIGNAL SENT TO DB2: {symbol} @ ₹{price:.2f}")
                self.logger.info(f"📤 DB1→DB2 Signal transmission successful")

                # Create pending position
                self._create_pending_position(symbol, price)

                # Reset pattern after signal generation
                if symbol in self.rolling_window_data:
                    self.rolling_window_data[symbol]['fr_pattern'] = []
                    self.rolling_window_data[symbol]['price_history'] = [price]
                    self.rolling_window_data[symbol]['last_update'] = timestamp

                    # 💾 SAVE RESET PATTERN TO DATABASE
                    self._save_rolling_window_data_to_database(symbol)

                return True
            else:
                self.logger.error(f"❌ Failed to send BUY signal to DB2 for {symbol}")
                return False

        except Exception as e:
            self.logger.error(f"❌ Error generating BUY signal for {symbol}: {e}")
            return False

    def get_status(self) -> Dict[str, any]:
        """Get current status of DB1 signal generator"""
        return {
            'active_positions': len(self.active_positions),
            'priority_categories': {k: len(v) for k, v in self.priority_categories.items()},
            'symbol_patterns_tracked': len(self.symbol_patterns),
            'rolling_window_symbols': len(self.rolling_window_data),
            'communicator_status': self.communicator.get_queue_status(),
            'performance_stats': self.communicator.get_performance_stats(),
            'last_update': datetime.now().isoformat()
        }


# Global instance for easy access
_db1_signal_generator = None

def get_db1_signal_generator():
    """Get global DB1 signal generator instance"""
    global _db1_signal_generator
    if _db1_signal_generator is None:
        _db1_signal_generator = DB1_SignalGenerator()
    return _db1_signal_generator
