# Clean Dual-Database Trading System: Technical Guide

## Project Overview

This system is a high-performance trading platform that uses a dual-database architecture to separate signal generation from trade execution. It processes market data for 224 symbols at 1-hour intervals, detects specific price patterns (4F+1R), and executes paper trades with an ₹800 profit target.

## Technical Architecture

### Core Components

1. **DB1 (Signal Generation & Data Storage)**
   - Fetches 1-hour interval data from Angel One API
   - Stores all market data in structured database
   - Detects 4F+1R patterns (four consecutive price falls followed by one rise)
   - Generates BUY signals when patterns are detected
   - Monitors positions for ₹800 profit target

2. **DB2 (Trade Execution)**
   - Operates independently on 2-minute intervals
   - Applies rolling window confirmations (RR/FF patterns)
   - Executes paper trades based on confirmed signals
   - Tracks trade performance and statistics

3. **Communication Layer**
   - Millisecond-level communication between DB1 and DB2
   - Transmits signals and position updates
   - Ensures data consistency across databases

4. **Web Interface**
   - Flask-based dashboard with 3 tabs
   - Real-time P&L calculations
   - Trading operations summary
   - Paper trading records

### Symbol Priority System

The system implements a GOLD/SILVER/BRONZE priority system:

1. **GOLD Tier**
   - Highest priority symbols (top 20-30)
   - Always processed first
   - Most reliable data fetching
   - Critical for system performance

2. **SILVER Tier**
   - Medium priority symbols (next 50-70)
   - Processed after GOLD tier
   - Regular monitoring and analysis

3. **BRONZE Tier**
   - Remaining symbols (100+)
   - Processed last
   - Less frequent analysis

### Performance Metrics

- Signal Generation: <150ms for all 224 symbols
- DB1→DB2 Communication: <50ms
- DB2 Execution: <20ms
- Total System Response: <300ms
- Database Size: DB1 (2.6 MB), DB2 (0.1 MB)

## Data Flow

1. **Data Acquisition**
   - Fetch 1-hour interval data from Angel One API
   - Store directly in SQLite database (DB1)
   - Check for missing data points and fill gaps
   - Apply GOLD/SILVER/BRONZE priority for fetching

2. **Pattern Detection**
   - Calculate F/R movements at data insertion
   - Track rolling window patterns for each symbol
   - Detect 4F+1R patterns for BUY signals

3. **Signal Transmission**
   - Send BUY signals from DB1 to DB2
   - Include price, timestamp, and pattern information

4. **Trade Execution**
   - Apply 2-minute rolling window confirmations
   - Execute paper trades for confirmed signals
   - Monitor positions for ₹800 profit target
   - Generate SELL signals when target reached

5. **Performance Tracking**
   - Calculate real-time P&L
   - Track trade statistics
   - Generate performance reports

## Recommended File Structure for New Projects

```
trading_system/
├── config/
│   ├── config.py                    # Configuration management
│   ├── api_config.json              # API credentials
│   └── trading_params.py            # Trading parameters
│
├── data/
│   ├── db/
│   │   ├── signal_db.sqlite         # DB1: Signal generation database
│   │   └── execution_db.sqlite      # DB2: Trade execution database
│   ├── symbols/
│   │   ├── symbol_list.xlsx         # Trading symbols list with priority tiers
│   │   └── token_mapping.json       # Symbol to token mapping
│   └── logs/
│       └── trading_logs/            # Application logs
│
├── core/
│   ├── data_management/
│   │   ├── data_fetcher.py          # API data fetching with priority handling
│   │   ├── data_integrity.py        # Missing data detection
│   │   └── database_manager.py      # Database operations
│   │
│   ├── signal_generation/
│   │   ├── pattern_detector.py      # F/R pattern detection
│   │   ├── signal_generator.py      # Trading signal generation
│   │   └── position_manager.py      # Position management
│   │
│   ├── trade_execution/
│   │   ├── confirmation_engine.py   # Signal confirmation
│   │   ├── trade_executor.py        # Paper trade execution
│   │   └── profit_monitor.py        # Profit target monitoring
│   │
│   └── communication/
│       └── db_communicator.py       # Inter-database communication
│
├── web/
│   ├── app.py                       # Flask application
│   ├── routes/
│   │   ├── api_routes.py            # API endpoints
│   │   └── view_routes.py           # Web view routes
│   ├── templates/
│   │   ├── dashboard.html           # Main dashboard
│   │   ├── positions.html           # Active positions view
│   │   └── trades.html              # Trade history view
│   └── static/
│       ├── css/                     # Stylesheets
│       └── js/                      # JavaScript files
│
├── utils/
│   ├── logging_utils.py             # Logging utilities
│   ├── time_utils.py                # Time management utilities
│   └── symbol_utils.py              # Symbol processing utilities
│
├── tests/
│   ├── test_data_fetcher.py         # Data fetching tests
│   ├── test_pattern_detector.py     # Pattern detection tests
│   └── test_trade_executor.py       # Trade execution tests
│
├── main.py                          # Main entry point
├── requirements.txt                 # Python dependencies
└── README.md                        # Project documentation
```

## Implementation Guide for New Projects

### 1. Data Management with Priority Tiers

```python
# data_fetcher.py - Core data fetching logic with priority tiers
def fetch_data_with_priority(symbols_by_tier, interval="ONE_HOUR"):
    """
    Fetch data for all symbols at specified interval
    Process in GOLD → SILVER → BRONZE order
    """
    results = {"GOLD": {"success": 0, "failed": 0},
               "SILVER": {"success": 0, "failed": 0},
               "BRONZE": {"success": 0, "failed": 0}}
    
    # Process GOLD tier first (highest priority)
    for tier in ["GOLD", "SILVER", "BRONZE"]:
        for symbol in symbols_by_tier[tier]:
            try:
                # Fetch data from API
                data = api.get_historical_data(symbol, interval)
                
                # Calculate F/R at insertion point
                for candle in data:
                    fr_value = calculate_fr(candle, previous_candle)
                    store_with_fr(symbol, candle, fr_value)
                
                results[tier]["success"] += 1
            except Exception as e:
                results[tier]["failed"] += 1
                log_error(f"Failed to fetch {tier} symbol {symbol}: {e}")
    
    return results
```

### 2. Pattern Detection

```python
# pattern_detector.py - F/R pattern detection
def detect_patterns(db_connection):
    """
    Detect 4F+1R patterns using direct SQL query
    Much faster than iterative approach
    """
    query = """
    SELECT symbol, timestamp, close_price FROM (
        SELECT symbol, timestamp, close_price,
        LAG(fr_value, 1) OVER (PARTITION BY symbol ORDER BY timestamp) as fr1,
        LAG(fr_value, 2) OVER (PARTITION BY symbol ORDER BY timestamp) as fr2,
        LAG(fr_value, 3) OVER (PARTITION BY symbol ORDER BY timestamp) as fr3,
        LAG(fr_value, 4) OVER (PARTITION BY symbol ORDER BY timestamp) as fr4,
        fr_value as fr5
        FROM trading_data
    ) WHERE fr1 = 'F' AND fr2 = 'F' AND fr3 = 'F' AND fr4 = 'F' AND fr5 = 'R'
    """
    return db_connection.execute(query).fetchall()
```

### 3. Signal Generation

```python
# signal_generator.py - Trading signal generation
def generate_signals(patterns):
    """
    Generate BUY signals from detected patterns
    """
    signals = []
    for pattern in patterns:
        signal = {
            'symbol': pattern['symbol'],
            'signal_type': 'BUY',
            'price': pattern['close_price'],
            'timestamp': pattern['timestamp'],
            'pattern': '4F+1R'
        }
        signals.append(signal)
    return signals
```

### 4. DB1-DB2 Communication

```python
# db_communicator.py - Inter-database communication
def send_signal_to_db2(signal):
    """
    Send signal from DB1 to DB2
    """
    try:
        # Convert to standardized format
        signal_data = {
            'symbol': signal['symbol'],
            'signal_type': signal['signal_type'],
            'price': signal['price'],
            'timestamp_ns': time.time_ns(),
            'pattern_info': {
                'pattern_type': signal['pattern'],
                'detection_time': signal['timestamp']
            },
            'source': 'DB1'
        }
        
        # Store in DB2 signal queue
        conn = sqlite3.connect('data/db/execution_db.sqlite')
        cursor = conn.cursor()
        cursor.execute('''
        INSERT INTO signal_queue
        (symbol, signal_type, price, timestamp, pattern_info, source)
        VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            signal_data['symbol'],
            signal_data['signal_type'],
            signal_data['price'],
            signal_data['timestamp_ns'],
            json.dumps(signal_data['pattern_info']),
            signal_data['source']
        ))
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        log_error(f"Failed to send signal to DB2: {e}")
        return False
```

### 5. Trade Execution in DB2

```python
# trade_executor.py - Paper trade execution
def execute_trades(confirmation_engine):
    """
    Execute paper trades for confirmed signals
    """
    # Get pending signals from queue
    conn = sqlite3.connect('data/db/execution_db.sqlite')
    cursor = conn.cursor()
    cursor.execute('''
    SELECT * FROM signal_queue
    WHERE processed = 0
    ORDER BY timestamp ASC
    ''')
    pending_signals = cursor.fetchall()
    conn.close()
    
    for signal_data in pending_signals:
        signal = {
            'symbol': signal_data[1],
            'signal_type': signal_data[2],
            'price': signal_data[3],
            'timestamp': signal_data[4],
            'pattern_info': json.loads(signal_data[5])
        }
        
        # Apply rolling window confirmation
        if confirmation_engine.confirm_signal(signal):
            # Execute paper trade
            trade = {
                'symbol': signal['symbol'],
                'entry_price': signal['price'],
                'quantity': calculate_quantity(signal['price']),
                'timestamp': datetime.now(),
                'status': 'ACTIVE'
            }
            store_trade(trade)
            
            # Mark signal as processed
            mark_signal_processed(signal_data[0])
```

### 6. Profit Monitoring

```python
# profit_monitor.py - Profit target monitoring
def monitor_positions(db_connection, profit_target=800):
    """
    Monitor active positions for profit target
    Generate SELL signals when target reached
    """
    active_positions = get_active_positions(db_connection)
    for position in active_positions:
        current_price = get_current_price(position['symbol'])
        profit = calculate_profit(position, current_price)
        
        if profit >= profit_target:
            generate_sell_signal(position, current_price)
```

## Database Schema

### DB1 (Signal Generation & Data Storage)

```sql
CREATE TABLE trading_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol TEXT NOT NULL,
    token TEXT NOT NULL,
    timestamp DATETIME NOT NULL,
    open_price REAL NOT NULL,
    high_price REAL NOT NULL,
    low_price REAL NOT NULL,
    close_price REAL NOT NULL,
    volume INTEGER NOT NULL,
    fr_value TEXT,  -- 'F', 'R', or 'FLAT'
    symbol_tier TEXT, -- 'GOLD', 'SILVER', 'BRONZE'
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, timestamp)
);

CREATE TABLE symbol_priority (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol TEXT NOT NULL UNIQUE,
    tier TEXT NOT NULL, -- 'GOLD', 'SILVER', 'BRONZE'
    priority_score REAL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE active_positions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol TEXT NOT NULL,
    entry_price REAL NOT NULL,
    quantity INTEGER NOT NULL,
    entry_timestamp DATETIME NOT NULL,
    target_price REAL NOT NULL,
    status TEXT DEFAULT 'ACTIVE',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE trading_signals (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol TEXT NOT NULL,
    signal_type TEXT NOT NULL,
    price REAL NOT NULL,
    timestamp DATETIME NOT NULL,
    pattern_sequence TEXT,
    source TEXT DEFAULT 'DB1',
    executed BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### DB2 (Trade Execution)

```sql
CREATE TABLE signal_queue (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol TEXT NOT NULL,
    signal_type TEXT NOT NULL,
    price REAL NOT NULL,
    timestamp BIGINT NOT NULL,
    pattern_info TEXT,
    source TEXT DEFAULT 'DB1',
    processed BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE paper_trades (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol TEXT NOT NULL,
    entry_price REAL NOT NULL,
    exit_price REAL,
    quantity INTEGER NOT NULL,
    entry_timestamp DATETIME NOT NULL,
    exit_timestamp DATETIME,
    profit_loss REAL,
    status TEXT DEFAULT 'ACTIVE',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE rolling_window_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol TEXT NOT NULL,
    timestamp DATETIME NOT NULL,
    price REAL NOT NULL,
    fr_value TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE portfolio_status (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    total_pot REAL NOT NULL,
    vault_amount REAL NOT NULL,
    active_positions INTEGER NOT NULL,
    completed_trades INTEGER NOT NULL,
    total_profit REAL NOT NULL,
    updated_at DATETIME NOT NULL
);
```

## Trading Strategies

### 1. 4F+1R Pattern (DB1)

- **Description**: Four consecutive price falls followed by one rise
- **Implementation**: Track F/R movements in database, detect pattern with SQL
- **Signal Generation**: Generate BUY signal when pattern detected
- **Profit Target**: ₹800 per trade
- **Priority Handling**: Apply GOLD/SILVER/BRONZE tiers for processing order

### 2. Rolling Window Confirmation (DB2)

- **Description**: 2-minute RR/FF confirmations
- **Implementation**: Track 2-minute price movements
- **Confirmation Rules**:
  - RR (Rise-Rise) confirms BUY signals
  - FF (Fall-Fall) confirms SELL signals
- **Trade Execution**: Execute paper trades for confirmed signals

## Best Practices

1. **Data Integrity**
   - Always check for missing data points
   - Fill gaps before pattern detection
   - Validate data quality before processing

2. **Performance Optimization**
   - Use direct SQL queries for pattern detection
   - Calculate F/R at data insertion point
   - Implement database indexing for faster queries
   - Process symbols by priority tier (GOLD → SILVER → BRONZE)

3. **Error Handling**
   - Implement comprehensive logging
   - Handle API connection failures gracefully
   - Retry failed operations with exponential backoff
   - Critical alerts for GOLD tier failures

4. **System Monitoring**
   - Track API rate limits
   - Monitor database performance
   - Log all signal generation and trade execution
   - Priority-based monitoring (more attention to GOLD tier)

5. **Testing**
   - Test pattern detection with historical data
   - Validate profit calculations
   - Simulate trading days with historical data
   - Verify priority tier processing

This technical guide provides a comprehensive overview of the dual-database trading system and serves as a blueprint for implementing similar projects in the future.

<EMAIL>
Ashajunnu@123