#!/usr/bin/env python3
"""
DB1 Pattern Detector

This module detects 4F+1R patterns using SQL queries with perfect logic.
Follows consistent naming and structure.
"""

import sqlite3
import logging
from typing import List, Dict, Optional
from datetime import datetime

class DB1PatternDetector:
    """Perfect 4F+1R pattern detector using SQL queries"""
    
    def __init__(self):
        self.db_path = 'Data/trading_data.db'
        self.logger = logging.getLogger(__name__)
        
    def detect_4f1r_pattern(self, symbol: str) -> Optional[Dict]:
        """Detect 4F+1R pattern using SQL queries with perfect logic"""
        try:
            # Get last 6 data points (6 points = 5 movements = 4F+1R)
            recent_data = self._get_last_6_points(symbol)
            
            if len(recent_data) < 6:
                return None
            
            # Extract F/R movements (skip first START record)
            movements = []
            prices = []
            
            for i, record in enumerate(recent_data):
                prices.append(record['close_price'])
                if i > 0:  # Skip first record (START)
                    movements.append(record['fr_movement'])
            
            # Check for exact 4F+1R pattern
            if len(movements) != 5:
                return None
            
            pattern_string = ''.join(movements)
            
            if pattern_string == 'FFFFR':
                # Validate 0.5% drop requirement
                start_price = prices[0]  # First price
                lowest_price = min(prices[1:5])  # Lowest in the 4 falls
                
                drop_percentage = ((start_price - lowest_price) / start_price) * 100
                
                if drop_percentage >= 0.5:
                    # Perfect 4F+1R pattern detected!
                    signal_price = prices[-1]  # Price at R point
                    
                    pattern_info = {
                        'symbol': symbol,
                        'pattern': 'FFFFR',
                        'signal_price': signal_price,
                        'drop_percentage': drop_percentage,
                        'start_price': start_price,
                        'lowest_price': lowest_price,
                        'prices': prices,
                        'movements': movements,
                        'detected_at': datetime.now()
                    }
                    
                    self.logger.info(f"🎯 PERFECT 4F+1R DETECTED: {symbol}")
                    self.logger.info(f"   Pattern: {pattern_string}")
                    self.logger.info(f"   Signal Price: ₹{signal_price:.2f}")
                    self.logger.info(f"   Drop: {drop_percentage:.2f}% (₹{start_price:.2f} → ₹{lowest_price:.2f})")
                    
                    return pattern_info
                else:
                    self.logger.debug(f"   {symbol}: FFFFR pattern but drop {drop_percentage:.2f}% < 0.5%")
                    return None
            else:
                self.logger.debug(f"   {symbol}: Pattern {pattern_string} (not FFFFR)")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Error detecting pattern for {symbol}: {e}")
            return None
    
    def _get_last_6_points(self, symbol: str) -> List[Dict]:
        """Get last 6 data points for pattern analysis"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT timestamp, close_price, fr_movement, previous_close
            FROM trading_data 
            WHERE symbol = ? 
            ORDER BY timestamp DESC 
            LIMIT 6
            ''', (symbol,))
            
            results = cursor.fetchall()
            conn.close()
            
            # Convert to list of dictionaries and reverse to chronological order
            data = []
            for row in reversed(results):
                data.append({
                    'timestamp': row[0],
                    'close_price': row[1],
                    'fr_movement': row[2],
                    'previous_close': row[3]
                })
            
            return data
            
        except Exception as e:
            self.logger.error(f"❌ Error getting data for {symbol}: {e}")
            return []
    
    def analyze_pattern_priority(self, symbol: str) -> str:
        """Analyze pattern for priority classification"""
        try:
            recent_data = self._get_last_6_points(symbol)
            
            if len(recent_data) < 6:
                return 'INSUFFICIENT_DATA'
            
            # Extract movements (skip first START record)
            movements = []
            for i, record in enumerate(recent_data):
                if i > 0:  # Skip first record
                    movements.append(record['fr_movement'])
            
            if len(movements) != 5:
                return 'INSUFFICIENT_MOVEMENTS'
            
            pattern_string = ''.join(movements)
            
            # Priority classification
            if pattern_string == 'FFFFR':
                return 'GOLD'  # Ready for BUY signal
            elif len(pattern_string) == 5 and pattern_string[1:5] == 'FFFF':
                return 'SILVER'  # XFFFF pattern
            elif len(pattern_string) == 5 and pattern_string[2:5] == 'FFF':
                return 'BRONZE'  # XXFFF pattern
            elif len(pattern_string) == 5 and pattern_string[3:5] == 'FF':
                return 'BRONZE'  # XXXFF pattern
            else:
                return 'REMAINING'  # Other patterns
                
        except Exception as e:
            self.logger.error(f"❌ Error analyzing priority for {symbol}: {e}")
            return 'ERROR'
    
    def scan_all_symbols_for_patterns(self) -> List[Dict]:
        """Scan all symbols for 4F+1R patterns"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Get all symbols with at least 6 data points
            cursor.execute('''
            SELECT symbol, COUNT(*) as count 
            FROM trading_data 
            GROUP BY symbol 
            HAVING COUNT(*) >= 6
            ''')
            
            symbols = cursor.fetchall()
            conn.close()
            
            detected_patterns = []
            
            for symbol, count in symbols:
                pattern_info = self.detect_4f1r_pattern(symbol)
                if pattern_info:
                    detected_patterns.append(pattern_info)
            
            return detected_patterns
            
        except Exception as e:
            self.logger.error(f"❌ Error scanning for patterns: {e}")
            return []

# Global instance
db1_pattern_detector = DB1PatternDetector()
