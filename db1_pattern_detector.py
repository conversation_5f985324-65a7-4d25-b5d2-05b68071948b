#!/usr/bin/env python3
"""
DB1 Pattern Detector - PURE SQL OPERATIONS

This module detects 4F+1R patterns using PURE SQL queries.
NO API CALLS - Only SQL operations on existing DB1 data.
FOLLOWS EXACT DOCUMENTATION:
- Last 5 data points for 4F+1R pattern
- 0.5% minimum drop validation
- GOLD/SILVER/BRONZE priority integration
"""

import sqlite3
import logging
from typing import List, Dict, Optional
from datetime import datetime

class DB1PatternDetector:
    """Pure SQL 4F+1R pattern detector"""
    
    def __init__(self):
        self.db_path = 'Data/trading_data.db'
        self.logger = logging.getLogger(__name__)
        
    def detect_4f1r_pattern(self, symbol: str) -> Optional[Dict]:
        """Detect 4F+1R pattern using PURE SQL - EXACT DOCUMENTATION SPECS"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Get last 5 data points for pattern analysis (EXACT DOCUMENTATION)
            cursor.execute('''
            SELECT timestamp, close_price, fr_movement, previous_close
            FROM trading_data 
            WHERE symbol = ? 
            ORDER BY timestamp DESC 
            LIMIT 5
            ''', (symbol,))
            
            results = cursor.fetchall()
            conn.close()
            
            if len(results) < 5:
                return None
            
            # Reverse to get chronological order
            data_points = list(reversed(results))
            
            # Extract F/R movements from the 5 data points
            movements = []
            prices = []
            
            for i, (timestamp, close_price, fr_movement, previous_close) in enumerate(data_points):
                prices.append(close_price)
                if i > 0:  # Skip first point (no movement)
                    movements.append(fr_movement)
            
            # Check for exact 4F+1R pattern (4 movements from 5 points)
            if len(movements) != 4:
                return None
            
            pattern_string = ''.join(movements)
            
            if pattern_string == 'FFFR':  # 4F+1R pattern
                # Validate 0.5% drop requirement (EXACT DOCUMENTATION)
                start_price = prices[0]  # First price
                lowest_price = min(prices[1:4])  # Lowest in the 3 falls (points 2,3,4)
                
                drop_percentage = ((start_price - lowest_price) / start_price) * 100
                
                if drop_percentage >= 0.5:
                    # Perfect 4F+1R pattern detected!
                    signal_price = prices[-1]  # Price at R point (5th point)
                    
                    pattern_info = {
                        'symbol': symbol,
                        'pattern': 'FFFR',
                        'signal_price': signal_price,
                        'drop_percentage': drop_percentage,
                        'start_price': start_price,
                        'lowest_price': lowest_price,
                        'prices': prices,
                        'movements': movements,
                        'detected_at': datetime.now()
                    }
                    
                    self.logger.info(f"🎯 4F+1R DETECTED: {symbol}")
                    self.logger.info(f"   Pattern: {pattern_string}")
                    self.logger.info(f"   Signal Price: ₹{signal_price:.2f}")
                    self.logger.info(f"   Drop: {drop_percentage:.2f}% (₹{start_price:.2f} → ₹{lowest_price:.2f})")
                    
                    return pattern_info
                else:
                    self.logger.debug(f"   {symbol}: FFFR pattern but drop {drop_percentage:.2f}% < 0.5%")
                    return None
            else:
                self.logger.debug(f"   {symbol}: Pattern {pattern_string} (not FFFR)")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Error detecting pattern for {symbol}: {e}")
            return None
    
    def analyze_pattern_priority(self, symbol: str) -> str:
        """Analyze pattern for GOLD/SILVER/BRONZE priority - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Get last 5 data points
            cursor.execute('''
            SELECT fr_movement FROM trading_data 
            WHERE symbol = ? 
            ORDER BY timestamp DESC 
            LIMIT 5
            ''', (symbol,))
            
            results = cursor.fetchall()
            conn.close()
            
            if len(results) < 5:
                return 'INSUFFICIENT_DATA'
            
            # Extract movements (reverse to chronological order, skip first)
            movements = [row[0] for row in reversed(results)][1:]  # Skip first point
            
            if len(movements) != 4:
                return 'INSUFFICIENT_MOVEMENTS'
            
            pattern_string = ''.join(movements)
            
            # Priority classification (EXACT DOCUMENTATION)
            if pattern_string == 'FFFR':
                return 'GOLD'  # Ready for BUY signal
            elif pattern_string.endswith('FFF'):  # XFFF pattern
                return 'SILVER'
            elif pattern_string.endswith('FF'):   # XXFF pattern
                return 'BRONZE'
            else:
                return 'REMAINING'  # Other patterns
                
        except Exception as e:
            self.logger.error(f"❌ Error analyzing priority for {symbol}: {e}")
            return 'ERROR'
    
    def scan_all_symbols_for_patterns(self) -> List[Dict]:
        """Scan all symbols for 4F+1R patterns - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Get all symbols with at least 5 data points
            cursor.execute('''
            SELECT symbol, COUNT(*) as count 
            FROM trading_data 
            GROUP BY symbol 
            HAVING COUNT(*) >= 5
            ''')
            
            symbols = cursor.fetchall()
            conn.close()
            
            detected_patterns = []
            
            for symbol, count in symbols:
                pattern_info = self.detect_4f1r_pattern(symbol)
                if pattern_info:
                    detected_patterns.append(pattern_info)
            
            return detected_patterns
            
        except Exception as e:
            self.logger.error(f"❌ Error scanning for patterns: {e}")
            return []
    
    def get_priority_queue_status(self) -> Dict:
        """Get priority queue status for all symbols - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Get all symbols with data
            cursor.execute('SELECT DISTINCT symbol FROM trading_data')
            symbols = [row[0] for row in cursor.fetchall()]
            conn.close()
            
            priority_counts = {
                'GOLD': 0,
                'SILVER': 0, 
                'BRONZE': 0,
                'REMAINING': 0,
                'INSUFFICIENT_DATA': 0
            }
            
            priority_symbols = {
                'GOLD': [],
                'SILVER': [],
                'BRONZE': [],
                'REMAINING': [],
                'INSUFFICIENT_DATA': []
            }
            
            for symbol in symbols:
                priority = self.analyze_pattern_priority(symbol)
                if priority in priority_counts:
                    priority_counts[priority] += 1
                    priority_symbols[priority].append(symbol)
            
            return {
                'counts': priority_counts,
                'symbols': priority_symbols,
                'total_symbols': len(symbols)
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error getting priority queue status: {e}")
            return {}

# Global instance
db1_pattern_detector = DB1PatternDetector()
