#!/usr/bin/env python3
"""
Fix Existing F/R Data in DB1
Updates all records with NULL/N/A fr_movement to proper F/R calculations
Then triggers signal detection for trading
"""

import sqlite3
import logging
from typing import List, Dict

class ExistingDataFixer:
    def __init__(self):
        self.DB_PATH = 'Data/trading_data.db'
        self.logger = self._setup_logging()
        
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)
    
    def get_symbols_needing_fr_fix(self) -> List[str]:
        """Get all symbols that have NULL or missing F/R data"""
        try:
            conn = sqlite3.connect(self.DB_PATH, timeout=30.0)
            cursor = conn.cursor()
            
            # Find symbols with NULL or missing F/R data
            cursor.execute('''
            SELECT DISTINCT symbol 
            FROM trading_data 
            WHERE fr_movement IS NULL 
            ORDER BY symbol
            ''')
            
            symbols = [row[0] for row in cursor.fetchall()]
            conn.close()
            
            self.logger.info(f"📊 Found {len(symbols)} symbols needing F/R fix")
            return symbols
            
        except Exception as e:
            self.logger.error(f"❌ Error getting symbols: {e}")
            return []
    
    def fix_fr_data_for_symbol(self, symbol: str) -> bool:
        """Fix F/R data for a specific symbol"""
        try:
            conn = sqlite3.connect(self.DB_PATH, timeout=30.0)
            cursor = conn.cursor()
            
            # Get all records for this symbol ordered by timestamp
            cursor.execute('''
            SELECT id, timestamp, close_price, fr_movement, previous_close
            FROM trading_data 
            WHERE symbol = ? 
            ORDER BY timestamp ASC
            ''', (symbol,))
            
            records = cursor.fetchall()
            
            if not records:
                self.logger.warning(f"⚠️ No records found for {symbol}")
                conn.close()
                return False
            
            self.logger.info(f"📊 Fixing F/R data for {symbol} ({len(records)} records)")
            
            # Calculate F/R movements
            previous_close = None
            updates_made = 0
            
            for i, (record_id, timestamp, close_price, current_fr, current_prev_close) in enumerate(records):
                
                # Calculate F/R movement
                if i == 0:
                    # First record is always BASE
                    fr_movement = 'BASE'
                    prev_close_value = close_price
                    self.logger.info(f"📊 {symbol} {timestamp}: ₹{close_price:.2f} = BASE (first record)")
                else:
                    # Compare with previous close
                    if close_price > previous_close:
                        fr_movement = 'R'  # Rise
                    elif close_price < previous_close:
                        fr_movement = 'F'  # Fall
                    else:
                        fr_movement = 'FLAT'  # No change
                    
                    prev_close_value = previous_close
                    time_str = timestamp.split(' ')[1][:5] if ' ' in timestamp else timestamp
                    self.logger.info(f"📊 {symbol} {time_str}: ₹{previous_close:.2f} → ₹{close_price:.2f} = {fr_movement}")
                
                # Update the record if F/R is NULL or different
                if current_fr is None or current_fr != fr_movement:
                    cursor.execute('''
                    UPDATE trading_data 
                    SET fr_movement = ?, previous_close = ?
                    WHERE id = ?
                    ''', (fr_movement, prev_close_value, record_id))
                    updates_made += 1
                
                # Set previous_close for next iteration
                previous_close = close_price
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"✅ {symbol}: Updated {updates_made}/{len(records)} records with F/R data")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error fixing F/R data for {symbol}: {e}")
            return False
    
    def detect_signals_for_symbol(self, symbol: str) -> Dict:
        """Detect trading signals for a symbol after F/R fix"""
        try:
            conn = sqlite3.connect(self.DB_PATH, timeout=30.0)
            cursor = conn.cursor()
            
            # Get last 5 F/R movements for pattern detection
            cursor.execute('''
            SELECT timestamp, close_price, fr_movement 
            FROM trading_data 
            WHERE symbol = ? AND fr_movement IS NOT NULL
            ORDER BY timestamp DESC 
            LIMIT 5
            ''', (symbol,))
            
            rows = cursor.fetchall()
            
            if len(rows) < 5:
                conn.close()
                return {'signal': None, 'reason': f'Only {len(rows)} records available'}
            
            # Extract F/R pattern (reverse to get chronological order)
            fr_pattern = [row[2] for row in reversed(rows)]
            latest_price = rows[0][1]  # Most recent close price
            latest_timestamp = rows[0][0]
            
            # Check for trading patterns
            signal_detected = None
            priority = None
            pattern_name = None
            
            if fr_pattern == ['F', 'F', 'F', 'F', 'R']:
                signal_detected = 'BUY'
                priority = 'GOLD'
                pattern_name = 'FFFFR'
                self.logger.info(f"🎯 GOLD SIGNAL: {symbol} FFFFR pattern at ₹{latest_price:.2f}")
                
            elif fr_pattern[1:] == ['F', 'F', 'F', 'F']:  # XFFFF
                signal_detected = 'BUY'
                priority = 'SILVER'
                pattern_name = 'XFFFF'
                self.logger.info(f"🥈 SILVER SIGNAL: {symbol} XFFFF pattern at ₹{latest_price:.2f}")
                
            elif fr_pattern[2:] == ['F', 'F', 'F']:  # XXFFF
                signal_detected = 'BUY'
                priority = 'BRONZE'
                pattern_name = 'XXFFF'
                self.logger.info(f"🥉 BRONZE SIGNAL: {symbol} XXFFF pattern at ₹{latest_price:.2f}")
            
            # Store signal if detected
            if signal_detected:
                self.store_buy_signal(symbol, latest_price, latest_timestamp, pattern_name, priority)
                conn.close()
                return {
                    'signal': signal_detected,
                    'priority': priority,
                    'pattern': pattern_name,
                    'price': latest_price,
                    'timestamp': latest_timestamp
                }
            else:
                pattern_str = ''.join(fr_pattern)
                conn.close()
                return {'signal': None, 'pattern': pattern_str, 'reason': 'No trading pattern detected'}
                
        except Exception as e:
            self.logger.error(f"❌ Error detecting signals for {symbol}: {e}")
            return {'signal': None, 'error': str(e)}
    
    def store_buy_signal(self, symbol: str, price: float, timestamp: str, pattern: str, priority: str):
        """Store BUY signal in signal_buy table"""
        try:
            conn = sqlite3.connect(self.DB_PATH, timeout=30.0)
            cursor = conn.cursor()
            
            # Create signal_buy table if not exists
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS signal_buy (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                signal_price REAL NOT NULL,
                signal_time DATETIME NOT NULL,
                pattern TEXT NOT NULL,
                priority TEXT NOT NULL,
                status TEXT DEFAULT 'ACTIVE',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, signal_time)
            )
            ''')
            
            # Insert BUY signal
            cursor.execute('''
            INSERT OR REPLACE INTO signal_buy 
            (symbol, signal_price, signal_time, pattern, priority)
            VALUES (?, ?, ?, ?, ?)
            ''', (symbol, price, timestamp, pattern, priority))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"🎯 BUY SIGNAL STORED: {symbol} {pattern} {priority} at ₹{price:.2f}")
            
        except Exception as e:
            self.logger.error(f"❌ Error storing BUY signal for {symbol}: {e}")
    
    def fix_all_existing_data(self) -> Dict:
        """Main method to fix all existing F/R data and detect signals"""
        try:
            self.logger.info("🚀 STARTING F/R DATA FIX FOR EXISTING RECORDS")
            
            # Get symbols needing F/R fix
            symbols = self.get_symbols_needing_fr_fix()
            
            if not symbols:
                self.logger.info("✅ No symbols need F/R fixing")
                return {'success': 0, 'failed': 0, 'signals': 0}
            
            # Fix F/R data for each symbol
            fixed_count = 0
            failed_count = 0
            signals_generated = 0
            
            for symbol in symbols:
                self.logger.info(f"📊 Processing {symbol}...")
                
                # Fix F/R data
                if self.fix_fr_data_for_symbol(symbol):
                    fixed_count += 1
                    
                    # Detect signals after fixing F/R data
                    signal_result = self.detect_signals_for_symbol(symbol)
                    if signal_result.get('signal') == 'BUY':
                        signals_generated += 1
                        
                else:
                    failed_count += 1
            
            self.logger.info(f"🎉 F/R DATA FIX COMPLETED")
            self.logger.info(f"📊 Results: {fixed_count} fixed, {failed_count} failed, {signals_generated} signals generated")
            
            return {
                'success': fixed_count,
                'failed': failed_count,
                'signals': signals_generated,
                'total': len(symbols)
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error in fix_all_existing_data: {e}")
            return {'success': 0, 'failed': 0, 'signals': 0, 'error': str(e)}

# Main execution
if __name__ == "__main__":
    fixer = ExistingDataFixer()
    result = fixer.fix_all_existing_data()
    print(f"📊 Final Result: {result}")
