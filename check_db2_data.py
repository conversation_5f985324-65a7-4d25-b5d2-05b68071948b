import sqlite3
from datetime import datetime

print("=== CHECKING DB2 2-MINUTE DATA STRUCTURE ===")

conn = sqlite3.connect('Data/trading_operations.db')
cursor = conn.cursor()

# Check DB1 signal (base point)
print("1. DB1 SIGNAL (BASE POINT):")
cursor.execute('SELECT symbol, signal_price, status FROM db2_signals_received WHERE symbol = "360ONE"')
signal = cursor.fetchone()
if signal:
    symbol, signal_price, status = signal
    print(f"   360ONE BUY signal: ₹{signal_price:.2f} ({status})")
    print(f"   This should be the BASE POINT for DB2 2-minute F/R calculation")
else:
    print("   ❌ No signal found")

print("\n2. DB2 2-MINUTE DATA:")
cursor.execute('''
SELECT close_price, fr_movement, previous_close, timestamp 
FROM db2_trading_data 
WHERE symbol = "360ONE" 
ORDER BY timestamp
''')

data = cursor.fetchall()

if data:
    print(f"   Found {len(data)} 2-minute data points:")
    
    for i, (price, movement, prev_close, timestamp) in enumerate(data):
        try:
            time_obj = datetime.fromisoformat(timestamp)
            time_str = time_obj.strftime('%H:%M:%S')
        except:
            time_str = timestamp[-8:] if len(timestamp) >= 8 else timestamp
        
        print(f"   {i+1}. {time_str}: ₹{price:.2f} ({movement}) [Prev: ₹{prev_close or 'None'}]")
    
    # Check if first data point uses DB1 signal as base
    first_price, first_movement, first_prev = data[0][0], data[0][1], data[0][2]
    
    if signal and first_prev is None and first_movement == 'START':
        print(f"\n   ✅ CORRECT: First data point is START (₹{first_price:.2f})")
    elif signal and abs(first_prev - signal_price) < 0.01:
        print(f"\n   ✅ CORRECT: First data point uses DB1 signal as base")
    else:
        print(f"\n   ❌ ISSUE: First data point should use DB1 signal (₹{signal_price:.2f}) as base")
    
    # Check for RR pattern
    movements = [row[1] for row in data if row[1] in ['R', 'F']]
    print(f"\n   Movement pattern: {' → '.join(movements)}")
    
    # Look for RR
    rr_found = False
    for i in range(len(movements) - 1):
        if movements[i] == 'R' and movements[i + 1] == 'R':
            print(f"   ✅ RR PATTERN FOUND at positions {i+1}-{i+2}")
            rr_found = True
            break
    
    if not rr_found:
        print("   ⚠️ No RR pattern found yet")

else:
    print("   ❌ No 2-minute data found")

print("\n3. CURRENT POSITION:")
cursor.execute('''
SELECT symbol, buy_price, current_price, shares_quantity, current_profit, status 
FROM trading_positions 
WHERE symbol = "360ONE"
''')

position = cursor.fetchone()
if position:
    symbol, buy_price, current_price, shares, profit, status = position
    print(f"   {symbol}: {status}")
    print(f"   Buy Price: ₹{buy_price:.2f}")
    print(f"   Current Price: ₹{current_price or 0:.2f}")
    print(f"   Shares: {shares:,}")
    print(f"   Current Profit: ₹{profit or 0:.2f}")
else:
    print("   ❌ No position found")

print("\n4. ARCHITECTURE VERIFICATION:")
print("   DB1 Base Point: 15-minute close prices")
print("   DB2 Base Point: DB1 signal price (₹1152.35)")
print("   DB2 F/R Logic: 2-min price vs previous 2-min price")
print("   RR Confirmation: Two consecutive RISE movements in 2-min data")

conn.close()

print("\n=== SUMMARY ===")
print("✅ All trading should be SQL-based (no API calls)")
print("✅ DB2 should use DB1 signal as starting base point")
print("✅ Flask should only display what's in SQL tables")
