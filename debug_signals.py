import sqlite3

# Check all signals in detail
conn = sqlite3.connect('Data/trading_operations.db')
cursor = conn.cursor()

print("=== ALL SIGNALS IN db2_signals_received ===")
cursor.execute('SELECT * FROM db2_signals_received')
signals = cursor.fetchall()

# Get column names
cursor.execute('PRAGMA table_info(db2_signals_received)')
columns = [row[1] for row in cursor.fetchall()]
print(f"Columns: {columns}")

for signal in signals:
    print(f"Signal: {dict(zip(columns, signal))}")

print("\n=== SIGNAL COUNTS ===")
cursor.execute('SELECT COUNT(*) FROM db2_signals_received')
total = cursor.fetchone()[0]
print(f"Total signals: {total}")

cursor.execute('SELECT COUNT(*) FROM db2_signals_received WHERE status IN ("RECEIVED", "CONFIRMING")')
pending = cursor.fetchone()[0]
print(f"Pending signals: {pending}")

cursor.execute('SELECT COUNT(*) FROM db2_signals_received WHERE status = "EXECUTED"')
executed = cursor.fetchone()[0]
print(f"Executed signals: {executed}")

print("\n=== POSITION STATUS ===")
cursor.execute('SELECT symbol, status, current_profit FROM trading_positions')
positions = cursor.fetchall()
for pos in positions:
    print(f"Position: {pos[0]} - {pos[1]} - Profit: ₹{pos[2] or 0:.2f}")

conn.close()
