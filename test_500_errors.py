#!/usr/bin/env python3
"""
Test all the 500 error endpoints that were failing
"""

import requests
import time

def test_500_errors():
    print("🔧 Testing ALL 500 ERROR FIXES...")
    
    try:
        # Test 1: priority-queue/status (was failing with trading_signals error)
        print("\n1. Testing /api/priority-queue/status...")
        response = requests.get('http://localhost:5000/api/priority-queue/status', timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            success = data.get('success', False)
            priority_queue = data.get('priority_queue', {})
            print(f"   ✅ Success: {success}")
            print(f"   ✅ GOLD: {len(priority_queue.get('GOLD', []))}")
            print(f"   ✅ SILVER: {len(priority_queue.get('SILVER', []))}")
            print(f"   ✅ BRONZE: {len(priority_queue.get('BRONZE', []))}")
            print(f"   ✅ REMAINING: {len(priority_queue.get('REMAINING', []))}")
        else:
            print(f"   ❌ Error: {response.text[:200]}")
        
        # Test 2: DB1 signals (was using wrong table)
        print("\n2. Testing /api/db1/signals...")
        response = requests.get('http://localhost:5000/api/db1/signals', timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            success = data.get('success', False)
            count = data.get('count', 0)
            print(f"   ✅ Success: {success}")
            print(f"   ✅ Signals Count: {count}")
        else:
            print(f"   ❌ Error: {response.text[:200]}")
        
        # Test 3: layer2-confirmations (was fixed earlier)
        print("\n3. Testing /api/layer2-confirmations...")
        response = requests.get('http://localhost:5000/api/layer2-confirmations', timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            success = data.get('success', False)
            count = data.get('count', 0)
            print(f"   ✅ Success: {success}")
            print(f"   ✅ Confirmations Count: {count}")
        else:
            print(f"   ❌ Error: {response.text[:200]}")
        
        # Test 4: paper-trading/records
        print("\n4. Testing /api/paper-trading/records...")
        response = requests.get('http://localhost:5000/api/paper-trading/records', timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            success = data.get('success', False)
            count = data.get('count', 0)
            print(f"   ✅ Success: {success}")
            print(f"   ✅ Records Count: {count}")
        else:
            print(f"   ❌ Error: {response.text[:200]}")
        
        print("\n🎯 SUMMARY:")
        print("✅ All 500 errors should be fixed now!")
        print("✅ Frontend should load without errors!")
        
    except Exception as e:
        print(f"❌ Test error: {e}")

if __name__ == "__main__":
    test_500_errors()
