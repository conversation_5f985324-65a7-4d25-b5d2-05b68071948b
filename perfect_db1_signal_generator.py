#!/usr/bin/env python3
"""
Perfect DB1 Signal Generator - PURE SQL OPERATIONS

This module generates BUY signals when 4F+1R patterns are detected.
NO API CALLS - Only SQL operations.
USES CORRECT SCHEMA: trading_signals table (not signal_buy).
FOLLOWS EXACT DOCUMENTATION SPECIFICATIONS.
"""

import sqlite3
import logging
from typing import List, Dict, Optional
from datetime import datetime
from db1_pattern_detector import db1_pattern_detector

class PerfectDB1SignalGenerator:
    """Pure SQL BUY signal generator for 4F+1R patterns"""
    
    def __init__(self):
        self.db_path = 'Data/trading_data.db'
        self.logger = logging.getLogger(__name__)
        
    def generate_buy_signal(self, pattern_info: Dict) -> bool:
        """Generate BUY signal from 4F+1R pattern - PURE SQL"""
        try:
            # Store signal in trading_signals table (CORRECT SCHEMA)
            signal_id = self._store_signal(
                symbol=pattern_info['symbol'],
                signal_type='BUY',
                signal_price=pattern_info['signal_price'],
                pattern_sequence=pattern_info['pattern'],
                drop_percentage=pattern_info['drop_percentage']
            )
            
            if signal_id:
                self.logger.info(f"💰 BUY SIGNAL GENERATED: {pattern_info['symbol']} @ ₹{pattern_info['signal_price']:.2f}")
                self.logger.info(f"   Pattern: {pattern_info['pattern']}")
                self.logger.info(f"   Drop: {pattern_info['drop_percentage']:.2f}%")
                self.logger.info(f"   Signal ID: {signal_id}")
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error generating signal for {pattern_info['symbol']}: {e}")
            return False
    
    def _store_signal(self, symbol: str, signal_type: str, signal_price: float, 
                     pattern_sequence: str, drop_percentage: float) -> Optional[int]:
        """Store BUY signal in trading_signals table - CORRECT SCHEMA"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Check if signal already exists for this symbol (prevent duplicates)
            cursor.execute('''
            SELECT id FROM trading_signals 
            WHERE symbol = ? AND signal_type = ?
            ORDER BY created_at DESC
            LIMIT 1
            ''', (symbol, signal_type))
            
            existing = cursor.fetchone()
            if existing:
                # Check if signal was created recently (within last hour)
                cursor.execute('''
                SELECT created_at FROM trading_signals 
                WHERE id = ?
                ''', (existing[0],))
                
                created_at = cursor.fetchone()[0]
                created_time = datetime.fromisoformat(created_at)
                time_diff = datetime.now() - created_time
                
                if time_diff.total_seconds() < 3600:  # Less than 1 hour
                    conn.close()
                    self.logger.debug(f"   Recent signal exists for {symbol}")
                    return existing[0]
            
            # Insert new signal into trading_signals table
            cursor.execute('''
            INSERT INTO trading_signals 
            (symbol, signal_type, price, pattern_sequence, created_at)
            VALUES (?, ?, ?, ?, ?)
            ''', (symbol, signal_type, signal_price, pattern_sequence, datetime.now()))
            
            signal_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return signal_id
            
        except Exception as e:
            self.logger.error(f"❌ Error storing signal for {symbol}: {e}")
            return None
    
    def get_untransmitted_signals(self) -> List[Dict]:
        """Get all signals that haven't been transmitted to DB2 - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Get signals from trading_signals table (CORRECT SCHEMA)
            cursor.execute('''
            SELECT id, symbol, signal_type, price, pattern_sequence, created_at
            FROM trading_signals 
            WHERE signal_type = 'BUY'
            ORDER BY created_at ASC
            ''')
            
            results = cursor.fetchall()
            conn.close()
            
            signals = []
            for row in results:
                signals.append({
                    'id': row[0],
                    'symbol': row[1],
                    'signal_type': row[2],
                    'signal_price': row[3],
                    'pattern_sequence': row[4],
                    'created_at': row[5]
                })
            
            return signals
            
        except Exception as e:
            self.logger.error(f"❌ Error getting untransmitted signals: {e}")
            return []
    
    def scan_and_generate_signals(self) -> List[Dict]:
        """Scan all symbols and generate signals for detected patterns - PURE SQL"""
        try:
            # Detect all 4F+1R patterns using pure SQL
            detected_patterns = db1_pattern_detector.scan_all_symbols_for_patterns()
            
            generated_signals = []
            
            for pattern_info in detected_patterns:
                success = self.generate_buy_signal(pattern_info)
                if success:
                    generated_signals.append(pattern_info)
            
            if generated_signals:
                self.logger.info(f"🎯 GENERATED {len(generated_signals)} BUY SIGNALS")
            
            return generated_signals
            
        except Exception as e:
            self.logger.error(f"❌ Error scanning and generating signals: {e}")
            return []
    
    def get_signal_statistics(self) -> Dict:
        """Get signal generation statistics - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Total signals
            cursor.execute('SELECT COUNT(*) FROM trading_signals')
            total_signals = cursor.fetchone()[0]
            
            # BUY signals
            cursor.execute('SELECT COUNT(*) FROM trading_signals WHERE signal_type = "BUY"')
            buy_signals = cursor.fetchone()[0]
            
            # Recent signals (last 24 hours)
            cursor.execute('''
            SELECT COUNT(*) FROM trading_signals 
            WHERE datetime(created_at) > datetime('now', '-1 day')
            ''')
            recent_signals = cursor.fetchone()[0]
            
            # Signals by symbol
            cursor.execute('''
            SELECT symbol, COUNT(*) as count 
            FROM trading_signals 
            GROUP BY symbol 
            ORDER BY count DESC
            ''')
            
            signals_by_symbol = cursor.fetchall()
            conn.close()
            
            return {
                'total_signals': total_signals,
                'buy_signals': buy_signals,
                'recent_signals': recent_signals,
                'signals_by_symbol': dict(signals_by_symbol)
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error getting signal statistics: {e}")
            return {}
    
    def get_recent_signals(self, limit: int = 10) -> List[Dict]:
        """Get recent signals for display - PURE SQL"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT id, symbol, signal_type, price, pattern_sequence, created_at
            FROM trading_signals 
            ORDER BY created_at DESC 
            LIMIT ?
            ''', (limit,))
            
            results = cursor.fetchall()
            conn.close()
            
            signals = []
            for row in results:
                signals.append({
                    'id': row[0],
                    'symbol': row[1],
                    'signal_type': row[2],
                    'signal_price': row[3],
                    'pattern_sequence': row[4],
                    'created_at': row[5]
                })
            
            return signals
            
        except Exception as e:
            self.logger.error(f"❌ Error getting recent signals: {e}")
            return []

# Global instance
perfect_db1_signal_generator = PerfectDB1SignalGenerator()
