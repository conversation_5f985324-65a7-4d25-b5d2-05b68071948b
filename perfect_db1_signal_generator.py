#!/usr/bin/env python3
"""
Perfect DB1 Signal Generator

This module generates BUY signals when 4F+1R patterns are detected.
Follows consistent naming and structure.
"""

import sqlite3
import logging
from typing import List, Dict, Optional
from datetime import datetime
from db1_pattern_detector import db1_pattern_detector

class PerfectDB1SignalGenerator:
    """Perfect BUY signal generator for 4F+1R patterns"""
    
    def __init__(self):
        self.db_path = 'Data/trading_data.db'
        self.logger = logging.getLogger(__name__)
        
    def generate_buy_signal(self, pattern_info: Dict) -> bool:
        """Generate BUY signal from 4F+1R pattern"""
        try:
            # Store signal in DB1
            signal_id = self._store_signal(
                symbol=pattern_info['symbol'],
                signal_type='BUY',
                signal_price=pattern_info['signal_price'],
                pattern_sequence=pattern_info['pattern'],
                drop_percentage=pattern_info['drop_percentage']
            )
            
            if signal_id:
                self.logger.info(f"💰 BUY SIGNAL GENERATED: {pattern_info['symbol']} @ ₹{pattern_info['signal_price']:.2f}")
                self.logger.info(f"   Pattern: {pattern_info['pattern']}")
                self.logger.info(f"   Drop: {pattern_info['drop_percentage']:.2f}%")
                self.logger.info(f"   Signal ID: {signal_id}")
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error generating signal for {pattern_info['symbol']}: {e}")
            return False
    
    def _store_signal(self, symbol: str, signal_type: str, signal_price: float, 
                     pattern_sequence: str, drop_percentage: float) -> Optional[int]:
        """Store BUY signal in DB1 trading_signals table"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            # Check if signal already exists for this symbol (prevent duplicates)
            cursor.execute('''
            SELECT id FROM trading_signals 
            WHERE symbol = ? AND signal_type = ? AND transmitted_to_db2 = FALSE
            ''', (symbol, signal_type))
            
            existing = cursor.fetchone()
            if existing:
                conn.close()
                self.logger.debug(f"   Signal already exists for {symbol}")
                return existing[0]
            
            # Insert new signal
            cursor.execute('''
            INSERT INTO trading_signals 
            (symbol, signal_type, signal_price, pattern_sequence, drop_percentage, confidence_score, transmitted_to_db2)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (symbol, signal_type, signal_price, pattern_sequence, drop_percentage, 1.0, False))
            
            signal_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return signal_id
            
        except Exception as e:
            self.logger.error(f"❌ Error storing signal for {symbol}: {e}")
            return None
    
    def get_untransmitted_signals(self) -> List[Dict]:
        """Get all signals that haven't been transmitted to DB2"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT id, symbol, signal_type, signal_price, pattern_sequence, 
                   drop_percentage, confidence_score, created_at
            FROM trading_signals 
            WHERE transmitted_to_db2 = FALSE
            ORDER BY created_at ASC
            ''')
            
            results = cursor.fetchall()
            conn.close()
            
            signals = []
            for row in results:
                signals.append({
                    'id': row[0],
                    'symbol': row[1],
                    'signal_type': row[2],
                    'signal_price': row[3],
                    'pattern_sequence': row[4],
                    'drop_percentage': row[5],
                    'confidence_score': row[6],
                    'created_at': row[7]
                })
            
            return signals
            
        except Exception as e:
            self.logger.error(f"❌ Error getting untransmitted signals: {e}")
            return []
    
    def mark_signal_transmitted(self, signal_id: int) -> bool:
        """Mark signal as transmitted to DB2"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute('''
            UPDATE trading_signals 
            SET transmitted_to_db2 = TRUE 
            WHERE id = ?
            ''', (signal_id,))
            
            conn.commit()
            conn.close()
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error marking signal {signal_id} as transmitted: {e}")
            return False
    
    def scan_and_generate_signals(self) -> List[Dict]:
        """Scan all symbols and generate signals for detected patterns"""
        try:
            # Detect all 4F+1R patterns
            detected_patterns = db1_pattern_detector.scan_all_symbols_for_patterns()
            
            generated_signals = []
            
            for pattern_info in detected_patterns:
                success = self.generate_buy_signal(pattern_info)
                if success:
                    generated_signals.append(pattern_info)
            
            if generated_signals:
                self.logger.info(f"🎯 GENERATED {len(generated_signals)} BUY SIGNALS")
            
            return generated_signals
            
        except Exception as e:
            self.logger.error(f"❌ Error scanning and generating signals: {e}")
            return []

# Global instance
perfect_db1_signal_generator = PerfectDB1SignalGenerator()
