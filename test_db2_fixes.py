#!/usr/bin/env python3
"""
Test DB2 Fixes - Comprehensive Validation

This script tests all the fixes:
1. ₹100,000 investment per symbol
2. DB2 signal tracking table
3. Portfolio summary and display
4. Proper stock calculation
5. DB1→DB2 communication mechanism
"""

import logging
import sqlite3
import time
from datetime import datetime
from db2_trade_executor import get_db2_trade_executor
from db1_db2_communicator import TradingSignal, get_communicator

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_investment_amount():
    """Test ₹100,000 investment amount"""
    logger.info("🔍 TESTING ₹100,000 INVESTMENT AMOUNT")
    
    db2_executor = get_db2_trade_executor()
    
    if db2_executor.investment_per_symbol == 100000:
        logger.info("✅ Investment amount correctly set to ₹100,000")
        return True
    else:
        logger.error(f"❌ Wrong investment amount: ₹{db2_executor.investment_per_symbol} (expected ₹100,000)")
        return False

def test_stock_calculation():
    """Test stock calculation with ₹100,000"""
    logger.info("🔍 TESTING STOCK CALCULATION")
    
    # Test with different stock prices
    test_cases = [
        {"price": 100.0, "expected_shares": 1000},
        {"price": 250.0, "expected_shares": 400},
        {"price": 500.0, "expected_shares": 200},
        {"price": 1000.0, "expected_shares": 100},
        {"price": 2000.0, "expected_shares": 50}
    ]
    
    for case in test_cases:
        price = case["price"]
        expected_shares = case["expected_shares"]
        calculated_shares = int(100000 / price)
        actual_investment = calculated_shares * price
        
        logger.info(f"📊 Price ₹{price:.2f}: {calculated_shares:,} shares = ₹{actual_investment:,.2f}")
        
        if calculated_shares == expected_shares:
            logger.info(f"✅ Correct calculation for ₹{price}")
        else:
            logger.error(f"❌ Wrong calculation for ₹{price}: got {calculated_shares}, expected {expected_shares}")
            return False
    
    return True

def test_db2_signal_tracking():
    """Test DB2 signal tracking table"""
    logger.info("🔍 TESTING DB2 SIGNAL TRACKING")
    
    db2_executor = get_db2_trade_executor()
    
    try:
        conn = sqlite3.connect(db2_executor.db_path)
        cursor = conn.cursor()
        
        # Check if signal tracking table exists
        cursor.execute("PRAGMA table_info(db2_signals_received)")
        columns = [row[1] for row in cursor.fetchall()]
        
        required_columns = ['symbol', 'signal_type', 'signal_price', 'status', 'received_time']
        missing_columns = [col for col in required_columns if col not in columns]
        
        if missing_columns:
            logger.error(f"❌ Missing columns in db2_signals_received: {missing_columns}")
            return False
        else:
            logger.info("✅ db2_signals_received table has all required columns")
        
        # Check portfolio summary table
        cursor.execute("PRAGMA table_info(db2_portfolio_summary)")
        portfolio_columns = [row[1] for row in cursor.fetchall()]
        
        required_portfolio_columns = ['total_investment', 'total_current_value', 'total_profit', 'active_positions']
        missing_portfolio_columns = [col for col in required_portfolio_columns if col not in portfolio_columns]
        
        if missing_portfolio_columns:
            logger.error(f"❌ Missing columns in db2_portfolio_summary: {missing_portfolio_columns}")
            return False
        else:
            logger.info("✅ db2_portfolio_summary table has all required columns")
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing signal tracking: {e}")
        return False

def test_db1_to_db2_communication():
    """Test DB1→DB2 communication and signal storage"""
    logger.info("🔍 TESTING DB1→DB2 COMMUNICATION")
    
    try:
        communicator = get_communicator()
        db2_executor = get_db2_trade_executor()
        
        # Create test signal
        test_signal = TradingSignal(
            symbol='TEST_COMM',
            signal_type='BUY',
            price=500.0,  # ₹500 per share
            timestamp_ns=time.time_ns(),
            pattern_info={'pattern_type': '4F+1R', 'test': True},
            source='TEST_DB1'
        )
        
        # Send signal
        success = communicator.send_signal_to_db2(test_signal)
        if not success:
            logger.error("❌ Failed to send signal to DB2")
            return False
        
        logger.info("✅ Signal sent to DB2")
        
        # Process signal
        db2_executor.run_periodic_check()
        
        # Check if signal was stored in DB2
        conn = sqlite3.connect(db2_executor.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT symbol, signal_type, signal_price, status 
        FROM db2_signals_received 
        WHERE symbol = ?
        ''', ('TEST_COMM',))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            symbol, signal_type, signal_price, status = result
            logger.info(f"✅ Signal stored in DB2: {symbol} {signal_type} ₹{signal_price} ({status})")
            return True
        else:
            logger.error("❌ Signal not found in DB2 tracking table")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing communication: {e}")
        return False

def test_portfolio_display():
    """Test portfolio summary and display"""
    logger.info("🔍 TESTING PORTFOLIO DISPLAY")
    
    try:
        db2_executor = get_db2_trade_executor()
        
        # Update portfolio summary
        db2_executor._update_portfolio_summary()
        
        # Get portfolio summary
        portfolio = db2_executor.get_portfolio_summary()
        
        required_fields = ['total_investment', 'total_current_value', 'total_profit', 'active_positions']
        missing_fields = [field for field in required_fields if field not in portfolio]
        
        if missing_fields:
            logger.error(f"❌ Missing portfolio fields: {missing_fields}")
            return False
        
        logger.info("✅ Portfolio summary structure correct")
        logger.info(f"📊 Portfolio Data:")
        logger.info(f"   💰 Total Investment: ₹{portfolio['total_investment']:,.2f}")
        logger.info(f"   📈 Current Value: ₹{portfolio['total_current_value']:,.2f}")
        logger.info(f"   💵 Current Profit: ₹{portfolio['total_profit']:,.2f}")
        logger.info(f"   📊 Active Positions: {portfolio['active_positions']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing portfolio display: {e}")
        return False

def test_15_minute_monitoring():
    """Test 15-minute monitoring explanation"""
    logger.info("🔍 EXPLAINING 15-MINUTE MONITORING")
    
    logger.info("📊 15-MINUTE MONITORING LOGIC:")
    logger.info("   1. DB2 receives BUY signal from DB1 (15-minute pattern)")
    logger.info("   2. DB2 stores signal in db2_signals_received table")
    logger.info("   3. DB2 confirms with RR pattern using 2-minute data")
    logger.info("   4. DB2 executes BUY and stores position")
    logger.info("   5. Every 15 minutes: DB2 fetches latest price")
    logger.info("   6. DB2 stores 2-minute data with F/R calculation")
    logger.info("   7. DB2 calculates current profit vs ₹800 target")
    logger.info("   8. When ₹800+ profit: DB2 checks FF pattern")
    logger.info("   9. DB2 executes SELL when FF confirmed")
    
    logger.info("✅ 15-minute monitoring logic explained")
    return True

def main():
    """Main test function"""
    logger.info("🚀 TESTING DB2 FIXES")
    logger.info("=" * 60)
    
    tests = [
        ("₹100,000 Investment Amount", test_investment_amount),
        ("Stock Calculation", test_stock_calculation),
        ("DB2 Signal Tracking", test_db2_signal_tracking),
        ("DB1→DB2 Communication", test_db1_to_db2_communication),
        ("Portfolio Display", test_portfolio_display),
        ("15-Minute Monitoring", test_15_minute_monitoring)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 TEST: {test_name}")
        logger.info("-" * 40)
        
        try:
            if test_func():
                logger.info(f"✅ PASSED: {test_name}")
                passed_tests += 1
            else:
                logger.error(f"❌ FAILED: {test_name}")
        except Exception as e:
            logger.error(f"❌ ERROR in {test_name}: {e}")
    
    logger.info("\n" + "=" * 60)
    logger.info(f"📊 TEST RESULTS: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        logger.info("🎉 ALL TESTS PASSED - DB2 FIXES COMPLETE!")
        logger.info("✅ ₹100,000 investment per symbol")
        logger.info("✅ Proper stock calculation and portfolio tracking")
        logger.info("✅ DB2 signal tracking and communication")
        logger.info("✅ Portfolio display and monitoring")
        return True
    else:
        logger.error("❌ SOME TESTS FAILED - FIXES NEEDED")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
