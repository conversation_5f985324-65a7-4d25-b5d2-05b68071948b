"""
Database setup and management for trading data
"""
import logging
from datetime import datetime
from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Index, Text, UniqueConstraint, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import SQLAlchemyError
from config import DATABASE_CONFIG

Base = declarative_base()

class TradingData(Base):
    """Trading data model with F/R pattern calculation"""
    __tablename__ = 'trading_data'

    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False)
    token = Column(String(20), nullable=False)
    exchange = Column(String(10), nullable=False, default='NSE')
    timestamp = Column(DateTime, nullable=False)
    open_price = Column(Float, nullable=False)
    high_price = Column(Float, nullable=False)
    low_price = Column(Float, nullable=False)
    close_price = Column(Float, nullable=False)
    volume = Column(Integer, nullable=False)
    interval_type = Column(String(20), nullable=False, default='FIFTEEN_MINUTE')
    created_at = Column(DateTime, default=datetime.utcnow)

    # 🎯 F/R PATTERN COLUMNS (calculated at insertion)
    fr_movement = Column(String(10))  # 'F', 'R', 'N', 'START'
    previous_close = Column(Float)    # Previous close price for F/R calculation
    fr_calculated = Column(Boolean, default=False)  # Flag to indicate F/R was calculated

    # Create indexes and unique constraint for better query performance and duplicate prevention
    __table_args__ = (
        Index('idx_symbol_timestamp', 'symbol', 'timestamp'),
        Index('idx_timestamp', 'timestamp'),
        Index('idx_symbol', 'symbol'),
        Index('idx_fr_movement', 'fr_movement'),  # Index for F/R queries
        UniqueConstraint('symbol', 'timestamp', name='uq_symbol_timestamp'),
    )

class SymbolMaster(Base):
    """Symbol master data"""
    __tablename__ = 'symbol_master'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False, unique=True)
    token = Column(String(20), nullable=False)
    company_name = Column(String(200))
    industry = Column(String(100))
    series = Column(String(10))
    isin_code = Column(String(20))
    exchange = Column(String(10), default='NSE')
    is_active = Column(Integer, default=1)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class DataFetchLog(Base):
    """Log of data fetch operations"""
    __tablename__ = 'data_fetch_log'

    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False)
    fetch_date = Column(DateTime, nullable=False)
    start_time = Column(DateTime, nullable=False)
    end_time = Column(DateTime, nullable=False)
    records_fetched = Column(Integer, default=0)
    status = Column(String(20), nullable=False)  # SUCCESS, FAILED, PARTIAL
    error_message = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)

class TradingSignals(Base):
    """Trading signals for Layer 2 confirmations"""
    __tablename__ = 'trading_signals'

    id = Column(Integer, primary_key=True, autoincrement=True)
    symbol = Column(String(20), nullable=False)
    signal_type = Column(String(10), nullable=False)  # BUY, SELL
    price = Column(Float, nullable=False)
    signal_price = Column(Float, nullable=False)  # Original signal price for Layer 2 tracking
    timestamp = Column(DateTime, nullable=False)
    pattern_sequence = Column(Text)  # Store RISE,FALL,RISE pattern
    executed = Column(Integer, default=0)  # 0=pending, 1=executed, -1=force_hold
    created_at = Column(DateTime, default=datetime.utcnow)

class DatabaseManager:
    def __init__(self, db_type: str = "sqlite"):
        self.db_type = db_type
        self.engine = None
        self.Session = None
        self.logger = logging.getLogger(__name__)

        # Initialize database connection and create tables
        self.connect()
        self.create_tables()

    def connect(self):
        """Connect to database"""
        try:
            db_config = DATABASE_CONFIG.get(self.db_type)
            if not db_config:
                raise ValueError(f"Unsupported database type: {self.db_type}")
            
            self.engine = create_engine(
                db_config["url"],
                echo=db_config.get("echo", False)
            )
            
            self.Session = sessionmaker(bind=self.engine)
            self.logger.info(f"Connected to {self.db_type} database")
            
        except Exception as e:
            self.logger.error(f"Database connection failed: {e}")
            raise
    
    def create_tables(self):
        """Create all tables"""
        try:
            if self.engine is None:
                self.logger.error("Database engine not initialized. Cannot create tables.")
                return False

            Base.metadata.create_all(self.engine)
            self.logger.info("Database tables created successfully")
            return True
        except Exception as e:
            self.logger.error(f"Error creating tables: {e}")
            return False
    
    def get_session(self):
        """Get database session"""
        if not self.Session:
            self.connect()
        return self.Session()
    
    def insert_trading_data(self, data_records: list):
        """Insert trading data records with F/R calculation at insertion point"""
        session = self.get_session()
        try:
            inserted_count = 0
            skipped_count = 0
            newly_inserted_symbols = set()  # Track symbols with new data

            for record in data_records:
                # Normalize timestamp for consistent comparison
                timestamp = record['timestamp']
                if isinstance(timestamp, str):
                    # Parse string timestamp
                    try:
                        if 'T' in timestamp:
                            timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        else:
                            timestamp = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')
                        timestamp = timestamp.replace(tzinfo=None, microsecond=0)
                    except:
                        self.logger.warning(f"Failed to parse timestamp: {timestamp}")
                        continue
                elif isinstance(timestamp, datetime):
                    timestamp = timestamp.replace(tzinfo=None, microsecond=0)

                # Check if record already exists with normalized timestamp
                existing = session.query(TradingData).filter_by(
                    symbol=record['symbol'],
                    timestamp=timestamp
                ).first()

                if not existing:
                    # 🎯 CALCULATE F/R AT INSERTION POINT
                    record_copy = record.copy()
                    record_copy['timestamp'] = timestamp

                    # Get previous close price for F/R calculation
                    previous_close = self._get_previous_close_price(session, record['symbol'], timestamp)

                    if previous_close is not None:
                        current_close = record['close_price']

                        # Calculate F/R movement
                        if current_close > previous_close:
                            fr_movement = 'R'
                        elif current_close < previous_close:
                            fr_movement = 'F'
                        else:
                            fr_movement = 'N'

                        # Add F/R data to record
                        record_copy['fr_movement'] = fr_movement
                        record_copy['previous_close'] = previous_close
                        record_copy['fr_calculated'] = True

                        self.logger.debug(f"📊 {record['symbol']}: ₹{previous_close:.2f} → ₹{current_close:.2f} = {fr_movement}")
                    else:
                        # First record for this symbol
                        record_copy['fr_movement'] = 'START'
                        record_copy['previous_close'] = None
                        record_copy['fr_calculated'] = True

                        self.logger.debug(f"📊 {record['symbol']}: First record = START")

                    trading_data = TradingData(**record_copy)
                    session.add(trading_data)
                    inserted_count += 1
                    newly_inserted_symbols.add(record['symbol'])
                else:
                    skipped_count += 1
                    self.logger.debug(f"⚠️ DUPLICATE SKIPPED: {record['symbol']} at {timestamp}")

            session.commit()

            if inserted_count > 0:
                self.logger.info(f"✅ Inserted {inserted_count} new trading data records with F/R calculations")

            if skipped_count > 0:
                self.logger.info(f"⚠️ Skipped {skipped_count} duplicate records (already exist)")

            # 🚀 TRIGGER SIMPLE PATTERN ANALYSIS FOR NEW DATA
            if newly_inserted_symbols:
                self._trigger_simple_pattern_analysis(newly_inserted_symbols)

            return True

        except SQLAlchemyError as e:
            session.rollback()
            self.logger.error(f"Error inserting trading data: {e}")
            return False
        finally:
            session.close()

    def _get_previous_close_price(self, session, symbol: str, current_timestamp: datetime) -> float:
        """Get the previous close price for F/R calculation"""
        try:
            # Get the most recent record before current timestamp
            previous_record = session.query(TradingData).filter(
                TradingData.symbol == symbol,
                TradingData.timestamp < current_timestamp
            ).order_by(TradingData.timestamp.desc()).first()

            if previous_record:
                return previous_record.close_price

            return None

        except Exception as e:
            self.logger.error(f"Error getting previous close price for {symbol}: {e}")
            return None

    def _trigger_simple_pattern_analysis(self, symbols: set):
        """🎯 TRIGGER DB1 SIGNAL ANALYSIS"""
        try:
            self.logger.info(f"🎯 TRIGGERING DB1 SIGNAL ANALYSIS for {len(symbols)} symbols")

            # Import DB1 signal generator
            from db1_signal_generator import get_db1_signal_generator

            # Get DB1 signal generator
            db1_generator = get_db1_signal_generator()

            if db1_generator:
                # Trigger signal analysis for symbols
                signals_generated = 0
                for symbol in symbols:
                    if db1_generator.analyze_symbol_for_signals(symbol):
                        signals_generated += 1

                if signals_generated > 0:
                    self.logger.info(f"🎉 DB1 SIGNAL ANALYSIS COMPLETE: {signals_generated} signals generated")
                else:
                    self.logger.info("📊 DB1 signal analysis complete - no signals generated")
            else:
                self.logger.warning("⚠️ DB1 signal generator not available")

        except Exception as e:
            self.logger.error(f"Error triggering DB1 signal analysis: {e}")

    def _trigger_trading_analysis_for_symbols(self, symbols: set):
        """🚫 DEPRECATED: Old direct trading logic - Now using proper DB1→DB2→Paper Trading flow"""
        # This function has been completely disabled to prevent any direct trading execution
        # All trading signals must now go through the Trading Coordinator and Confirmation Engine
        self.logger.info("🚫 OLD TRADING ANALYSIS DISABLED - Using proper DB1→DB2→Paper Trading flow")
        self.logger.info(f"📊 {len(symbols)} symbols would have been analyzed, but now handled by Trading Coordinator")
        return

    def insert_symbol_master(self, symbol_data: dict):
        """Insert or update symbol master data"""
        session = self.get_session()
        try:
            # Check if symbol exists
            existing = session.query(SymbolMaster).filter_by(symbol=symbol_data['symbol']).first()
            
            if existing:
                # Update existing record
                for key, value in symbol_data.items():
                    setattr(existing, key, value)
                existing.updated_at = datetime.utcnow()
            else:
                # Insert new record
                symbol_master = SymbolMaster(**symbol_data)
                session.add(symbol_master)
            
            session.commit()
            self.logger.info(f"Symbol master data updated for {symbol_data['symbol']}")
            return True
            
        except SQLAlchemyError as e:
            session.rollback()
            self.logger.error(f"Error updating symbol master: {e}")
            return False
        finally:
            session.close()
    
    def log_data_fetch(self, log_data: dict):
        """Log data fetch operation"""
        session = self.get_session()
        try:
            fetch_log = DataFetchLog(**log_data)
            session.add(fetch_log)
            session.commit()
            return True
        except SQLAlchemyError as e:
            session.rollback()
            self.logger.error(f"Error logging data fetch: {e}")
            return False
        finally:
            session.close()
    
    def get_latest_data_timestamp(self, symbol: str):
        """Get latest data timestamp for a symbol"""
        session = self.get_session()
        try:
            result = session.query(TradingData.timestamp).filter_by(symbol=symbol).order_by(TradingData.timestamp.desc()).first()
            return result[0] if result else None
        except Exception as e:
            self.logger.error(f"Error getting latest timestamp: {e}")
            return None
        finally:
            session.close()
    
    def cleanup_old_data(self, days_to_keep: int = 30):
        """Clean up old data beyond specified days"""
        session = self.get_session()
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
            deleted = session.query(TradingData).filter(TradingData.timestamp < cutoff_date).delete()
            session.commit()
            self.logger.info(f"Cleaned up {deleted} old records")
            return deleted
        except Exception as e:
            session.rollback()
            self.logger.error(f"Error cleaning up data: {e}")
            return 0
        finally:
            session.close()
