#!/usr/bin/env python3
"""
Comprehensive End-to-End Test - Complete Trading Flow

This test creates a complete trading scenario:
1. Clear all DB1 and DB2 data
2. Insert 15-minute data with 4F+1R pattern for RELIANCE
3. DB1 detects pattern and generates BUY signal
4. DB2 receives signal and executes trade
5. DB2 monitors profit and executes SELL
6. All data visible in dashboard

Symbol: RELIANCE
Pattern: 4F+1R (Fall-Fall-Fall-Fall-Rise)
Investment: ₹100,000
Target: ₹800 profit
"""

import logging
import sqlite3
import time
from datetime import datetime, timedelta
from db1_signal_generator import get_db1_signal_generator
from db2_trade_executor import get_db2_trade_executor
from db1_db2_communicator import get_communicator

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def clear_all_data():
    """Clear all existing data from DB1 and DB2"""
    logger.info("🧹 CLEARING ALL EXISTING DATA")

    try:
        # Clear DB1 data
        conn1 = sqlite3.connect('Data/trading_data.db')
        cursor1 = conn1.cursor()

        # Clear tables that exist
        tables_to_clear = ['trading_data', 'trading_signals', 'active_patterns']
        for table in tables_to_clear:
            try:
                cursor1.execute(f"DELETE FROM {table}")
                logger.info(f"   ✅ Cleared {table}")
            except sqlite3.OperationalError as e:
                if "no such table" in str(e):
                    logger.info(f"   ⚠️ Table {table} doesn't exist (skipping)")
                else:
                    raise e

        conn1.commit()
        conn1.close()

        logger.info("✅ Cleared DB1 data")

        # Clear DB2 data
        conn2 = sqlite3.connect('Data/trading_operations.db')
        cursor2 = conn2.cursor()

        # Clear tables that exist
        db2_tables = ['db2_signals_received', 'trading_positions', 'db2_trading_data',
                      'db2_portfolio_summary', 'paper_trades']
        for table in db2_tables:
            try:
                cursor2.execute(f"DELETE FROM {table}")
                logger.info(f"   ✅ Cleared {table}")
            except sqlite3.OperationalError as e:
                if "no such table" in str(e):
                    logger.info(f"   ⚠️ Table {table} doesn't exist (skipping)")
                else:
                    raise e

        conn2.commit()
        conn2.close()

        logger.info("✅ Cleared DB2 data")

        return True

    except Exception as e:
        logger.error(f"❌ Error clearing data: {e}")
        return False

def create_4f_1r_pattern_data():
    """Create 15-minute data with 4F+1R pattern for RELIANCE"""
    logger.info("📊 CREATING 4F+1R PATTERN DATA FOR RELIANCE")
    
    try:
        conn = sqlite3.connect('Data/trading_data.db')
        cursor = conn.cursor()
        
        # Base time - start from 9:15 AM today
        base_time = datetime.now().replace(hour=9, minute=15, second=0, microsecond=0)
        
        # Create 4F+1R pattern data
        pattern_data = [
            # Before pattern (normal data)
            {'time_offset': -45, 'price': 2500.00, 'movement': 'START'},
            {'time_offset': -30, 'price': 2510.00, 'movement': 'R'},
            {'time_offset': -15, 'price': 2505.00, 'movement': 'F'},
            
            # 4F+1R Pattern starts here
            {'time_offset': 0,   'price': 2500.00, 'movement': 'F'},  # Fall 1
            {'time_offset': 15,  'price': 2485.00, 'movement': 'F'},  # Fall 2
            {'time_offset': 30,  'price': 2470.00, 'movement': 'F'},  # Fall 3
            {'time_offset': 45,  'price': 2455.00, 'movement': 'F'},  # Fall 4
            {'time_offset': 60,  'price': 2475.00, 'movement': 'R'},  # Rise 1 (BUY SIGNAL!)
            
            # After pattern (for monitoring)
            {'time_offset': 75,  'price': 2480.00, 'movement': 'R'},  # Rise 2 (RR confirmation)
            {'time_offset': 90,  'price': 2485.00, 'movement': 'R'},  # Rise 3
            {'time_offset': 105, 'price': 2490.00, 'movement': 'R'},  # Rise 4
            {'time_offset': 120, 'price': 2495.00, 'movement': 'R'},  # Rise 5
            {'time_offset': 135, 'price': 2500.00, 'movement': 'R'},  # Rise 6
            {'time_offset': 150, 'price': 2505.00, 'movement': 'R'},  # Rise 7
            {'time_offset': 165, 'price': 2510.00, 'movement': 'R'},  # Rise 8 (₹800+ profit reached)
            {'time_offset': 180, 'price': 2505.00, 'movement': 'F'},  # Fall 1 (FF pattern starts)
            {'time_offset': 195, 'price': 2500.00, 'movement': 'F'},  # Fall 2 (FF confirmed - SELL!)
        ]
        
        logger.info("📊 INSERTING PATTERN DATA:")
        logger.info("   4F+1R Pattern: F-F-F-F-R (BUY signal at ₹2475)")
        logger.info("   RR Confirmation: R-R (Execute BUY)")
        logger.info("   Profit Target: ₹2510 (₹800+ profit)")
        logger.info("   FF Confirmation: F-F (Execute SELL)")
        
        for i, data_point in enumerate(pattern_data):
            timestamp = base_time + timedelta(minutes=data_point['time_offset'])
            price = data_point['price']
            movement = data_point['movement']
            
            # Calculate previous close
            if i == 0:
                previous_close = None
                fr_calculated = True
            else:
                previous_close = pattern_data[i-1]['price']
                fr_calculated = True
            
            # Insert data
            cursor.execute('''
            INSERT INTO trading_data
            (symbol, token, exchange, timestamp, open_price, high_price, low_price, close_price,
             volume, interval_type, fr_movement, previous_close, fr_calculated, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                'RELIANCE',
                'RELIANCE_TOKEN',  # Token
                'NSE',             # Exchange
                timestamp.isoformat(),
                price - 2.5,  # Open slightly lower
                price + 2.5,  # High slightly higher
                price - 5.0,  # Low lower
                price,         # Close price
                100000,        # Volume
                '15min',       # Interval type
                movement,
                previous_close,
                fr_calculated,
                datetime.now().isoformat()
            ))
            
            logger.info(f"   {timestamp.strftime('%H:%M')}: ₹{price:.2f} ({movement})")
        
        conn.commit()
        conn.close()
        
        logger.info(f"✅ Inserted {len(pattern_data)} data points for RELIANCE")
        logger.info("🎯 4F+1R pattern ready for detection!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating pattern data: {e}")
        return False

def run_db1_pattern_detection():
    """Run DB1 pattern detection to generate BUY signal"""
    logger.info("🔍 RUNNING DB1 PATTERN DETECTION")
    
    try:
        db1_generator = get_db1_signal_generator()
        
        # Run pattern detection
        logger.info("🔄 DB1 analyzing RELIANCE data for 4F+1R pattern...")
        
        # Trigger pattern detection
        signals_generated = db1_generator._analyze_patterns_and_generate_buy_signals(['RELIANCE'])
        logger.info(f"🔄 DB1 generated {signals_generated} signals")
        
        # Check if signal was generated
        conn = sqlite3.connect('Data/trading_data.db')
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT symbol, signal_type, price, pattern_sequence, created_at
        FROM trading_signals
        WHERE symbol = 'RELIANCE'
        ORDER BY created_at DESC
        LIMIT 1
        ''')
        
        signal = cursor.fetchone()
        conn.close()
        
        if signal:
            symbol, signal_type, price, pattern_info, created_at = signal
            logger.info(f"✅ DB1 SIGNAL GENERATED:")
            logger.info(f"   Symbol: {symbol}")
            logger.info(f"   Type: {signal_type}")
            logger.info(f"   Price: ₹{price:.2f}")
            logger.info(f"   Pattern: {pattern_info}")
            logger.info(f"   Time: {created_at}")
            return True
        else:
            logger.warning("⚠️ No signal generated by DB1")
            return False
        
    except Exception as e:
        logger.error(f"❌ Error in DB1 pattern detection: {e}")
        return False

def run_db2_signal_processing():
    """Run DB2 to process the signal from DB1"""
    logger.info("📥 RUNNING DB2 SIGNAL PROCESSING")
    
    try:
        db2_executor = get_db2_trade_executor()
        
        # Process signals from DB1
        logger.info("🔄 DB2 checking for signals from DB1...")
        db2_executor.run_periodic_check()
        
        # Check signal tracking
        conn = sqlite3.connect('Data/trading_operations.db')
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT symbol, signal_type, signal_price, status, notes
        FROM db2_signals_received 
        WHERE symbol = 'RELIANCE' 
        ORDER BY received_time DESC 
        LIMIT 1
        ''')
        
        signal = cursor.fetchone()
        
        if signal:
            symbol, signal_type, signal_price, status, notes = signal
            logger.info(f"✅ DB2 SIGNAL RECEIVED:")
            logger.info(f"   Symbol: {symbol}")
            logger.info(f"   Type: {signal_type}")
            logger.info(f"   Price: ₹{signal_price:.2f}")
            logger.info(f"   Status: {status}")
            logger.info(f"   Notes: {notes or 'None'}")
            
            # Check if position was created
            cursor.execute('''
            SELECT symbol, buy_price, shares_quantity, investment, status
            FROM trading_positions 
            WHERE symbol = 'RELIANCE' 
            ORDER BY buy_time DESC 
            LIMIT 1
            ''')
            
            position = cursor.fetchone()
            
            if position:
                symbol, buy_price, shares, investment, status = position
                logger.info(f"✅ DB2 POSITION CREATED:")
                logger.info(f"   Symbol: {symbol}")
                logger.info(f"   Buy Price: ₹{buy_price:.2f}")
                logger.info(f"   Shares: {shares:,}")
                logger.info(f"   Investment: ₹{investment:,.2f}")
                logger.info(f"   Status: {status}")
            
            conn.close()
            return True
        else:
            logger.warning("⚠️ No signal received by DB2")
            conn.close()
            return False
        
    except Exception as e:
        logger.error(f"❌ Error in DB2 signal processing: {e}")
        return False

def simulate_profit_monitoring():
    """Simulate profit monitoring and SELL execution"""
    logger.info("💰 SIMULATING PROFIT MONITORING")
    
    try:
        db2_executor = get_db2_trade_executor()
        
        # Run profit monitoring
        logger.info("🔄 DB2 monitoring ₹800 profit target...")
        db2_executor._monitor_profit_targets_and_execute_sells()
        
        # Check portfolio summary
        portfolio = db2_executor.get_portfolio_summary()
        
        logger.info("📊 PORTFOLIO SUMMARY:")
        logger.info(f"   Total Investment: ₹{portfolio.get('total_investment', 0):,.2f}")
        logger.info(f"   Current Value: ₹{portfolio.get('total_current_value', 0):,.2f}")
        logger.info(f"   Current Profit: ₹{portfolio.get('total_profit', 0):,.2f}")
        logger.info(f"   Active Positions: {portfolio.get('active_positions', 0)}")
        logger.info(f"   Completed Trades: {portfolio.get('completed_trades', 0)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error in profit monitoring: {e}")
        return False

def display_dashboard_data():
    """Display all data that should be visible in dashboard"""
    logger.info("🌐 DASHBOARD DATA SUMMARY")
    logger.info("=" * 60)
    
    try:
        # DB1 Data
        logger.info("📊 DB1 DATA (Active Patterns):")
        conn1 = sqlite3.connect('Data/trading_data.db')
        cursor1 = conn1.cursor()
        
        cursor1.execute('''
        SELECT symbol, signal_type, price, pattern_info, created_at
        FROM trading_signals 
        WHERE symbol = 'RELIANCE'
        ORDER BY created_at DESC
        ''')
        
        signals = cursor1.fetchall()
        for signal in signals:
            symbol, signal_type, price, pattern_info, created_at = signal
            logger.info(f"   🎯 {symbol}: {signal_type} @ ₹{price:.2f} ({pattern_info})")
        
        conn1.close()
        
        # DB2 Data
        logger.info("\n📥 DB2 DATA (Paper Trading Brain):")
        conn2 = sqlite3.connect('Data/trading_operations.db')
        cursor2 = conn2.cursor()
        
        cursor2.execute('SELECT COUNT(*) FROM db2_signals_received')
        total_signals = cursor2.fetchone()[0]
        
        cursor2.execute("SELECT COUNT(*) FROM db2_signals_received WHERE status = 'CONFIRMING'")
        pending_buy = cursor2.fetchone()[0]
        
        cursor2.execute("SELECT COUNT(*) FROM trading_positions WHERE status = 'ACTIVE'")
        active_positions = cursor2.fetchone()[0]
        
        cursor2.execute('SELECT SUM(current_profit) FROM trading_positions WHERE status = "ACTIVE"')
        total_profit_result = cursor2.fetchone()
        total_profit = total_profit_result[0] if total_profit_result[0] else 0
        
        logger.info(f"   📊 Total Signals: {total_signals}")
        logger.info(f"   ⏳ Pending BUY: {pending_buy}")
        logger.info(f"   📈 Active Positions: {active_positions}")
        logger.info(f"   💰 Total Profit: ₹{total_profit:.2f}")
        
        # Paper Trading Records
        logger.info("\n💼 PAPER TRADING RECORDS:")
        cursor2.execute('''
        SELECT symbol, buy_price, shares_quantity, investment, current_profit, status, buy_time
        FROM trading_positions 
        WHERE symbol = 'RELIANCE'
        ORDER BY buy_time DESC
        ''')
        
        positions = cursor2.fetchall()
        for position in positions:
            symbol, buy_price, shares, investment, profit, status, buy_time = position
            logger.info(f"   📊 {symbol}: {shares:,} shares @ ₹{buy_price:.2f}")
            logger.info(f"      💰 Investment: ₹{investment:,.2f}")
            logger.info(f"      💵 Profit: ₹{profit:.2f}")
            logger.info(f"      📊 Status: {status}")
            logger.info(f"      🕐 Time: {buy_time}")
        
        conn2.close()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error displaying dashboard data: {e}")
        return False

def main():
    """Main comprehensive test function"""
    logger.info("🚀 COMPREHENSIVE END-TO-END TEST")
    logger.info("=" * 80)
    logger.info("Symbol: RELIANCE")
    logger.info("Pattern: 4F+1R (Fall-Fall-Fall-Fall-Rise)")
    logger.info("Investment: ₹100,000")
    logger.info("Target: ₹800 profit")
    logger.info("=" * 80)
    
    steps = [
        ("Clear All Data", clear_all_data),
        ("Create 4F+1R Pattern Data", create_4f_1r_pattern_data),
        ("Run DB1 Pattern Detection", run_db1_pattern_detection),
        ("Run DB2 Signal Processing", run_db2_signal_processing),
        ("Simulate Profit Monitoring", simulate_profit_monitoring),
        ("Display Dashboard Data", display_dashboard_data)
    ]
    
    passed_steps = 0
    total_steps = len(steps)
    
    for step_name, step_func in steps:
        logger.info(f"\n📋 STEP: {step_name}")
        logger.info("-" * 50)
        
        try:
            if step_func():
                logger.info(f"✅ PASSED: {step_name}")
                passed_steps += 1
            else:
                logger.error(f"❌ FAILED: {step_name}")
                break  # Stop on first failure
        except Exception as e:
            logger.error(f"❌ ERROR in {step_name}: {e}")
            break
    
    logger.info("\n" + "=" * 80)
    logger.info(f"📊 TEST RESULTS: {passed_steps}/{total_steps} steps completed")
    
    if passed_steps == total_steps:
        logger.info("🎉 COMPREHENSIVE TEST COMPLETED SUCCESSFULLY!")
        logger.info("✅ Complete trading flow from DB1 to DB2 working")
        logger.info("✅ All data visible in dashboard")
        logger.info("✅ RELIANCE trade executed with ₹100,000 investment")
        logger.info("🌐 CHECK DASHBOARD NOW - All data should be visible!")
        return True
    else:
        logger.error("❌ TEST FAILED - Check logs for issues")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
