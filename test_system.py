#!/usr/bin/env python3
"""
Test System - Quick test to verify all components work
"""
import sqlite3
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_database_connections():
    """Test database connections"""
    try:
        # Test DB1 connection
        conn1 = sqlite3.connect('Data/trading_data.db', timeout=30.0)
        cursor1 = conn1.cursor()
        cursor1.execute('SELECT COUNT(*) FROM trading_data')
        db1_count = cursor1.fetchone()[0]
        conn1.close()
        
        # Test DB2 connection
        conn2 = sqlite3.connect('Data/trading_operations.db', timeout=30.0)
        cursor2 = conn2.cursor()
        cursor2.execute('SELECT COUNT(*) FROM trading_positions')
        db2_count = cursor2.fetchone()[0]
        conn2.close()
        
        logger.info(f"✅ DB1 (trading_data.db): {db1_count} records")
        logger.info(f"✅ DB2 (trading_operations.db): {db2_count} positions")
        return True
        
    except Exception as e:
        logger.error(f"❌ Database test failed: {e}")
        return False

def test_trading_components():
    """Test trading system components"""
    try:
        # Test simple integration
        from simple_integration import simple_integration
        logger.info("✅ Simple Integration imported successfully")
        
        # Test DB1 engine
        from db1_engine import db1_engine
        logger.info("✅ DB1 Engine imported successfully")
        
        # Test DB2 engine
        from db2_engine import db2_engine
        logger.info("✅ DB2 Engine imported successfully")
        
        # Test market hours check
        market_hours = simple_integration.is_market_hours()
        logger.info(f"✅ Market hours check: {market_hours}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Trading components test failed: {e}")
        return False

def test_flask_app():
    """Test Flask app import"""
    try:
        import flask_app
        logger.info("✅ Flask app imported successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Flask app test failed: {e}")
        return False

def test_data_integrity():
    """Test data integrity"""
    try:
        from simple_integration import simple_integration
        integrity_status = simple_integration.check_data_integrity()
        
        logger.info("✅ Data Integrity Check:")
        logger.info(f"   DB1 Total Records: {integrity_status.get('db1_total_records', 0)}")
        logger.info(f"   DB1 F/R Calculated: {integrity_status.get('db1_fr_calculated', 0)}")
        logger.info(f"   DB2 Signals: {integrity_status.get('db2_signals_received', 0)}")
        logger.info(f"   DB2 Positions: {integrity_status.get('db2_trading_positions', 0)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Data integrity test failed: {e}")
        return False

def main():
    """Run all tests"""
    logger.info("🚀 TESTING COMPLETE TRADING SYSTEM")
    logger.info("=" * 50)
    
    tests = [
        ("Database Connections", test_database_connections),
        ("Trading Components", test_trading_components),
        ("Flask App", test_flask_app),
        ("Data Integrity", test_data_integrity)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n🔄 Testing {test_name}...")
        if test_func():
            passed += 1
            logger.info(f"✅ {test_name}: PASSED")
        else:
            logger.error(f"❌ {test_name}: FAILED")
    
    logger.info("\n" + "=" * 50)
    logger.info(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL TESTS PASSED - System is ready!")
        logger.info("\n🚀 To start the system:")
        logger.info("   python flask_app.py")
        logger.info("\n📊 Then visit: http://localhost:5000")
    else:
        logger.error("❌ Some tests failed - Please check the errors above")

if __name__ == "__main__":
    main()
