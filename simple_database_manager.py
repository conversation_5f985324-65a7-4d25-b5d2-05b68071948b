#!/usr/bin/env python3
"""
Simple Database Manager

This replaces the complex ORM-based database_setup.py with simple, direct SQL operations.
"""

import sqlite3
import logging
from datetime import datetime
from typing import List, Dict

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleDatabaseManager:
    """Simple database manager using direct SQL"""

    def __init__(self):
        self.logger = logger
        self.db1_path = 'Data/trading_data.db'
        self.db2_path = 'Data/trading_operations.db'

    def connect(self):
        """Legacy method for compatibility - no-op since we use direct connections"""
        self.logger.debug("📡 Database connection (using direct SQLite connections)")
        return True

    def create_tables(self):
        """Legacy method for compatibility - tables already exist from our schema"""
        self.logger.debug("📊 Database tables (already created)")
        return True
    
    def insert_trading_data(self, data_records: List[Dict]) -> bool:
        """Insert trading data with SIMPLE F/R calculations"""
        try:
            self.logger.info(f"📊 INSERTING {len(data_records)} RECORDS WITH SIMPLE F/R CALCULATION")
            
            conn = sqlite3.connect(self.db1_path)
            cursor = conn.cursor()
            
            inserted_count = 0
            symbols_with_new_data = set()
            
            for record in data_records:
                symbol = record['symbol']
                timestamp = record['timestamp']
                close_price = record['close_price']
                
                # Check if record already exists
                cursor.execute('''
                SELECT id FROM trading_data 
                WHERE symbol = ? AND timestamp = ?
                ''', (symbol, timestamp))
                
                if cursor.fetchone():
                    self.logger.debug(f"⚠️ DUPLICATE SKIPPED: {symbol} at {timestamp}")
                    continue
                
                # Get previous close price for F/R calculation
                cursor.execute('''
                SELECT close_price FROM trading_data 
                WHERE symbol = ? AND timestamp < ?
                ORDER BY timestamp DESC 
                LIMIT 1
                ''', (symbol, timestamp))
                
                previous_result = cursor.fetchone()
                
                if previous_result:
                    previous_close = previous_result[0]
                    
                    # Calculate F/R movement
                    if close_price > previous_close:
                        fr_movement = 'R'
                    elif close_price < previous_close:
                        fr_movement = 'F'
                    else:
                        fr_movement = 'N'
                    
                    self.logger.info(f"📊 {symbol}: ₹{previous_close:.2f} → ₹{close_price:.2f} = {fr_movement}")
                else:
                    # First record for this symbol
                    previous_close = None
                    fr_movement = 'START'
                    self.logger.info(f"📊 {symbol}: First record = START")
                
                # Insert record with F/R calculation
                cursor.execute('''
                INSERT INTO trading_data 
                (symbol, token, exchange, timestamp, open_price, high_price, low_price, close_price, volume, fr_movement, previous_close)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    record['symbol'],
                    record.get('token'),
                    record.get('exchange'),
                    record['timestamp'],
                    record['open_price'],
                    record['high_price'],
                    record['low_price'],
                    record['close_price'],
                    record.get('volume'),
                    fr_movement,
                    previous_close
                ))
                
                inserted_count += 1
                symbols_with_new_data.add(symbol)
            
            conn.commit()
            conn.close()
            
            if inserted_count > 0:
                self.logger.info(f"✅ INSERTED {inserted_count} RECORDS WITH CORRECT F/R CALCULATION")
                
                # Check patterns for each symbol with new data
                for symbol in symbols_with_new_data:
                    self._check_pattern_for_symbol(symbol)
            
            return True

        except Exception as e:
            self.logger.error(f"Error inserting trading data: {e}")
            return False
    
    def _check_pattern_for_symbol(self, symbol):
        """Check if symbol has 4F+1R pattern using simple SQL"""
        self.logger.info(f"🔍 CHECKING PATTERN FOR {symbol}")
        
        conn = sqlite3.connect(self.db1_path)
        cursor = conn.cursor()
        
        # Get last 6 records for this symbol (6 points = 5 movements = 4F+1R)
        cursor.execute('''
        SELECT timestamp, close_price, fr_movement, previous_close
        FROM trading_data 
        WHERE symbol = ?
        ORDER BY timestamp DESC
        LIMIT 6
        ''', (symbol,))
        
        records = cursor.fetchall()
        conn.close()
        
        if len(records) < 6:
            self.logger.info(f"📊 {symbol}: Only {len(records)}/6 intervals available")
            return False
        
        # Reverse to get chronological order
        records = list(reversed(records))
        
        # Extract movements (skip first record which is START)
        movements = []
        for i in range(1, len(records)):
            timestamp, close_price, fr_movement, previous_close = records[i]
            movements.append(fr_movement)
            self.logger.info(f"   {i}. {timestamp}: ₹{previous_close:.2f} → ₹{close_price:.2f} = {fr_movement}")
        
        self.logger.info(f"📊 {symbol} PATTERN: {' → '.join(movements)}")
        
        # Check for 4F+1R pattern
        expected_pattern = ['F', 'F', 'F', 'F', 'R']
        
        if movements == expected_pattern:
            self.logger.info(f"🎉 {symbol}: 4F+1R PATTERN DETECTED!")
            
            # Calculate drop percentage
            start_price = records[0][1]  # First record close price
            lowest_price = min(record[1] for record in records[1:5])  # Lowest in the 4 falls
            drop_percentage = ((start_price - lowest_price) / start_price) * 100
            
            self.logger.info(f"📉 DROP: ₹{start_price:.2f} → ₹{lowest_price:.2f} = {drop_percentage:.2f}%")
            
            if drop_percentage >= 0.5:
                self.logger.info(f"✅ DROP SUFFICIENT: {drop_percentage:.2f}% ≥ 0.5%")
                
                # Generate BUY signal
                current_price = records[-1][1]  # Last record close price
                self._generate_buy_signal(symbol, current_price, movements, drop_percentage)
                return True
            else:
                self.logger.info(f"❌ DROP INSUFFICIENT: {drop_percentage:.2f}% < 0.5%")
        else:
            self.logger.info(f"❌ {symbol}: Pattern mismatch - Expected: {' → '.join(expected_pattern)}")
        
        return False
    
    def _generate_buy_signal(self, symbol, price, pattern, drop_percentage):
        """Generate BUY signal and save to database"""
        self.logger.info(f"🎯 GENERATING BUY SIGNAL: {symbol} @ ₹{price:.2f}")
        
        conn = sqlite3.connect(self.db1_path)
        cursor = conn.cursor()
        
        # Insert BUY signal
        cursor.execute('''
        INSERT INTO trading_signals
        (symbol, signal_type, signal_price, price, pattern_sequence, drop_percentage)
        VALUES (?, ?, ?, ?, ?, ?)
        ''', (symbol, 'BUY', price, price, ''.join(pattern), drop_percentage))
        
        conn.commit()
        conn.close()
        
        self.logger.info(f"💾 BUY SIGNAL SAVED: {symbol} @ ₹{price:.2f}")
        
        # Send signal to DB2
        self._send_signal_to_db2(symbol, 'BUY', price, ''.join(pattern))
    
    def _send_signal_to_db2(self, symbol, signal_type, price, pattern):
        """Send signal to DB2 for confirmation"""
        self.logger.info(f"📡 SENDING SIGNAL TO DB2: {signal_type} {symbol} @ ₹{price:.2f}")
        
        conn = sqlite3.connect(self.db2_path)
        cursor = conn.cursor()
        
        # Insert signal in DB2
        cursor.execute('''
        INSERT INTO db2_signals_received 
        (symbol, signal_type, signal_price)
        VALUES (?, ?, ?)
        ''', (symbol, signal_type, price))
        
        conn.commit()
        conn.close()
        
        self.logger.info(f"✅ SIGNAL SENT TO DB2: {signal_type} {symbol}")

# Create global instance to replace the old DatabaseManager
simple_db_manager = SimpleDatabaseManager()

def get_simple_database_manager():
    """Get the simple database manager instance"""
    return simple_db_manager
